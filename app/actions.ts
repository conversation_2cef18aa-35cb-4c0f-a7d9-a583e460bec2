'use server';

import { userInfo, } from '@/supabase/verifications/user-info';
import { UNAUTHORIZED, } from '@/utils/constants';
import { createClientAdmin, } from '@/utils/supabase/server';
import { BaseRank, Profile, Rank, Schedule, School, SchoolSettings, Service, Sport, SportProfile, } from '@/utils/constants/types';
import { PostgrestMaybeSingleResponse, PostgrestResponse, } from '@supabase/supabase-js';
import webpush from 'web-push';
import { addLeadingZero, getHoursMinusThree, } from '@/lib/utils';

type NotificationData = {
  title: string,
  body: string,
  url?: string,
}

type ErrorCodes = {
  message: string,
  status: 400 | 401
}

type GetSchoolServiceResponse = {
  data: Service[],
  status: 200
} | ErrorCodes

type GetSchoolSportsResponse = {
  data: Sport[]
  status: 200
} | ErrorCodes

type GetSchoolSettingsResponse = {
  data: SchoolSettings,
  status: 200
} | ErrorCodes

type GetSportRanksResponse = {
  data: Rank[],
  status: 200
} | ErrorCodes

type GetSportBaseRanksResponse = {
  data: BaseRank[],
  status: 200
} | ErrorCodes


type GetProfileResponse = {
  data: Profile,
  status: 200
} | ErrorCodes

type UpdateProfileResponse = {
  data: Profile,
  status: 200
} | ErrorCodes

webpush.setVapidDetails(
  'mailto:<EMAIL>',
  process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!,
  process.env.VAPID_PRIVATE_KEY!
);

export async function subscribeUser(sub: PushSubscription) {
  const user = await userInfo();
  console.log('🚀 ~ subscribeUser ~ user:', user);

  if (!user) {
    return {
      message: UNAUTHORIZED,
      status: 401,
    };
  }

  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
    .from('user_pwa')
    .insert({
      sub: JSON.stringify(sub),
      schoolId: user.user_metadata.schoolId,
      profileId: user.id,
    })
    .select();
  console.log('🚀 ~ subscribeUser ~ error:', error);

  if (error) return { status: 400, message: 'Falha ao registrar notificação do usuário.', };
  return { status: 200, };
}

export async function unsubscribeUser() {
  const user = await userInfo();

  if (!user || user.user_metadata.role !== 'admin') {
    return {
      message: UNAUTHORIZED,
      status: 401,
    };
  }

  const supabaseAdmin = createClientAdmin();

	try {
    const { error, } = await supabaseAdmin
      .from('user_pwa')
      .delete()
      .match({ profileId: user.id, })
      .select();
    if (error) return { status: 400, message: 'Falha ao remover registro de notificação do usuário.', };

		return { status: 200, };
	} catch (err) {
		console.error(err);
		return { status: 500, message: 'Falha ao remover registro de notificação do usuário.', };
	}
}

export async function sendPushNotificationToUser(profileId: string, data: NotificationData) {
  const supabase = createClientAdmin();

  const { data: pwaSub, } = await supabase
    .from('user_pwa')
    .select()
    .match({ profileId, })
    .order('createdAt', { ascending: false, })
    .limit(1)
    .single();
    console.log('🚀 ~ sendPushNotificationToUser ~ pwaSub:', pwaSub);

  if (!pwaSub) {
    return { status: 400, message: 'Sem registro PWA', };
  }

  try {
    const respone = await webpush.sendNotification(
      JSON.parse(pwaSub.sub),
      JSON.stringify({
        title: data.title,
        body: data.body,
        url: data.url,
      })
    );
    console.log('🚀 ~ sendPushNotificationToUser ~ respone:', respone);
    return { status: 200, };
  } catch (error) {
    return { status: 500, message: 'Failed to send notification', };
  }
}

export async function sendNotification(sub: PushSubscription, message: string) {
  try {
    await webpush.sendNotification(
      sub,
      JSON.stringify({
        title: 'Test Notification',
        body: message,
      })
    );
    return { status: 200, };
  } catch (error) {
    return { status: 500, message: 'Failed to send notification', };
  }
}

export const getAllSchoolServices = async (): Promise<GetSchoolServiceResponse> => {
  const user = await userInfo();

  if (!user) return { message: UNAUTHORIZED, status: 401, };

  const supabaseAdmin = createClientAdmin();
  const { data, }: PostgrestResponse<Service>  = await supabaseAdmin
    .from('service')
    .select()
    .match({ schoolId: user.user_metadata.schoolId, });

  if (!data) return { message: 'Erro ao buscar serviços.', status: 400, };

  if (data.length === 0) {
    return { message: 'Sem serviços cadastrados.', status: 400, };
  }

  return { data, status: 200, };
};

export const getAllSchoolSports = async (): Promise<GetSchoolSportsResponse> => {
  const user = await userInfo();

  if (!user) return { message: UNAUTHORIZED, status: 400, };

  const supabaseAdmin = createClientAdmin();
  const { data, } = await supabaseAdmin
    .from('sport_school')
    .select('sport(*)')
    .match({ schoolId: user.user_metadata.schoolId, })
    .returns<{ sport: Sport }[]>();

  if (!data) return { message: 'Erro ao buscar esportes.', status: 400, };

  if (!data.length) return { message: 'Escola não tem esporte cadastrado.', status: 400, };

  return { data: data.map(schoolSport => schoolSport.sport), status: 200, };
};

export const getSchoolSettings = async (): Promise<GetSchoolSettingsResponse> => {
  const user = await userInfo();
  const supabaseAdmin = createClientAdmin();
    const { data, }: PostgrestMaybeSingleResponse<SchoolSettings> = await supabaseAdmin
    .from('school_settings')
    .select()
    .match({ schoolId: user?.user_metadata.schoolId, })
    .maybeSingle();

  if (!data) {
    return {
      message: 'Erro ao buscar preferências da escola.',
      status: 400,
    };
  }

  return { data, status: 200, };
};

export const getStudents = async (): Promise<{ data: Profile[] | null, status: 200 } | { message: string, status: 400 | 401 }> => {
  const user = await userInfo();

  if (!user) return {
    message: UNAUTHORIZED,
    status: 401,
  };

  const supabase = createClientAdmin();

  const { data: profiles, } = await supabase
    .from('profile')
    .select()
    .match({ schoolId: user.user_metadata.schoolId, type: 'student', });
  console.log('🚀 ~ getStudents ~ profiles:', profiles);

  const { data: leads, } = await supabase
    .from('lead')
    .select()
    .match({ schoolId: user.user_metadata.schoolId, });
  console.log('🚀 ~ getStudents ~ leads:', leads);

  const students = [...(profiles || []), ...(leads || []),]
  .sort((a, b) => a.name.localeCompare(b.name));
  console.log('🚀 ~ getStudents ~ students:', students);

  if (students.length > 0) return {
    data: students,
    status: 200,
  };

  return {
    message: 'Não foi possível buscar os estudantes.',
    status: 400,
  };
};

export const getRanksBySport = async ({ sportId, }: { sportId: string }): Promise<GetSportRanksResponse> => {
  const supabase = createClientAdmin();
  const { data, }: PostgrestResponse<Rank> = await supabase
    .from('rank')
    .select()
    .order('name', { ascending: true, })
    .match({ sportId, });

  if (!data) return { message: 'Erro ao buscar esportes.', status: 400, };

  if (!data.length) return { data: [], status: 200, };

  return { data, status: 200, };
};

export const getBaseRanksBySport = async ({ sportId, }: { sportId: string }): Promise<GetSportBaseRanksResponse> => {
  const supabase = createClientAdmin();
  const { data, }: PostgrestResponse<Rank> = await supabase
    .from('base_rank')
    .select()
    .order('name', { ascending: true, })
    .match({ sportId, });

  if (!data) return { message: 'Erro ao buscar esportes.', status: 400, };

  if (!data.length) return { data: [], status: 200, };

  return { data, status: 200, };
};

export const updateProfile = async ({ profileId, formData, }: { profileId: string, formData: Partial<Profile> }): Promise<UpdateProfileResponse> => {
  const user = await userInfo();

  if (!user) return { message: UNAUTHORIZED, status: 401, };

  const supabaseAdmin = createClientAdmin();
  const { data, }: PostgrestMaybeSingleResponse<Profile> = await supabaseAdmin
    .from('profile')
    .select()
    .match({ id: profileId, })
    .maybeSingle();

  if (!data) return { message: 'Erro ao buscar usuário.', status: 400, };

  const isAdminFromStudentSchool = user.user_metadata.role === 'admin' && user.user_metadata.schoolId === data.schoolId;
  const isStudentHimself = user.id === profileId;

  if (!isAdminFromStudentSchool && !isStudentHimself) return { message: UNAUTHORIZED, status: 401, };

  const { data: updateProfileResponse, }: PostgrestMaybeSingleResponse<Profile> = await supabaseAdmin
    .from('profile')
    .update(formData)
    .match({ id: profileId, })
    .maybeSingle();

  if (!updateProfileResponse) return { message: 'Erro ao atualizar usuário.', status: 400, };

  return { data: updateProfileResponse, status: 200, };
};

export const getProfile = async (id?: string): Promise<GetProfileResponse> => {
  const user = await userInfo();

  if (!user) return {
    message: UNAUTHORIZED,
    status: 401,
  };

  const supabaseAdmin = createClientAdmin();
  const { data, } = await supabaseAdmin
    .from('profile')
    .select()
    .match({ id: id || user.id, })
    .maybeSingle();

  if (!data) return {
    message: UNAUTHORIZED,
    status: 401,
  };

  return {
    data,
    status: 200,
  };
};

export const getProfileSports = async (): Promise<{ data: Sport[], status: 200 } | { message: string, status: 400 }> => {
  const user = await userInfo();

  const supabaseAdmin = createClientAdmin();
  const { data, } = await supabaseAdmin
    .from('sport_profile')
    .select('sport(*)')
    .match({ profileId: user.id, })
    .returns<SportProfile & { sport: Sport }[]>();

  if (!data || !data.length || !data[0].sport) return {
    message: 'Não foi encontrado esportes.',
    status: 400,
  };

  return { data: data.map(sportProfile => sportProfile.sport), status: 200, };
};

export const getSchoolByDomain = async ({ domain, }: { domain: string }): Promise<{ data: School, status: 200 } | { message: string, status: 400 }> => {
  const supabaseAdmin = createClientAdmin();
  const { data, } = await supabaseAdmin
    .from('school')
    .select()
    .match({ domain, })
    .maybeSingle();

  if (!data) return {
    message: 'Não foi possível encontrar a escola.',
    status: 400,
  };
  return { data, status: 200, };
};

export const getServicesBySport = async ({ sportId, }: { sportId: string }): Promise<{ data: Service[], status: 200 } | { message: string, status: 401 | 400}> => {
  const profileReponse = await getProfile();

  if (profileReponse.status !== 200) return {
    message: UNAUTHORIZED,
    status: 401,
  };

  const profile = profileReponse.data;

  const supabaseAdmin = createClientAdmin();
  const { data, } = await supabaseAdmin
  .from('service')
  .select()
  .match({
    schoolId: profile.schoolId,
    sportId,
  });
  if (!data) return {
    message: 'Erro ao buscar serviços.',
    status: 400,
  };
  return { data, status: 200, };
};

export const logError = async (error) => {
  console.log('🚀 ~ logError ~ error:', error);
  const supabaseAdmin = createClientAdmin();
  await supabaseAdmin.from('log').insert({ value: JSON.stringify(error), });
};

type GetScheduleByServiceAndDayProps = { serviceId: string, dayOfWeek: number };
export const getScheduleByServiceAndDay = async ({ serviceId, dayOfWeek, }: GetScheduleByServiceAndDayProps): Promise<{ data: Schedule, status: 200 } | { message: string, status: 400 }> => {
  const supabase = createClientAdmin();
  let match = {
    serviceId,
    active: true,
    number: dayOfWeek,
  };
  console.log('🚀 ~ getScheduleByServiceAndDay ~ match:', match);

  const hourNow = addLeadingZero(getHoursMinusThree().toString()) + ':' + addLeadingZero(new Date().getMinutes().toString());
  console.log('HOUR NOW:', hourNow);
  const { data: schedule, } = await supabase
    .from('schedule')
    .select()
    .match(match)
    .lt('hour', hourNow)
    .order('hour', { ascending: false, })
    .returns<Schedule[]>();
    console.log('🚀 ~ getScheduleByServiceAndDay ~ schedule:', schedule);

    if (schedule) {
      if (schedule.length === 0) return { message: 'Sem aula até o momento.', status: 400, };
      console.log('🚀 ~ getScheduleByServiceAndDay ~ schedule:', schedule);
    return { data: schedule[0], status: 200, };
  } else {
    return { message: 'Erro ao buscar agenda.', status: 400, };
  }
};
