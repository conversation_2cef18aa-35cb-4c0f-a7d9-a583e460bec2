'use client';

import {
  But<PERSON>,
  Card,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from '@/components/index';

interface SubscribePromptProps {
  onSubscribe: () => void
  onCancel: () => void
  onDontAsk: () => void
}

const SubscribePrompt = ({ onSubscribe, onCancel, onDontAsk, }: SubscribePromptProps) => {
  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center pb-4 sm:items-center sm:pb-0">
      <div className="fixed inset-0 bg-background/80 backdrop-blur-sm" />
      <Card className="z-50 w-full max-w-lg">
        <CardHeader>
          <CardTitle>Receber notificações?</CardTitle>
          <CardDescription>
            Receba notificações de mensalidades, graduação, e muito mais.
          </CardDescription>
        </CardHeader>
        <CardFooter className="flex justify-end gap-2">
          <Button onClick={onSubscribe} className='w-full'>
            Sim
          </Button>
          <Button variant="destructive" onClick={onCancel}>
            Não
          </Button>
          <Button variant="destructive" onClick={onDontAsk}>
            Não perguntar de novo.
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default SubscribePrompt;