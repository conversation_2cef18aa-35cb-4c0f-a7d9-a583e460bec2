'use client';
import { Button, } from '@/components/ui/shard/button';
import Link from 'next/link';
import { useEffect, } from 'react';
import { logError, } from './actions';

 // Error boundaries must be Client Components

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  console.log(error);

  useEffect(() => {
    (async () => {
      await logError(error);
    })();
  }, [error,]);

  return (
    // global-error must include html and body tags
    <html className='w-full h-screen'>
      <body className='w-full h-full flex flex-col items-center justify-center gap-2'>
        <h2><PERSON><PERSON><PERSON><PERSON>, algum erro desconhecido aconteceu.</h2>
        <Link href='/cursos'><Button>Página inicial</Button></Link>
      </body>
    </html>
  );
}