import { generateRandomString, } from '@/lib/utils';
import { MISSING_PARAMS, UNAUTHORIZED, UNKNOWN_ERROR, } from '@/utils/constants';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { NextRequest, NextResponse, } from 'next/server';

export async function GET (request: NextRequest) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user) return NextResponse.json({ message: UNAUTHORIZED, }, { status: 400, });

  const searchParams = request.nextUrl.searchParams;

  const supabaseAdmin = createClientAdmin();
  const { data, error, } = await supabaseAdmin
  .from('face')
  .select()
  .match({ email: searchParams.get('email') || user.email, })
  .maybeSingle();

  if (!data && !error) return NextResponse.json(null);
  if (!data && error) return NextResponse.json({ message: 'Não existe imagem registrada.', }, { status: 400, });

  return NextResponse.json(data);
}

export async function POST (request: NextRequest) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user) return NextResponse.json({ message: UNAUTHORIZED, }, { status: 400, });

  const formData = await request.formData();
  const domain = formData.get('domain');
  const image = formData.get('image');
  const folder = formData.get('folder');
  if (!domain || !image || !folder) return NextResponse.json({ message: MISSING_PARAMS, }, { status: 400, });

  const supabaseAdmin = createClientAdmin();
  const imgName = generateRandomString();
  const { data, } = await supabaseAdmin.storage.from(`${domain}/${folder}`).upload(imgName as string, image);

  if (data) return NextResponse.json(data);

  return NextResponse.json({ message: UNKNOWN_ERROR, }, { status: 500, });
}