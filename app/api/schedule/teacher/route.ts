import { MISSING_PARAMS, UNAUTHORIZED, } from '@/utils/constants';
import { Profile, Schedule, TeacherSchedule, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { getDay, parseISO, } from 'date-fns';
import { NextRequest, NextResponse, } from 'next/server';

type RequestType = {
  days: string[],
  limit: number,
  serviceId: string,
  schedules: Schedule[],
  teacherId?: string
}

export async function POST(request: NextRequest) {
  const { days, limit, schedules, serviceId, teacherId,}: RequestType = await request.json();
  if (!serviceId || !limit || !days || !schedules || !schedules.length) {
    return NextResponse.json({ message: MISSING_PARAMS, }, { status: 400, });
  }
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user) return NextResponse.json({ message: '<PERSON>u<PERSON>rio não encontrado.', }, { status: 400, });
  if (user.user_metadata.role === 'student') return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });

  const supabaseAdmin = createClientAdmin();

  const promises = days.map(async day => {
    const inPromises = schedules
      .filter(sch => getDay(parseISO(day)) === sch.number)
      .map(async schedule => {
        console.log('🚀 ~ POST ~ schedule:', schedule);
        const { data: t, error, } = await supabaseAdmin
          .from('teacher_schedule')
          .insert(Array.from({ length: limit, }, () => ({
            day,
            profileId: teacherId || user.id,
            scheduleId: schedule.id,
            serviceId: serviceId,
          })))
          .select();
          console.log('🚀 ~ POST ~ error:', error);
        console.log('🚀 ~ POST ~ t:', t);
        if (error) {
          console.log('🚀 ~ POST ~ error:', error);
          await supabase.from('error').insert({ 'scheduleerror': error, });
          return NextResponse.json({ message: 'Erro ao salvar escala.', }, { status: 400, });
        }
    });
    return await Promise.all(inPromises);
  });

  await Promise.all(promises);

  return NextResponse.json({ message: 'Escala salva.', });
}

export async function GET(request: Request) {
  const searchParams = request.nextUrl.searchParams;
  const serviceId = searchParams.get('serviceId');
  const day = searchParams.get('day');
  const scheduleId = searchParams.get('scheduleId');
  if (!serviceId) {
    return Response.json({ message: 'Informações insuficientes.', }, { status:400, });
  }
  const supabase = createClientAdmin();
  const { data: teacherSchedules, } = await supabase
    .from('teacher_schedule')
    .select('*, profile(*)')
    .match({
      serviceId,
      day,
      scheduleId,
    })
    .returns<(TeacherSchedule & { profile: Profile })[] | null>();

  if (!teacherSchedules) return [];

  const availableTeachers: Profile[] = [];

  const promises = teacherSchedules?.map(async teacherSchedule => {
    const { data: bookings, } = await supabase
    .from('booking')
    .select()
    .match({
      serviceId,
      day,
      scheduleId,
      teacherId: teacherSchedule.profile.id,
    });

    if (bookings && (!teacherSchedule.limit || teacherSchedule.limit > bookings.length)) {
      availableTeachers.push(teacherSchedule.profile);
    }
  });
  await Promise.all(promises);

  if (availableTeachers) {
    return Response.json(availableTeachers);
  } else {
    return Response.json({ message: 'Erro ao buscar agenda.', }, { status: 400, });
  }
}
