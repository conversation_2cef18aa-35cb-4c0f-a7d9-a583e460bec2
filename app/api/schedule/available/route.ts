import { compareAndFilter, groupBookings, groupTeachersSchedule, } from '@/lib/utils';
import { Schedule, } from '@/utils/constants/types';
import { createClientAdmin, } from '@/utils/supabase/server';
import { format, } from 'date-fns';
import { NextResponse, } from 'next/server';

type Bookings = {
  day: string,
  schedule: Schedule,
}

export async function GET(request: Request) {
  const searchParams = request.nextUrl.searchParams;
  const serviceId = searchParams.get('serviceId');
  const supabase = createClientAdmin();

  // TODO pegar no máximo 30 dias
  const { data: teachersSchedule, error, } = await supabase
    .rpc('get_unique_teacher_schedules_by_service', { service: serviceId, });
  console.log('🚀 ~ GET ~ error:', error);

  if (!teachersSchedule) return NextResponse
    .json(
      { message: 'Professores não encontrados.', },
      {status: 400,},
  );

  const { data: bookings,  } = await supabase.
    from('booking')
    .select('day, schedule(*)')
    .order('day', { ascending: true, })
    .eq('serviceId', serviceId)
    .eq('status', 'approved')
    .returns<Bookings[] | null>();

  const groupedTeachersSchedule = groupTeachersSchedule(teachersSchedule);
  const groupedBookings = groupBookings(bookings || []);
  console.log('🚀 ~ GET ~ groupedBookings:', groupedBookings);
  console.log('🚀 ~ GET ~ groupedTeachersSchedule:', groupedTeachersSchedule);

  let availableSchedules =  compareAndFilter(groupedTeachersSchedule, groupedBookings);
  return NextResponse.json(availableSchedules);
}