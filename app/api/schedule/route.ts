import { Schedule, } from '@/utils/constants/types';
import { createClientAdmin, } from '@/utils/supabase/server';

export async function GET(request: Request) {
  const searchParams = request.nextUrl.searchParams;
  const serviceId = searchParams.get('serviceId');
  const dayOfWeek = searchParams.get('dayOfWeek');
  if (!serviceId) {
    return Response.json({ message: 'Informações insuficientes.', }, { status:400, });
  }
  const supabase = createClientAdmin();
  let match = {
    serviceId,
    active: true,
  };

  if (dayOfWeek) {
    match = {
      ...match,
      number: dayOfWeek,
    };
  }

  const { data: schedule, } = await supabase
    .from('schedule')
    .select()
    .match(match)
    .order('hour')
    .returns<Schedule[]>();
  if (schedule) {
    return Response.json(schedule);
  } else {
    return Response.json({ message: 'Erro ao buscar agenda.', }, { status: 400, });
  }
}
