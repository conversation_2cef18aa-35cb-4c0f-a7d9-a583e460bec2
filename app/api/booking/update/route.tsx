import dictionary from '@/lib/dictionary';
import { calculateTimeDifferenceInHours, } from '@/lib/utils';
import { MISSING_PARAMS, } from '@/utils/constants';
import { Booking, Pack, Schedule, Service, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestMaybeSingleResponse, } from '@supabase/supabase-js';
import { addHours, addMinutes, } from 'date-fns';
import { NextRequest, NextResponse, } from 'next/server';

type RequestType = {
  day: string,
  equipmentsIds: string[]
  serviceId: string,
  status: 'pending' | 'approved' | 'canceled',
  schedule: Schedule,
  schoolId: string,
  teacherId: string
}

export async function PATCH(request: NextRequest) {
  const { code, day, equipmentsIds, serviceId, schedule, status, teacherId, }: RequestType & { code: string } = await request.json();
  if (!code || !serviceId || !status || !day || !schedule || !teacherId) {
    return NextResponse.json({ message: MISSING_PARAMS, }, { status: 400, });
  }
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user) return NextResponse.json({ message: 'Usuario não encontrado', }, { status: 404, });

  const supabaseAdmin = createClientAdmin();

  const { data: currentBooking, }: { data: Booking & { schedule: Schedule } | null } = await supabaseAdmin
    .from('booking')
    .select('*, schedule(*)')
    .match({ code, })
    .maybeSingle();

  if (!currentBooking) return NextResponse.json({ message: 'Agendamento não encontrado', }, { status: 404, });

  const { data: service, }: PostgrestMaybeSingleResponse<Service> = await supabaseAdmin
    .from('service')
    .select()
    .match({ id: currentBooking.serviceId, })
    .maybeSingle();

  const hoursAndMinutes = currentBooking.schedule.hour.split(':');
  let date = addHours(currentBooking.day, parseInt(hoursAndMinutes[0]));
  date = addMinutes(date, parseInt(hoursAndMinutes[1]));
  const timeDifference = calculateTimeDifferenceInHours(date);

  if (service && service.policy > Math.abs(timeDifference)) return NextResponse.json({ message: 'Agendamento fora da política de cancelamento.', }, { status: 400, });

  const { data: booking, error, } = await supabaseAdmin
    .from('booking')
    .update({
      day,
      scheduleId: schedule.id,
      status,
      teacherId,
    })
    .in('status', ['approved', 'pending',])
    .eq('code', code)
    .eq('userId', user.id)
    .select()
    .maybeSingle();

  if (booking === null && error) {
    return NextResponse.json({ message: dictionary[error.message] || 'Erro ao salvar agendamento.', }, { status: 400, });
  }

  if (equipmentsIds.length) {
    await supabaseAdmin.from('booking_equipment').delete().eq('bookingId', booking.id);
    const equipmentsPromises = equipmentsIds.map(async equipmentId => {
      return await supabaseAdmin.from('booking_equipment').insert({
        bookingId: booking.id,
        equipmentId,
      });
    });
    await Promise.all(equipmentsPromises);
  }

  return NextResponse.json(booking);
}