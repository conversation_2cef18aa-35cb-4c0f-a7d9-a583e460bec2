import dictionary from '@/lib/dictionary';
import { generateRandomString, } from '@/lib/utils';
import { MISSING_PARAMS, } from '@/utils/constants';
import { Pack, PackProfile, Schedule, } from '@/utils/constants/types';
import { accountedForBookingStatus, } from '@/utils/supabase/constants';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestMaybeSingleResponse, } from '@supabase/supabase-js';
import { NextRequest, NextResponse, } from 'next/server';

type RequestType = {
  day: string,
  equipments: { id: string, key?: string }[]
  packProfileId: string,
  serviceId: string,
  status: 'pending' | 'approved' | 'canceled',
  schedule: Schedule,
  teacherId: string
}

export async function POST(request: NextRequest) {
  const { day, equipments, packProfileId, schedule, serviceId, status, teacherId, }: RequestType = await request.json();
  if (!packProfileId || !serviceId || !status || !day || !schedule || !teacherId) {
    return NextResponse.json({ message: MISSING_PARAMS, }, { status: 400, });
  }
  console.log('🚀 ~ POST ~ packId:', packProfileId);
  console.log('🚀 ~ POST ~ teacherId:', teacherId);
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user) return NextResponse.json({ message: 'Usuario nao encontrado', }, { status: 401, });

  const supabaseAdmin = createClientAdmin();

  const { data: packProfile, }: PostgrestMaybeSingleResponse<PackProfile & { pack: Pack }> = await supabaseAdmin
    .from('pack_profile')
    .select('*, pack(id, use)')
    .in('paymentStatus', ['received', 'confirmed',])
    .match({
      id: packProfileId,
      profileId: user.id,
      purchaseStatus: 'active',
    })
    .maybeSingle();

  console.log('🚀 ~ POST ~ packProfiles:', packProfile);
  if (!packProfile) {
    return NextResponse.json({ message: 'Erro ao procurar pacote do usuário.', }, { status: 400, });
  }

  const { count, } = await supabaseAdmin
    .from('booking')
    .select('*', { count: 'exact', head: true, })
    .eq('packProfileId', packProfile.id)
    .in('status', accountedForBookingStatus);

  if (packProfile.pack.use !== null && count !== null && (count + packProfile.used) >= packProfile.pack.use) {
    return NextResponse.json({ message: 'Usuário não possui créditos.', }, { status: 400, });
  }

  const { data: booking, error, } = await supabaseAdmin
    .from('booking')
    .insert({
      code: generateRandomString(),
      day,
      packProfileId: packProfile.id,
      serviceId,
      scheduleId: schedule.id,
      status,
      teacherId,
      userId: user.id,
    })
    .select()
    .maybeSingle();

  if (booking === null && error) {
    return NextResponse.json({ message: dictionary[error.message] || 'Erro ao salvar agendamento.', }, { status: 400, });
  }

  if (equipments.length) {
    const equipmentsPromises = equipments.map(async equipment => {
      return await supabaseAdmin.from('booking_equipment').insert({
        bookingId: booking.id,
        equipmentId: equipment.id,
      });
    });
    await Promise.all(equipmentsPromises);
  }

  console.log('🚀 ~ POST ~ packProfile:', packProfile.pack);
  console.log('🚀 ~ POST ~ count:', count);
  if (packProfile.pack.use !== null && ((count || 0) + packProfile.used) >= packProfile.pack.use - 1) {
    await supabaseAdmin
      .from('pack_profile')
      .update({ purchaseStatus: 'used', })
      .match({ id: packProfileId, })
      .maybeSingle();
  }

  return NextResponse.json(booking);
}
