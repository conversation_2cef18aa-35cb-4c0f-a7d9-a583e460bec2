import dictionary from '@/lib/dictionary';
import { calculateTimeDifferenceInHours, } from '@/lib/utils';
import { userInfo, } from '@/supabase/verifications/user-info';
import { MISSING_PARAMS, UNAUTHORIZED, UNKNOWN_ERROR, } from '@/utils/constants';
import { Booking, Schedule, Service, } from '@/utils/constants/types';
import { bookingStatusAdminEditableOnly, } from '@/utils/supabase/constants';
import { createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestMaybeSingleResponse, } from '@supabase/supabase-js';
import { addHours, addMinutes, } from 'date-fns';
import { NextRequest, NextResponse, } from 'next/server';

export async function PATCH(request: NextRequest) {
  const user = await userInfo();
  if (!user) return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });

  const json = await request.json();
  let { status, code, } = json;
  if (!status || !code) {
    return NextResponse.json({ message: MISSING_PARAMS, }, { status: 400, });
  }

  if (bookingStatusAdminEditableOnly.includes(status) && user.user_metadata.role !== 'admin') {
    return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });
  }

  const supabaseAdmin = createClientAdmin();

  if (user.user_metadata.role === 'student') {
    const { data: booking, error, }: PostgrestMaybeSingleResponse<Booking & { service: Service, schedule: Schedule }> = await supabaseAdmin
    .from('booking')
    .select('day, userId, service(*), schedule(*)')
    .eq('code', code)
    .maybeSingle();

    if (!booking || booking.userId !== user.id || booking.service.schoolId !== user.user_metadata.schoolId || error) return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });

    if (status === 'canceled') {
      const hoursAndMinutes = booking.schedule.hour.split(':');
      let date = addHours(booking.day, parseInt(hoursAndMinutes[0]));
      date = addMinutes(date, parseInt(hoursAndMinutes[1]));
      const timeDifference = calculateTimeDifferenceInHours(date);
      const { data: service, } = await supabaseAdmin
        .from('service')
        .select()
        .match({ id: booking.service.id, })
        .maybeSingle();
      if (!service) return NextResponse.json({ message: 'Não foi possível encontrar a política de cancelamento.', }, { status: 401, });
      if (service.policy > Math.abs(timeDifference)) status = 'bailed';
    }
  }

  const { data: booking, error, } = await supabaseAdmin
    .from('booking')
    .update({ status: status, })
    .eq('code', code)
    .select()
    .maybeSingle();

  if (error || !booking) return NextResponse.json({ message: dictionary[error.message] || 'Não foi possível alterar agendamento.', }, { status: 400, });

  return NextResponse.json(booking);
}