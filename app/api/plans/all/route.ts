import { Plan, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';

export async function GET() {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();


  const supabaseAdmin = createClientAdmin();
  const { data, } = await supabaseAdmin.from('plan').select().match({ schoolId: user?.user_metadata.schoolId, }).returns<Plan[] | null>();
  if (data) {
    return Response.json(data);
  } else {
    return Response.json({ message: 'Erro ao buscar planos.', }, { status: 400, });
  }
}
