import { UNAUTHORIZED, } from '@/utils/constants';
import { createClientAdmin, } from '@/utils/supabase/server';
import { NextRequest, NextResponse, } from 'next/server';
import webpush from 'web-push';

export async function POST(request: NextRequest) {
  const token = request.headers.get('api-access-token');
  if (token !== process.env.API_ACCESS_TOKEN) {
    return Response.json({ message: UNAUTHORIZED, status: 401, });
  }
  const data = await request.json();

  const supabaseAdmin = createClientAdmin();
  const { data: pwaSubscription, } = await supabaseAdmin
    .from('user_pwa')
    .select()
    .match({  profileId: data.profileId, })
    .order('createdAt', { ascending: false, })
    .single();
    console.log('🚀 ~ sendPushNotificationToUser ~ pwaSubscription:', pwaSubscription);

  if (!pwaSubscription) {
    return NextResponse.json({ message: 'Sem registro de push notification.', }, { status: 400,  });
  }

  webpush.setVapidDetails(
    'mailto:<EMAIL>',
    process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!,
    process.env.VAPID_PRIVATE_KEY!,
  );

  const message = data.isToday ? 'Sua fatura vence hoje.' : `Sua fatura vence dia ${data.dueDay}`;
  const payload = JSON.stringify({
    title: message,
    url: request.nextUrl.origin,
  });
  console.log('🚀 ~ sendPushNotificationToUser ~ payload:', payload);

  await webpush.sendNotification(JSON.parse(pwaSubscription.sub), payload);

  return NextResponse.json({ status: 200, });
}