import dictionary from '@/lib/dictionary';
import { LESSON_STATUS, MISSING_PARAMS, UNAUTHORIZED, UNKNOWN_ERROR, } from '@/utils/constants';
import { Progress, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { NextRequest, NextResponse, } from 'next/server';

export async function GET(request: NextRequest): Promise<NextResponse<Progress[] | { message: string }>> {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });
  }

  const searchParams = request.nextUrl.searchParams;
  const lessonId = searchParams.get('lessonId');
  const serviceId = searchParams.get('serviceId');
  if (!serviceId) {
    return NextResponse.json({ message: MISSING_PARAMS, }, { status:400, });
  }
  const supabaseAdmin = createClientAdmin();
  const { data: policy, } = await supabaseAdmin
  .from('progress')
  .select()
  .match({
    userId: user.id,
    lessonId,
    status: LESSON_STATUS.COMPLETE,
  })
  .maybeSingle();
  if (policy) {
    return NextResponse.json(policy);
  } else {
    return NextResponse.json({ message: 'Erro ao buscar progresso.', }, { status: 400, });
  }
}

export async function POST(request: Request) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });
  }


  const data = await request.json();
  const { courseId, lessonId, lessonSequence, status, } = data;

  if (!courseId || !lessonId || !lessonSequence || !status) {
    return NextResponse.json({ message: MISSING_PARAMS, }, { status:400, });
  }

  const supabaseAdmin = createClientAdmin();
  const { data: progress, error, } = await supabaseAdmin
  .from('progress')
  .insert({
    userId: user.id,
    courseId,
    lessonId,
    lessonSequence,
    status,
  })
  .select()
  .maybeSingle();

  if (progress) return NextResponse.json(progress);
  return NextResponse.json({ message: dictionary[error.message] || UNKNOWN_ERROR, }, { status: 400, });
}