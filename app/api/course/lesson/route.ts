import { MISSING_PARAMS, UNAUTHORIZED, } from '@/utils/constants';
import { Progress, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { NextRequest, NextResponse, } from 'next/server';

export async function GET(request: NextRequest): Promise<NextResponse<Progress[] | { message: string }>> {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });
  }

  const searchParams = request.nextUrl.searchParams;
  const courseId = searchParams.get('courseId');
  const sequence = searchParams.get('sequence');
  if (!courseId || !sequence) {
    return NextResponse.json({ message: MISSING_PARAMS, }, { status:400, });
  }
  const supabaseAdmin = createClientAdmin();
  const { data: lesson, } = await supabaseAdmin
  .from('lesson')
  .select()
  .match({
    courseId,
    sequence,
  })
  .maybeSingle();
  if (lesson) {
    return NextResponse.json(lesson);
  } else {
    return NextResponse.json({ message: 'Erro ao buscar aula.', }, { status: 400, });
  }
}
