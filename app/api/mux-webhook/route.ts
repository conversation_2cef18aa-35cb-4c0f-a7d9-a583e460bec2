import { createClientAdmin, } from '@/utils/supabase/server';

export async function POST(request: Request) {
  const body = await request.json();
  const { type, data, } = body;
  console.log("🚀 ~ POST ~ body:", body)

  const supabaseAdmin = createClientAdmin();

  if (type === 'video.asset.ready') {
    const { error, } = await supabaseAdmin
      .from('video')
      .update({
        publicPlaybackId: data.playback_ids[0].id,
        duration: data.duration,
        videoId: data.id,
      })
      .match({ id: data.passthrough, });
      console.log("🚀 ~ POST ~ error:", error)
      if (error) return Response.json({ status: 400, });
      return Response.json({ status: 200, });
  } else {
    return Response.json({ status: 400, });
  }
}