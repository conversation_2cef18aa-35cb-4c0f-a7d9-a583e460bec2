import { Tables, } from '@/database.types';
import { UNAUTHORIZED, } from '@/utils/constants';
import { BOOKING_STATUS, } from '@/utils/supabase/constants';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';

type Schedule = Tables<'schedule'>
type Bookings = {
  day: string,
  schedule: Schedule,
}
type UnifiedEquipments = Tables<'equipment'>[]
type Equipment = Tables<'equipment'>
type Equipments = Equipment[]
type GroupedEquipments = {
  selects: {
    [id: string]: Equipment[]
  },
  checkboxes: {
    [key: string]: Equipment
  }
}

const groupEquipments = (equipments: { equipment: Equipment }[]): GroupedEquipments => {
  let selects: { [key: string]: Equipments } = {};
  let checkboxes: { [key: string]: Equipment } = {};
  equipments.map(({ equipment, }) => {
    if (!equipment.type) {
      checkboxes = { ...checkboxes, [equipment.name]: equipment, };
    } else {
      if (selects[equipment.type]) {
        selects = { ...selects, [equipment.type]: [...selects[equipment.type], equipment,], };
      } else {
        selects = { ...selects, [equipment.type]: [equipment,], };
      }
    }
  });
  return {
    selects,
    checkboxes,
  };
};

export async function GET(request: Request) {
  const searchParams = request.nextUrl.searchParams;
  const day = searchParams.get('day');
  const scheduleId = searchParams.get('scheduleId');
  const serviceId = searchParams.get('serviceId');

  if (!day || !scheduleId || !serviceId) return Response.json({ message: 'Informações incompletas.', }, { status: 400,  });

  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user) return Response.json({ message: UNAUTHORIZED, }, { status: 400, });

  const supabaseAdmin = createClientAdmin();

  const { data: equipmentsData, } = await supabaseAdmin
    .from('service_equipment')
    .select('equipment(*)')
    .match({ serviceId, active: true, })
    .returns<[{ equipment: Equipment }] | null>();
  if (!equipmentsData) return Response.json({ message: 'Equipamentos não encontrados.', }, { status: 400, });

  const { data: bookings, } = await supabaseAdmin
  .from('booking')
  .select('id, day, schedule(*)')
  .match({
    day,
    scheduleId,
    serviceId,
    status: 'approved',
  })
  .returns<Bookings[] | null>();

  if (!bookings?.length) {
    return Response.json(groupEquipments(equipmentsData));
  }

  const bookingsFilteredByHour = bookings?.filter(({ schedule, }) => schedule.id === scheduleId);

  const equipmentsPromise = bookingsFilteredByHour.map(async booking => await supabaseAdmin
      .from('booking_equipment')
      .select('equipment(*)')
      .match({ bookingId: booking.id, })
      .returns<{ equipment: Tables<'equipment'> }[]>()) || [];

  const bookedEquipmentsPromise = await Promise.all(equipmentsPromise);
  if (!bookedEquipmentsPromise.length) return Response.json(groupEquipments(equipmentsData));

  const bookedEquipmentsData = bookedEquipmentsPromise.filter(booked => booked.status === 200 && booked.data !== null && booked.data.length !== 0).map(booked => booked.data);
  if (!bookedEquipmentsData?.length) return Response.json(groupEquipments(equipmentsData));

  const bookedEquipmentsUnified = bookedEquipmentsData.reduce((unifiedEquipments, promiseResult) => {
    if (promiseResult === null) return unifiedEquipments;
    return [
      ...unifiedEquipments,
      ...promiseResult.filter(data => data.equipment !== null).map(data => data.equipment),
    ];
  }, [] as UnifiedEquipments);
  const bookedEquipmentsWithCount = bookedEquipmentsUnified.reduce((acc, item) => {
    const { id, quantity, } = item;
    if (!acc[id]) {
      acc[id] = {
        count: 1,
        quantity,
      };
    } else {
      acc[id].count++;
    }
    return acc;
  }, {} as { [id: string]: { count: number, quantity: number } } );
  const unavailableEquipmentIds = Object.keys(bookedEquipmentsWithCount).filter(id => bookedEquipmentsWithCount[id].count >= bookedEquipmentsWithCount[id].quantity);
  if (unavailableEquipmentIds.length === 0) return Response.json(groupEquipments(equipmentsData));
    const filteredEquipments = equipmentsData?.filter(({ equipment, }) => !unavailableEquipmentIds.includes(equipment.id)) || [];
  return Response.json(groupEquipments(filteredEquipments));
}
