import { MISSING_PARAMS, UNAUTHORIZED, } from '@/utils/constants';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';

type RequestType = {
  name: string,
  quantity: string,
  type: string | undefined,
}

export async function POST(request: Request) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user || user.user_metadata.role !== 'admin') {
    return Response.json({ message: UNAUTHORIZED, }, { status: 401, });
  }

  const data: RequestType = await request.json();
  const { name, quantity, type, } = data;
  if (!name || !quantity) {
    return Response.json({ message: MISSING_PARAMS, }, { status:400, });
  }
  const supabaseAdmin = createClientAdmin();
  const { data: equipment, } = await supabaseAdmin
  .from('equipment')
  .insert({
    name,
    quantity: parseInt(quantity),
    schoolId: user.user_metadata.schoolId,
    type,
  }).select().single();
  if (equipment) {
    return Response.json(equipment);
  } else {
    return Response.json({ message: 'Erro ao salvar equipamento, tente mais tarde.', }, { status: 400, });
  }
}