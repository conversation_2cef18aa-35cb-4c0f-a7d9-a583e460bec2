import { UNAUTHORIZED, } from '@/utils/constants';
import { Equipment, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';

export async function GET() {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user) return Response.json({ message: UNAUTHORIZED, }, { status: 400, });

  const supabaseAdmin = createClientAdmin();
  const { data: equipments, } = await supabaseAdmin
    .from('equipment')
    .select().match({ schoolId: user.user_metadata.schoolId, })
    .returns<Equipment[]>();
  if (equipments) {
    return Response.json(equipments);
  } else {
    return Response.json({ message:  'Erro ao salvar equipamento, tente mais tarde.', }, { status: 400, });
  }
}
