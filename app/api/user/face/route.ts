import { MISSING_PARAMS, UNAUTHORIZED, UNKNOWN_ERROR, } from '@/utils/constants';
import { Face, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { NextRequest, NextResponse, } from 'next/server';

export async function GET (request: NextRequest) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user) return NextResponse.json({ message: UNAUTHORIZED, }, { status: 400, });

  const searchParams = request.nextUrl.searchParams;

  const supabaseAdmin = createClientAdmin();
  const { data, error, } = await supabaseAdmin
    .from('face')
    .select()
    .match({ email: searchParams.get('email') || user.email, })
    .maybeSingle();

  if (!data && !error) return NextResponse.json(null);
  if (!data && error) return NextResponse.json({ message: 'Não existe imagem registrada.', }, { status: 400, });

  return NextResponse.json(data);
}

export async function POST (request: NextRequest) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user) return NextResponse.json({ message: UNAUTHORIZED, }, { status: 400, });

  const formData = await request.json();
  if (!formData.newFace) return NextResponse.json({ message: MISSING_PARAMS, }, { status: 400, });

  const supabaseAdmin = createClientAdmin();
  const { data: currentFace, error, } = await supabaseAdmin
  .from('face')
  .select()
  .match({ email: formData.email || user.email, })
  .maybeSingle();
  console.log("🚀 ~ POST ~ currentFace:", currentFace)
  console.log("🚀 ~ POST ~ error:", error)
  
  let description = [];

  if (!currentFace && !error) {
    description.push(formData.newFace);
  }

  if (currentFace) {
    if (currentFace.description.includes(formData.newFace)) {
      return NextResponse.json({ message: 'Essa imagem já foi salva anteriormente.', }, { status: 400, });
    }
    description = [ ...currentFace.description, formData.newFace, ];
  }

  const { data, error: saveFaceError } = await supabaseAdmin
    .from('face')
    .upsert({ email: formData.email || user.email as string, description, }, { onConflict: 'email', })
    .select()
    .returns<Face | null>();
    console.log("🚀 ~ POST ~ data:", data)
    console.log("🚀 ~ POST ~ error:", saveFaceError)

  if (data) return NextResponse.json(data);

  return NextResponse.json({ message: UNKNOWN_ERROR, }, { status: 500, });
}