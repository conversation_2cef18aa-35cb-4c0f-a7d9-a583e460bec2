import { MISSING_PARAMS, UNAUTHOR<PERSON>Z<PERSON>, } from '@/utils/constants';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { NextRequest, NextResponse, } from 'next/server';

export async function GET(request: NextRequest) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user || user.user_metadata.role !== 'admin') return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });

  const searchParams = request.nextUrl.searchParams;
  const encodedUserEmails: string | null = searchParams.get('userEmails');
  if (!encodedUserEmails) {
    return NextResponse.json({ message: MISSING_PARAMS, }, { status: 400, });
  }

  const userEmails = JSON.parse(decodeURIComponent(encodedUserEmails));

  const supabaseAdmin = createClientAdmin();
  const { data: profiles, } = await supabaseAdmin.from('profile').select().in('email', userEmails).order('name');
  if (!profiles) return NextResponse.json({ message: 'Usuario nao encontrado.', }, { status: 404, });
  return NextResponse.json(profiles);
}