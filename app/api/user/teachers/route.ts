import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { NextResponse, } from 'next/server';

export async function GET() {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  const supabaseAdmin = createClientAdmin();
  const { data: profiles, } = await supabaseAdmin
    .from('profile')
    .select()
    .in('type', ['teacher', 'admin',])
    .eq('schoolId', user?.user_metadata.schoolId,);

  if (!profiles) return NextResponse.json({ message: 'Usu<PERSON>rio não encontrado.', }, { status: 404, });
  return NextResponse.json(profiles);
}