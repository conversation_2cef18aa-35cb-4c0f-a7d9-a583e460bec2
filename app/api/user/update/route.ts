import { userInfo, } from '@/supabase/verifications/user-info';
import { UNAUTHORIZED, UNKNOWN_ERROR, } from '@/utils/constants';
import { createClientAdmin, } from '@/utils/supabase/server';
import { NextRequest, NextResponse, } from 'next/server';

type RequestType = {
  birthdate?: string,
  cep?: string,
  city?: string,
  email?: string,
  faceImage?: string,
  name?: string,
  neighborhood?: string,
  profileImage?: string,
  phone?: string,
  street?: string,
  state?: string,
  mpId?: string,
}

export async function PATCH(request: NextRequest) {
  const user = await userInfo();

  if (!user) return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });

  const requestData: RequestType = await request.json();
  const supabaseAdmin = createClientAdmin();
  await supabaseAdmin
    .from('log')
    .insert({ value: JSON.stringify(requestData), });

  let { email, ...data } = requestData;

  const { data: profile, error: profileError, } = await supabaseAdmin
    .from('profile')
    .update(data)
    .match(email ? {
      email,
    } : {
      id: user?.id,
    })
    .select()
    .maybeSingle();

  if (profile) return NextResponse.json(profile);
  await supabaseAdmin.from('error').insert({ value: JSON.stringify({ profileError, }), });
  return NextResponse.json({ message: UNKNOWN_ERROR, }, { status: 400, });
}
