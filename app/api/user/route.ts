import { NextRequest, NextResponse, } from 'next/server';
import jwt from 'jsonwebtoken';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  console.log(process.env.JWT_KEY);
  console.log(searchParams.get('token'));
  jwt.verify(searchParams.get('token'), process.env.JWT_KEY, function(err, decoded) {
    console.log(JSON.stringify(decoded)); // bar
  });
  return NextResponse.json({
    message: 'Hello World!',
  });
}