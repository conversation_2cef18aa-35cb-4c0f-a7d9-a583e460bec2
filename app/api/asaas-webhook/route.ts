import { Payment, } from '@/utils/constants/types';
import { createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestMaybeSingleResponse, } from '@supabase/supabase-js';
import { format, getDate, parse, } from 'date-fns';
import { ptBR, } from 'date-fns/locale';
import { NextResponse, } from 'next/server';

export async function POST(request: Request) {
  const accessToken = request.headers.get('asaas-access-token');

  if (accessToken !== process.env.AS_ACCESS_TOKEN) {
    return new Response(null, { status: 400, });
  }

  const res = await request.json();
  console.log('🚀 ~ POST ~ res:', res);
  const supabase = createClientAdmin();


  if (res.event === 'SUBSCRIPTION_CREATED') {
    const { data: profile, } = await supabase
      .from('profile')
      .select()
      .match({ asId: (res.payment || res.subscription).customer, })
      .maybeSingle();
    console.log('🚀 ~ POST ~ profile:', profile);

    if (!profile) {
      return new Response(null, { status: 400, });
    }
    console.log('🚀 ~ POST ~ profile:', profile);
    const parsedDate = parse(res.subscription.nextDueDate, 'dd/MM/yyyy', new Date(), { locale: ptBR, });
    const parsedNextDueDate = format(parsedDate, 'yyyy-MM-dd');
    const { error, } = await supabase
    .from('subscription')
    .insert({
      id: res.subscription.id,
      object: res.subscription.object,
      customer: res.subscription.customer,
      billingType: res.subscription.billingType,
      billingDay: getDate(parsedDate),
      cycle: res.subscription.cycle,
      value: res.subscription.value,
      nextDueDate: parsedNextDueDate,
      description: res.subscription.description,
      deleted: res.subscription.deleted,
      planId: res.subscription.externalReference.split(':')[1],
      profileId: profile.id,
      paymentStatus: 'pending',
      status: res.subscription.status,
      cardNumber: res.subscription?.creditCard?.creditCardNumber,
      cardBrand: res.subscription?.creditCard?.creditCardBrand,
    });
    console.log('🚀 ~ SUBSCRIPTION_CREATED ~ error:', error);
    if (!error) return NextResponse.json(null, { status: 200, });
    else return NextResponse.json(null, { status: 400, });
  }

  if (res.event === 'PAYMENT_CREATED' || (res.event === 'PAYMENT_CONFIRMED' && res.payment.billingType === 'CREDIT_CARD')) {
    const [type, value,] = res.payment.externalReference.split(':');
    const { error, }: PostgrestMaybeSingleResponse<Payment> = await supabase
    .from('payment')
    .insert({
      id: res.payment.id,
      dateCreated: res.dateCreated,
      subscriptionId: res.payment.subscription,
      profileAsId: res.payment.customer,
      value: res.payment.value,
      netValue: res.payment.netValue,
      description: res.payment.description,
      paymentType: res.payment.billingType,
      status: res.payment.status?.toLowerCase(),
      dueDate: res.payment.dueDate,
      paymentDate: res.payment.paymentDate,
      installmentNumber: res.payment.installmentNumber,
      invoiceUrl: res.payment.invoiceUrl,
      externalReference: res.payment.externalReference,
      ...(type === 'pack' ? { packId: value, } : { planId: value, }),
    })
    .select()
    .maybeSingle();
    console.log('🚀 ~ PAYMENT_CREATED ~ error:', error);

    if (error) new Response(null, { status: 400, });
    new Response(null, { status: 200, });
  }

  if (res.event === 'PAYMENT_CONFIRMED' || res.event === 'PAYMENT_RECEIVED' || res.event === 'PAYMENT_OVERDUE') {
    const { error, }: PostgrestMaybeSingleResponse<Payment> = await supabase
    .from('payment')
    .update({
      status: res.payment.status?.toLowerCase(),
      paymentDate: res.payment.paymentDate,
    })
    .match({ id: res.payment.id, })
    .select()
    .maybeSingle();
    console.log('🚀 ~ POST ~ error:', error);
    if (error) new Response(null, { status: 400, });
  }
  return new Response(null, { status: 200, });
}