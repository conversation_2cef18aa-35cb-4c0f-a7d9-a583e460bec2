import { Face, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { NextResponse, } from 'next/server';

export async function GET() {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user || user.user_metadata.role !== 'admin') {
    return NextResponse.json({ message: 'Sem permissão.', }, { status: 401, });
  }

  const supabaseAdmin = createClientAdmin();
  const { data: faces, } = await supabaseAdmin
  .from('face')
  .select('*, profile!inner()')
  .eq('profile.schoolId', user.user_metadata.schoolId)
  .returns<Face[] | null>();

  if (faces !== null) {
    return NextResponse.json(faces);
  } else {
    return NextResponse.json({ message: 'Erro ao buscar imagens do reconhecimento facial.', }, { status: 400, });
  }
}
