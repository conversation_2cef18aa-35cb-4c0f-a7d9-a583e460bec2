import { MISSING_PARAMS, } from '@/utils/constants';
import { Face, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { NextRequest, NextResponse, } from 'next/server';

export async function GET(request: NextRequest) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user || user.user_metadata.role !== 'admin') {
    return NextResponse.json({ message: 'Sem permissão.', }, { status: 401, });
  }

  const searchParams = request.nextUrl.searchParams;
  if (!searchParams.get('email')) {
    return NextResponse.json({ message: MISSING_PARAMS, }, { status: 400, });
  }
  const supabaseAdmin = createClientAdmin();
  const { data: face, } = await supabaseAdmin
  .from('face')
  .select('*, profile!inner()')
  .eq('profile.email', searchParams.get('email'))
  .returns<Face | null>();

  if (face) {
    return NextResponse.json(face);
  } else {
    return NextResponse.json({ message: 'Erro ao buscar imagens do usuário.', }, { status: 400, });
  }
}
