import dictionary from '@/lib/dictionary';
import { MISSING_PARAMS, UNAUTHORIZED, } from '@/utils/constants';
import { Service, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestError, PostgrestSingleResponse, User, } from '@supabase/supabase-js';
import { NextRequest, NextResponse, } from 'next/server';

type Basics = {
  title: string,
  description: string,
  limit: number,
  policy: string | null
}

type Schedules = {
  [key: string]: string[]
}

type Teacher = { id: string }

type RequestType = {
  formData: {
    basics: Basics,
    schedules: Schedules,
    equipments: [{
      id: string,
    }],
    teachers: Teacher[]
    randomTeacher: boolean,
  }
  serviceId: string,
}

type UpdateServiceProps = {
  basics: Basics,
  serviceId: string,
  user: User,
  randomTeacher: boolean
}

type UpdateSchedules = {
  schedules: Schedules,
  serviceId: string,
  limit: number
}

type UpdateEquipments = {
  equipments: { id: string, }[],
  serviceId: string,
}

type UpdateTeachers = {
  teachers: { id: string, }[],
  serviceId: string,
}

type GetErrorMessageType = {
  error: PostgrestError,
  service: string,
}

async function updateService({ basics, serviceId, user, }: UpdateServiceProps) {
  const supabaseAdmin = createClientAdmin();
  const { data, error, }: PostgrestSingleResponse<Service | null> = await supabaseAdmin
    .from('service')
    .update(basics)
    .match({ id: serviceId, schoolId: user.user_metadata.schoolId, })
    .select()
    .maybeSingle();
  return { data, error, };
}

async function updateSchedules({ schedules, serviceId, limit, }: UpdateSchedules) {
  const deleteScheduleResponse = await deleteSchedule({ serviceId, });

  if (deleteScheduleResponse.error) {
    return { error: deleteScheduleResponse.error, };
  }

  const formattedSchedule = [];
  for (const weekDayNumber in schedules) { // Use for...in for object iteration
    const weekDaySchedules = schedules[weekDayNumber];
    const inserts = weekDaySchedules.map(hour => ({
      serviceId,
      number: parseInt(weekDayNumber),
      hour,
      limit,
    }));
    formattedSchedule.push(...inserts);
  }
  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
    .from('schedule')
    .insert(formattedSchedule);
  return { error, };
}

async function deleteSchedule({ serviceId, }: { serviceId: string }) {
  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
  .from('schedule')
  .update({ active: false, })
  .eq('serviceId', serviceId);
  return { error, };
}

async function deleteEquipments({ serviceId, }: { serviceId: string }) {
  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
  .from('service_equipment')
  .delete()
  .match({ serviceId, });
  return { error, };
}

async function deleteTeachers({ serviceId, }: { serviceId: string }) {
  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
  .from('service_teacher')
  .delete()
  .match({ serviceId, });
  return { error, };
}

async function updateEquipments({ equipments, serviceId, }: UpdateEquipments) {
  const deleteEquipmentsResponse = await deleteEquipments({ serviceId, });

  if (deleteEquipmentsResponse.error) {
    return { error: deleteEquipmentsResponse.error, };
  }

  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
    .from('service_equipment')
    .insert(equipments.map(equipment => ({
      equipmentId: equipment.id,
      serviceId,
    })));
  return { error, };
}

async function updateTeachers({ teachers, serviceId, }: UpdateTeachers) {
  const deleteTeachersResponse = await deleteTeachers({ serviceId, });

  if (deleteTeachersResponse.error) {
    return { error: deleteTeachersResponse.error, };
  }

  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
    .from('service_teacher')
    .insert(teachers.map(teacher => ({
      profileId: teacher.id,
      serviceId,
    })));
  return { error, };
}

function getErrorMessage({ error, service, }: GetErrorMessageType) {
  return error.message && error.message in dictionary
      ? dictionary[error.message]
      : `Não foi possível atualizar ${service}.`;
}

export async function PATCH(request: NextRequest) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user || user?.user_metadata.role !== 'admin') return NextResponse.json({ message: UNAUTHORIZED, }, { status: 400, });

  const body: RequestType = await request.json();
  const { formData: { equipments, schedules, basics, teachers, randomTeacher, }, serviceId, } = body;

  if (!schedules || !basics ) return NextResponse.json({ message: MISSING_PARAMS, }, { status: 400, });

  const updateServiceResponse = await updateService({ basics, serviceId, user, randomTeacher, });

  if (updateServiceResponse.error) {
    const message = getErrorMessage({ error: updateServiceResponse.error, service: 'serviço', });
    return NextResponse.json({ message, }, { status: 400, });
  }

  const updateSchedulesResponse = await updateSchedules({ schedules, serviceId, limit: basics.limit, });
  if (updateSchedulesResponse.error) {
      const message = getErrorMessage({ error: updateSchedulesResponse.error, service: 'horários', });
      return NextResponse.json({ message, }, { status: 400, });
  }

  const updateEquipmetsResponse = await updateEquipments({ equipments, serviceId, });

  if (updateEquipmetsResponse.error) {
    const message = getErrorMessage({ error: updateEquipmetsResponse.error, service: 'equipamentos', });
    return NextResponse.json({ message, }, { status: 400, });
  }

  const updateTeachersResponse = await updateTeachers({ teachers, serviceId, });

  if (updateTeachersResponse.error) {
    const message = getErrorMessage({ error: updateTeachersResponse.error, service: 'professores', });
    return NextResponse.json({ message, }, { status: 400, });
  }

  return NextResponse.json(null, { status: 200, });
}