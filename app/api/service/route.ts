import { formatScheduleData, } from '@/lib/utils';
import { MISSING_PARAMS, UNAUTHORIZED, } from '@/utils/constants';
import { Equipment, Schedule, ServiceTeacher, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { NextResponse, } from 'next/server';

export async function GET(request: Request) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user || user.user_metadata.role !== 'admin') {
    return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });
  }
  const searchParams = request.nextUrl.searchParams;
  const serviceId = searchParams.get('serviceId');
  if (!serviceId) {
    return Response.json({ message: MISSING_PARAMS, }, { status: 400, });
  }
  const supabaseAdmin = createClientAdmin();
  let { data: service, } = await supabaseAdmin
    .from('service')
    .select()
    .match({
      schoolId: user?.user_metadata.schoolId,
      id: serviceId,
    })
    .maybeSingle();

  if (!service) {
    return Response.json({ message: 'Erro ao buscar serviço.', }, { status: 400, });
  }

  const { data: serviceEquipment, } = await supabaseAdmin
    .from('service_equipment')
    .select('equipment(*)')
    .match({ serviceId: service.id, active: true, })
    .returns<{ equipment: Equipment }[] | null>();

  if (serviceEquipment) {
    service.equipments = serviceEquipment.map(service => {
      return { id: service.equipment.id, };
    });
  }

    const { data: schedules, } = await supabaseAdmin
      .from('schedule')
      .select()
      .match({ serviceId: service.id, active: true, })
      .returns<Schedule[] | null>();

    if (schedules) {
      service.schedules = formatScheduleData(schedules);
    }

    const { data: teachers, } = await supabaseAdmin
      .from('service_teacher')
      .select()
      .match({ serviceId: service.id, })
      .returns<ServiceTeacher[] | null>();

    if (teachers) {
      service.teachers = teachers.map(teacher => ({
        id: teacher.profileId,
      }));
    }
    return NextResponse.json(service);
}


