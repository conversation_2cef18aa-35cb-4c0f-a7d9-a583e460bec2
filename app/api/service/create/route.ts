import dictionary from '@/lib/dictionary';
import { userInfo, } from '@/supabase/verifications/user-info';
import { MISSING_PARAMS, UNAUTHORIZED, } from '@/utils/constants';
import { Service, } from '@/utils/constants/types';
import { createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestError, PostgrestSingleResponse, User, } from '@supabase/supabase-js';
import { NextResponse, } from 'next/server';

type Basics = {
  title: string,
  description: string,
  limit: number,
  sportId: string,
  policy: string | null,
}

type Schedules = {
  [key: string]: string[]
}

type Teacher = { id: string }

type Equipment = { id: string }

type RequestType = {
  formData: {
    basics: Basics,
    schedules: Schedules,
    equipments: Equipment[],
    teachers: Teacher[],
    randomTeacher: boolean,
  }
  serviceId: string,
}

type SaveServiceProps = {
  basics: Basics,
  user: User,
  randomTeacher: boolean
}

type SaveSchedules = {
  schedules: Schedules,
  serviceId: string,
  limit: number
}

type SaveTeachers = {
  teachers: Teacher[],
  serviceId: string
}

type SaveEquipments = {
  equipments: Equipment[],
  serviceId: string,
}

type GetErrorMessageType = {
  error: PostgrestError,
  service: string,
}


async function saveService({ basics, user, randomTeacher, }: SaveServiceProps) {
  const supabaseAdmin = createClientAdmin();
  const { data, error, }: PostgrestSingleResponse<Service | null> = await supabaseAdmin
  .from('service')
  .insert({
    schoolId: user?.user_metadata.schoolId,
    ...basics,
    randomTeacher,
  })
  .select()
  .maybeSingle();
  return { data, error, };
}

async function saveSchedules({ schedules, serviceId, limit, }: SaveSchedules) {
  const formattedSchedule = [];
  for (const weekDayNumber in schedules) {
    const weekDaySchedules = schedules[weekDayNumber];
    const inserts = weekDaySchedules.map(hour => ({
      serviceId,
      number: parseInt(weekDayNumber),
      hour,
      limit,
    }));
    formattedSchedule.push(...inserts);
  }
  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
    .from('schedule')
    .insert(formattedSchedule);
  return { error, };
}

async function saveTeachers({ teachers, serviceId, }: SaveTeachers) {
  const formattedTeachers = teachers.map(teacher => ({
    profileId: teacher.id,
    serviceId,
  }));

  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
    .from('service_teacher')
    .insert(formattedTeachers);

  return { error, };
}

async function saveEquipments({ equipments, serviceId, }:  SaveEquipments) {
  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
    .from('service_equipment')
    .insert(equipments.map(equipment => ({
      equipmentId: equipment.id,
      serviceId,
    })));

  return { error, };
}

async function deleteService({ serviceId, }: { serviceId: string }) {
  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
    .from('service')
    .delete()
    .match({ id: serviceId, })
    .select();

  return { error, };
}

function getErrorMessage({ error, service, }: GetErrorMessageType) {
  return error.message && error.message in dictionary
      ? dictionary[error.message]
      : `Não foi possível salvar ${service}.`;
}

export async function POST(request: Request) {
  const user = await userInfo();

  if (!user || user?.user_metadata.role !== 'admin') return NextResponse.json({ message: UNAUTHORIZED, }, { status: 400, });

  const data: RequestType = await request.json();
  const { formData: { basics, equipments, teachers, schedules, randomTeacher, },} = data;

  if (!schedules || !basics ) return Response.json({ message: MISSING_PARAMS, }, { status: 400, });

  const saveServiceResponse = await saveService({ basics, user, randomTeacher, });

  if (saveServiceResponse.error || !saveServiceResponse.data) {
    const message = getErrorMessage({ error: saveServiceResponse.error, service: 'serviço', });
    return NextResponse.json({ message, }, { status: 400, });
  }

  const serviceId = saveServiceResponse.data.id;
  const saveScheduleResponse = await saveSchedules({ schedules, serviceId, limit: basics.limit, });

  if (saveScheduleResponse.error) {
    const deleteServiceResponse = await deleteService({ serviceId, });
    if (deleteServiceResponse.error ) {
      const message = getErrorMessage({ error: deleteServiceResponse.error, service: 'serviço', });
      return NextResponse.json({ message, }, { status: 400, });
    }
    const message = getErrorMessage({ error: saveScheduleResponse.error, service: 'horários', });
    return NextResponse.json({ message, }, { status: 400, });
  }

  const saveTeachersResponse = await saveTeachers({ teachers, serviceId: serviceId, });

  if (saveTeachersResponse.error) {
    const deleteServiceResponse = await deleteService({ serviceId, });
    if (deleteServiceResponse.error ) {
      const message = getErrorMessage({ error: deleteServiceResponse.error, service: 'serviço', });
      return NextResponse.json({ message, }, { status: 400, });
    }
    const message = getErrorMessage({ error: saveTeachersResponse.error, service: 'professor', });
    return NextResponse.json({ message, }, { status: 400, });
  }

  const saveEquipmentsResponse = await saveEquipments({ equipments, serviceId: serviceId, });

  if (saveEquipmentsResponse.error) {
    const deleteServiceResponse = await deleteService({ serviceId, });
    if (deleteServiceResponse.error ) {
      const message = getErrorMessage({ error: deleteServiceResponse.error, service: 'serviço', });
      return NextResponse.json({ message, }, { status: 400, });
    }
    const message = getErrorMessage({ error: saveEquipmentsResponse.error, service: 'equipamentos', });
    return NextResponse.json({ message, }, { status: 400, });
  }
  return NextResponse.json({ status: 200, });
}