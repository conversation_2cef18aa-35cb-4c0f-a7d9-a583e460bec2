import { userInfo, } from '@/supabase/verifications/user-info';
import { MISSING_PARAMS, UNAUTHORIZED, } from '@/utils/constants';
import { Profile, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { format, } from 'date-fns';
import { NextRequest, NextResponse, } from 'next/server';

export async function POST(request: NextRequest) {
  const user = await userInfo();
  console.log('🚀 ~ POST ~ user:', user);
  if (!user || user.user_metadata.role !== 'admin') {
    return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });
  }

  const data = await request.json();
  console.log('🚀 ~ POST ~ data:', data);
  const { date, students, serviceId, scheduleId,} = data;

  if (!date || !students || students.length === 0 || !serviceId || !scheduleId) {
    return NextResponse.json({ message: MISSING_PARAMS, }, { status: 400, });
  }

  const formattedStudents = students.map((student: Profile) => ({
    date: format(date, 'yyyy-MM-dd'),
    userId: student.id,
    serviceId,
    scheduleId,
  }));
  console.log('🚀 ~ formattedStudents ~ formattedStudents:', formattedStudents);
  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
    .from('attendance')
    .upsert(formattedStudents, { ignoreDuplicates: true, onConflict: '"userId", date, "scheduleId"', })
    .select();
  console.log('🚀 ~ POST ~ error:', error);

  if (error) return NextResponse.json({ message: 'Erro ao salvar presença.', }, { status: 400, });

  return NextResponse.json(null, { status: 200, });
}

export async function GET(request: NextRequest) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user || user.user_metadata.role !== 'admin') {
    return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });
  }

  const searchParams = request.nextUrl.searchParams;
  const month = searchParams.get('month');
  const serviceId = searchParams.get('serviceId');

  const supabaseAdmin = createClientAdmin();
  const currentYear = new Date().getFullYear();

  if (month) {
    const { data: monthlyAttendances, } = await supabaseAdmin
      .from('attendance_monthly')
      .select()
      .match({ year: currentYear, month, serviceId, });
    if (!monthlyAttendances) return NextResponse.json({ message: 'Erro ao buscar frequência.',}, { status: 400, });
    return NextResponse.json(monthlyAttendances);
  }
  const { data: yearlyAttendances, } = await supabaseAdmin
    .from('attendance_yearly')
    .select()
    .match({ year: currentYear, serviceId, });
  if (!yearlyAttendances) return NextResponse.json({ message: 'Erro ao buscar frequência.',}, { status: 400, });
  return NextResponse.json(yearlyAttendances);

}