import { MISSING_PARAMS, UNAUTHORIZED, } from '@/utils/constants';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { NextRequest, NextResponse, } from 'next/server';

export async function POST(request: NextRequest) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user || user.user_metadata.role !== 'admin') {
    return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });
  }

  const data = await request.json();
  const { date, studentId, } = data;

  if (!date || !studentId) {
    return NextResponse.json({ message: MISSING_PARAMS, }, { status: 400, });
  }

  const supabaseAdmin = createClientAdmin();
  const response = await supabaseAdmin
  .from('attendance')
  .delete()
  .match({
    date: new Date(date)?.toLocaleDateString(),
    userId: studentId,
  });

  return NextResponse.json(response);
}