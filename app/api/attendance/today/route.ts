import { MISSING_PARAMS, UNAUTHORIZED, } from '@/utils/constants';
import { Attendance, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { format, } from 'date-fns';
import { NextRequest, NextResponse, } from 'next/server';

export async function GET(request: NextRequest) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user || user.user_metadata.role !== 'admin') {
    return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });
  }

  const searchParams = request.nextUrl.searchParams;
  const date = searchParams.get('date');
  const scheduleId = searchParams.get('scheduleId');
  const serviceId = searchParams.get('serviceId');

  if (!date) {
    return NextResponse.json({ message: MISSING_PARAMS, }, { status: 400, });
  }

  const supabaseAdmin = createClientAdmin();
  const { data: attendance, error, } = await supabaseAdmin
    .from('attendance')
    .select('*, profile!attendance_userId_fkey(*)')
    .eq('profile.schoolId', user.user_metadata.schoolId)
    .eq('date', format(date, 'yyyy-MM-dd'))
    .eq('scheduleId', scheduleId)
    .eq('serviceId', serviceId)
    .returns<Attendance[] | null>();

    console.log('🚀 ~ GET ~ error:', error);
  if (attendance) {
    return NextResponse.json(attendance);
  } else {
    return NextResponse.json({ message: 'Erro ao buscar presenças.', }, { status: 400, });
  }
}
