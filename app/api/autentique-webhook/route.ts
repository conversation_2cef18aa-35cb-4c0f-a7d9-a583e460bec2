import { createClientAdmin, } from '@/utils/supabase/server';
import { NextResponse, } from 'next/server';

export async function POST(request: Request) {
  const { event, } = await request.json();

  console.log('🚀 ~ POST ~ event:', event);
  if (event.type === 'signature.accepted') {
    const { data, } = event;
    const supabaseAdmin = createClientAdmin();
    const { error, } = await supabaseAdmin
      .from('contract')
      .update({ signed: true, })
      .match({ id: data.document, });
    console.log('🚀 ~ POST ~ body:', data);
    if (error) return NextResponse.json({ message: 'Error when updating contract.', }, { status: 400, });
  }

  if (event.type === 'document.created') {
    const { data, } = event;
    const supabaseAdmin = createClientAdmin();
    const { error, } = await supabaseAdmin
      .from('contract')
      .upsert({ id: data.id, originalUrl: data.files.original, }, { onConflict: 'id', });

      console.log('🚀 ~ POST ~ error:', error);
    if (error) return NextResponse.json({ message: 'Error when updating contract.', }, { status: 400, });
  }

  return NextResponse.json({ message: 'Contract saved successfully', }, { status: 200, });

}
