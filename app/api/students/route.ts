import { UNAUTHORIZED, } from '@/utils/constants';
import { Profile, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { NextResponse, } from 'next/server';

export async function GET() {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user || user.user_metadata.role !== 'admin') return NextResponse.json({ message: UNAUTHORIZED, }, { status: 400, });

  const supabaseAdmin = createClientAdmin();
  const { data: profile, } = await supabaseAdmin
  .from('profile')
  .select()
  .match({ schoolId: user.user_metadata.schoolId, })
  .returns<Profile[] | null>()
  .order('name', { ascending: true, })
  ;
  if (!profile) return NextResponse.json({ message: 'Usuarios não encontrados.', }, { status: 404, });
  return NextResponse.json(profile);
}