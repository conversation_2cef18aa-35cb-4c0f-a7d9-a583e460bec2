import { MISSING_PARAMS, UNAUTHORIZED, UNKNOWN_ERROR, } from '@/utils/constants';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { NextRequest, NextResponse, } from 'next/server';

export async function PATCH(request: NextRequest) {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user) return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });

  const json = await request.json();
  const { id, status, } = json;

  if (!id || !status) {
    return NextResponse.json({ message: MISSING_PARAMS, }, { status: 400, });
  }

  if (user.user_metadata.role !== 'admin') {
    return NextResponse.json({ message: UNAUTHORIZED, }, { status: 401, });
  }

  const supabaseAdmin = createClientAdmin();

  const { data: student, error: studentError, } = await supabaseAdmin
    .from('profile')
    .select('*')
    .eq('id', id)
    .maybeSingle();

  if (!student || studentError) {
    return NextResponse.json({ message: 'Aluno não encontrado.', }, { status: 404, });
  }

  const { data: updatedStudent, error: updateError, } = await supabaseAdmin
    .from('profile')
    .update({ status: status, })
    .eq('id', id)
    .select()
    .maybeSingle();

  if (updateError) {
    return NextResponse.json({ message: UNKNOWN_ERROR, }, { status: 400, });
  }

  if (updatedStudent) {
    return NextResponse.json(updatedStudent);
  }

  return NextResponse.json({ message: UNKNOWN_ERROR, }, { status: 400, });
}
