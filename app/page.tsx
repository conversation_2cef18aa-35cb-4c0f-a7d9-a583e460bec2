import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ArrowDownUp, HandCoins, Pencil, } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle, } from '@/components/ui/shard/card';
import Image from 'next/image';

export default async function LandingPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <header className="px-4 lg:px-6 h-14 flex items-center">
        <Link className="flex items-center justify-center" href="/">
          <Dumbbell className="h-6 w-6" />
          <span className="ml-2 text-2xl font-bold">Meu Mestre</span>
        </Link>
        <nav className="ml-auto flex gap-4 sm:gap-6">
          <Link className="text-sm font-medium hover:underline underline-offset-4" href="#features">
            Funcionalidades
          </Link>
          <Link className="text-sm font-medium hover:underline underline-offset-4" href="#price">
            Preço
          </Link>
        </nav>
      </header>
      <main className="flex-1">
        <a aria-label="Chat on WhatsApp" href="https://wa.me/+5585997309676?text=Oi%2C%20estou%20interessado%20na%20plataforma%20Meu%20Mestre." className="fixed right-10 bottom-20"><Image width="50" height="50" src="https://img.icons8.com/?size=100&id=16733&format=png&color=40C057" alt="whatsapp--v1"/></a>
        <section className="w-full py-12 md:py-24 lg:py-32 xl:py-48">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center space-y-4 text-center">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                  Eleve sua academia com o Meu Mestre
                </h1>
                <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl dark:text-gray-400">
                  Transforme sua academia em um negócio online com nossa plataforma.
                </p>
              </div>
              {/* <div className="space-x-4">
                <Button>Começar</Button>
                <Button variant="outline">Ver mais</Button>
              </div> */}
            </div>
          </div>
        </section>
        <section className="w-full py-12 md:py-24 lg:py-32 bg-gray-100 dark:bg-gray-800">
          <div className="container px-4 md:px-6" id="features">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl text-center mb-12">Funcionalidades da Plataforma</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                { icon: HandCoins, title: 'Gerenciamento financeiro', description: 'Fluxo de caixa, faturamento, despesas, lucro, análise de gráficos.', },
                { icon: ArrowDownUp, title: 'Recorrência de cobranças', description: 'Disponibilize ao seus alunos planos e pacotes com pagamento recorrent, cartão, pix, boleto.', },
                { icon: BookOpen, title: 'Aulas online', description: 'Disponibilize ao seus alunos aulas online pra o aprendizado não ficar restrito ao presencial.', },
                { icon: Users, title: 'Gerenciamento de membros', description: 'Notificações sobre membros, sistema de engajamento, presença, progresso.', },
                { icon: BarChart, title: 'Análise de negócio', description: 'Diversos tipos de dados pra ajudar na tomada de decisões.', },
                { icon: Pencil, title: 'Crie contratos', description: 'Crie contratos e envie pra ser assinado pelo aluno, protegendo sua academia.', },
              ].map((feature, index) => (
                <Card key={index}>
                  <CardHeader>
                    <feature.icon className="h-8 w-8 mb-2" />
                    <CardTitle>{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl text-center mb-12">O que os usuários estão dizendo</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                { name: 'João da Silva', gym: 'ClimaDojo', quote: 'A funcionalidade de notificações me permitiu saber de maneira mais rápido o que tá acontecendo.', },
              ].map((testimonial, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle>{testimonial.name}</CardTitle>
                    <CardDescription>{testimonial.gym}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p>{`"${testimonial.quote}"`}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
        <section className="w-full py-12 md:py-24 lg:py-32 bg-gray-100 dark:bg-gray-800" id="price">
          <div className="container px-4 md:px-6">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl text-center mb-12">Planos</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {[
                { name: 'Primeiro mês', price: 'Grátis', features: ['Gerênciamento financeiro', 'Cobrança com recorrência', 'Cursos online', 'Criação de contratos', 'Análise de dados', 'Gerenciamento de membros',], },
                { name: 'Mensal', price: 'R$ 89,90', features: ['Gerênciamento financeiro', 'Cobrança com recorrência', 'Cursos online', 'Criação de contratos', 'Análise de dados', 'Gerenciamento de membros',], },
                { name: 'Semestral', price: 'R$ 510,00', month: 'R$ 85,00', features: ['Gerênciamento financeiro', 'Cobrança com recorrência', 'Cursos online', 'Criação de contratos', 'Análise de dados', 'Gerenciamento de membros',], },
                { name: 'Anual', price: 'R$ 960,00', month: 'R$ 80,00', features: ['Gerênciamento financeiro', 'Cobrança com recorrência', 'Cursos online', 'Criação de contratos', 'Análise de dados', 'Gerenciamento de membros',], },
              ].map((plan, index) => (
                <Card key={index} className="border-primary">
                  <CardHeader>
                    <CardTitle>{plan.name}</CardTitle>
                    <CardDescription>
                      <div className="flex items-baseline">
                        <span className="text-3xl font-bold">{plan.price}</span>
                      </div>
                      {plan.month && (
                          <span className="text-sm text-muted-foreground">
                          {plan.month} / mês
                        </span>
                        )}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center">
                          <CheckCircle className="h-5 w-5 text-primary mr-2" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                  <CardFooter>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </div>
        </section>
      </main>
      <footer className="flex flex-col gap-2 sm:flex-row py-6 w-full shrink-0 items-center px-4 md:px-6 border-t">
        <p className="text-xs text-gray-500 dark:text-gray-400">© 2023 Meu Mestre. Todos os direitos reservados.</p>
        <nav className="sm:ml-auto flex gap-4 sm:gap-6">
          <Link className="text-xs hover:underline underline-offset-4" href="/politica-de-privacidade">
            Política de Privacidade
          </Link>
          <Link className="text-xs hover:underline underline-offset-4" href="/termos-de-uso">
            Termos de uso
          </Link>
        </nav>
      </footer>
    </div>
  );
}