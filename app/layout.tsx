import type { Metadata, } from 'next';
import { Inter, } from 'next/font/google';
import { Toaster, } from '@/components/ui/shard/toaster';
import { config, } from '@fortawesome/fontawesome-svg-core';
import { SpeedInsights, } from '@vercel/speed-insights/next';
import '@fortawesome/fontawesome-svg-core/styles.css';
import './globals.css';

config.autoAddCss = false;
const inter = Inter({ subsets: ['latin',], });

export const metadata: Metadata = {
  title: 'Meu Mestre',
  description: 'Continue aprendendo',
};

export default function RootLayout({ children, }: Readonly<{ children: React.ReactNode; }>) {
  return (
    <html lang='en'>
      <body className={inter.className}>
        {children}
        <SpeedInsights />
        <Toaster />
      </body>
    </html>
  );
}
