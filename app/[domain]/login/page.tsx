'use client';

import { useEffect, useState, } from 'react';
import Link from 'next/link';
import { useRouter, } from 'next/navigation';
import { zodResolver, } from '@hookform/resolvers/zod';
import { useForm, } from 'react-hook-form';
import { z, } from 'zod';
import { Eye, EyeOff, } from 'lucide-react';
import { Card, } from '@/components/ui/shard/card';
import { Button, } from '@/components/ui/shard/button';
import { Input, } from '@/components/ui/shard/input';
import H1 from '@/components/ui/typography/h1';
import { useToast, } from '@/components/ui/shard/use-toast';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/shard/form';
import { Params, } from '@/utils/constants/types';
import { getSchoolName, signIn, } from './actions';

export default function SignIn({ params: { domain, }, }: Params) {
  const [unmaskPassword, setUnmaskPassword,] = useState(false);
  const [emailLoginLable, setEmailLoginLabel,] = useState('Logar');
  const [schoolName, setSchoolName,] = useState<string | null>(null);
  const { toast, } = useToast();
  const router = useRouter();

  const formSchema = z.object({
    email: z.string({ required_error: 'Campo obrigatório.', }).min(5, { message: 'No mínimo 5 letras.', }).max(50, { message: 'No máximo 50 letras.', }),
    password: z.string({ required_error: 'Campo obrigatório.', }).min(7, { message: 'Mínimo 7 digitos.', }).max(50, { message: 'No máximo 50 letras.', }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  useEffect(() => {
    (async () => {
      const response = await getSchoolName(domain);
      if (response.status === 200) {
        setSchoolName(response.data.name);
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })()
  }, []);


  const signinIn = async (formData: z.infer<typeof formSchema>) => {
    setEmailLoginLabel('Logando');
    const response = await signIn({ ...formData, domain, }) as { message?: string, status: number };
    if (response.status !== 200) {
      toast({
        variant: 'destructive',
        description: response?.message,
      });
      setEmailLoginLabel('Logar');
    } else {
      router.push('/');
    }
  };

  const PasswordMask = () => {
    if (unmaskPassword) return <EyeOff onClick={() => setUnmaskPassword(false)} className='absolute right-2 top-2 cursor-pointer' />;
    return <Eye onClick={() => setUnmaskPassword(true)} className='absolute right-2 top-2 cursor-pointer' />;
  };

  return (
    <main className='flex items-center flex-col gap-14 justify-center h-svh px-4'>
      <H1>{schoolName}</H1>
      <Form {...form}>
        <Card className='p-4 w-full sm:w-96'>
          <form onSubmit={form.handleSubmit(signinIn)} className="space-y-4 w-full">
            <FormField
              control={form.control}
              name="email"
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="m@example" {...field} type='email' />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>Senha</FormLabel>
                  <FormControl>
                    <div className='relative'>
                      <PasswordMask />
                      <Input type={`${unmaskPassword ? 'text' : 'password'}`} placeholder="******" {...field} />
                    </div>
                  </FormControl>
                  <Link href='/recuperar-senha' className='ml-auto inline-block text-sm underline'>
                      Esqueceu sua senha?
                  </Link>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className='w-full'>{emailLoginLable}</Button>
          </form>
        </Card>
      </Form>
    </main>
  );
}
