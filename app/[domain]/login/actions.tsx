'use server';

import dictionary from '@/lib/dictionary';
import { School, Subdomains, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';

type Params = {
  domain: string,
  email: string,
  password: string
}

const signIn = async ({ domain, email, password, }: Params) => {
  const supabaseAdmin = createClientAdmin();

  const { data: school, }: { data: School | null } = await supabaseAdmin
  .from('school')
  .select()
  .match({ domain, })
  .maybeSingle();

  if (!school) return {
    message: 'Não autorizado.',
  };

  const { data: profile, } = await supabaseAdmin
    .from('profile')
    .select()
    .match({ email: email.toLowerCase(), schoolId: school.id, })
    .maybeSingle();

  if (!profile) return {
    message: 'Não autorizado.',
  };

  const supabase = createClient();
  const { data: signInResponse, error, } = await supabase.auth.signInWithPassword({
    email: email,
    password: password,
  });

  if (error) {
    return {
      message: dictionary[error.code] || 'Erro ao logar usuário',
      status: error.status,
    };
  }

  if (signInResponse.user) {
    return { status: 200, };
  }
};

type Response = { data: School, status: 200 } | { message: string, status: 401 | 404 | 500 }

const getSchoolName = async (domain: Subdomains): Promise<Response> => {
  try {
    const supabase = createClientAdmin();

    const { data, error, } = await supabase
      .from('school')
      .select()
      .match({ domain, })
      .maybeSingle();

    console.log('🚀 ~ getSchoolName ~ error:', error);
    if (!data || error) return {
      message: 'Não encontramos a escola.',
      status: 404,
    };

    return { data, status: 200, };
  } catch (error) {
    const supabase = createClientAdmin();
    await supabase
      .from('error')
      .insert({ value: `getSchoolName: ${JSON.stringify(error)}`,});
    return {
      message: 'Algum erro desconhecido aconteceu.',
      status: 500,
    };
  }
};

export { getSchoolName, signIn, };