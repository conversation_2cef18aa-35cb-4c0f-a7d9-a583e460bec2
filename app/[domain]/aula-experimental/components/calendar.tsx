'use client';

import { useState, } from 'react';
import { CalendarIcon, } from 'lucide-react';
import { format, } from 'date-fns';
import { ptBR, } from 'date-fns/locale';
import { Calendar, } from '@/components/ui/shard/calendar';
import { Popover, PopoverContent, PopoverTrigger, } from '@/components/ui/shard/popover';
import { Button, } from '@/components/ui/shard/button';
import { cn, } from '@/lib/utils';


interface ScheduleCalendarProps {
  onSelectDate: (date: Date | undefined) => void
}

export function ScheduleCalendar({ onSelectDate, }: ScheduleCalendarProps) {
  const [date, setDate,] = useState<Date>();

  const handleSelect = (newDate: Date | undefined) => {
    setDate(newDate);
    onSelectDate(newDate);
  };

  return (
    <div className="flex items-center gap-2 p-4">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn('justify-start text-left font-normal', !date && 'text-muted-foreground')}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date ? format(date, 'PPP', { locale: ptBR, }) : 'Selecione uma data'}
          </Button>
        </PopoverTrigger>
        <PopoverContent side="right" align="start" className="w-auto p-0">
          <Calendar mode="single" selected={date} onSelect={handleSelect} locale={ptBR} />
        </PopoverContent>
      </Popover>
    </div>
  );
}

