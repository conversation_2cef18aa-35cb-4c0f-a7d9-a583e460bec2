'use client';

import { useEffect, useState, } from 'react';
import { cn, } from '@/lib/utils';
import { Button, } from '@/components/ui/shard/button';
import { getServicesBySport, } from '../actions';
import { toast, } from '@/components/ui/shard/use-toast';

interface Sport {
  id: string;
  name: string;
}

interface Service {
  id: string;
  title: string;
}

interface SportsSelectorProps {
  sports: Sport[];
  selectedSport: string | null;
  onSelectSport: (sportId: string) => void;
  selectedService: string | null;
  onSelectService: (serviceId: string) => void;
}

export function SportsSelector({
  sports,
  selectedSport,
  onSelectSport,
  selectedService,
  onSelectService,
}: SportsSelectorProps) {
  const [services, setServices,] = useState<Service[]>([]);

  useEffect(() => {
    async function fetchServices() {
      if (selectedSport) {
        const response = await getServicesBySport(selectedSport);

        if (response.status === 200) {
          setServices(response.data);
        } else {
          toast({
            variant: "destructive",
            title: response.message,
          });
        }
      }
    }
    fetchServices();
  }, [selectedSport,]);

  return (
    <div className="p-4">
      <div className="flex flex-wrap gap-2 mb-4">
        {sports.map((sport) => (
          <Button
            key={sport.id}
            variant={selectedSport === sport.id ? 'default' : 'secondary'}
            onClick={() => {
              onSelectSport(sport.id);
              onSelectService(null);
            }}
            className={cn(
              'rounded-full',
              selectedSport === sport.id && 'bg-black text-white'
            )}
          >
            {sport.name}
          </Button>
        ))}
      </div>

      {selectedSport && services.length > 0 && (
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-2">Escolha uma modalidade:</h3>
          <div className="flex flex-wrap gap-2">
            {services.map((service) => (
              <Button
                key={service.id}
                variant={selectedService === service.id ? 'default' : 'outline'}
                onClick={() => onSelectService(service.id)}
                className={cn(
                  'rounded-full',
                  selectedService === service.id && 'bg-blue-500 text-white'
                )}
              >
                {service.title}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
