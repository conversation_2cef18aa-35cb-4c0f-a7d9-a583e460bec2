'use client';

import { useState, } from 'react';
import { useForm, } from 'react-hook-form';
import { z, } from 'zod';
import { zodResolver, } from '@hookform/resolvers/zod';
import { createLead, sendInvite, } from '../actions';
import { Button, } from '@/components/ui/shard/button';
import { Card, CardContent, CardHeader, CardTitle, } from '@/components/ui/shard/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Input, } from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import { format, } from 'date-fns';

const formSchema = z.object({
  name: z.string().min(2, { message: 'Nome muito curto.', }).max(40, { message: 'Nome muito longo.', }),
  phone: z.string().min(10, { message: 'Número de telefone inválido.', }),
  email: z.string().email({ message: 'E-mail inválido.', }),
});

type FormData = z.infer<typeof formSchema>;

interface ClassFormProps {
  service: object,
  schoolId: string;
  day: any;
  schedule: any;
}

export function ClassForm({ service, schoolId, day, schedule, }: ClassFormProps) {
  const [isLoading, setIsLoading, ] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled, ] = useState(false);
  const [label, setLabel, ] = useState('Confirmar Agendamento');
  const formattedDay = format(new Date(day), 'dd/MM/yyyy');
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      phone: '',
      email: '',
    },
  });

  const formatPhone = (value: string) => {
    value = value.replace(/\D/g, '');
    if (value.length > 11) value = value.slice(0, 11);

    if (value.length > 10) {
      return `(${value.slice(0, 2)}) ${value.slice(2, 7)}-${value.slice(7)}`;
    } else if (value.length > 6) {
      return `(${value.slice(0, 2)}) ${value.slice(2, 6)}-${value.slice(6)}`;
    } else if (value.length > 2) {
      return `(${value.slice(0, 2)}) ${value.slice(2)}`;
    } else {
      return value;
    }
  };

  const onSubmit = async (formData: FormData) => {
    if (!schoolId) {
      toast({
        variant: 'destructive',
        title: 'Erro ao processar cadastro',
        description: 'ID da escola não encontrado.',
      });

      setIsButtonDisabled(false);
      return;
    }

    setIsLoading(true);
    try {
      await createLead(formData.name, formData.email, formData.phone, schoolId);
      toast({ description: 'Agendamento realizado com sucesso!', });
      await sendInvite( formData.name, formData.email, formattedDay, schedule, service);
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Erro ao cadastrar',
        description: 'Tente novamente mais tarde.',
      });
    } finally {
      setIsLoading(false);
      setIsButtonDisabled(true);
      setLabel('Agendamento confirmado!');
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-lg">Agendar Aula Experimental</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>Nome</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Seu nome completo" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phone"
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>Telefone</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="(XX) XXXXX-XXXX"
                      value={formatPhone(field.value)}
                      onChange={(e) => field.onChange(formatPhone(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>E-mail</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="<EMAIL>" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full" disabled={isLoading || isButtonDisabled}>
              {isLoading ? 'Cadastrando...' : label}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
