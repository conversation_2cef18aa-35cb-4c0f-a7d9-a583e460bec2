'use client';

import { useState, } from 'react';
import { Button, } from '@/components/ui/shard/button';
import { Card, CardContent, CardHeader, CardTitle, } from '@/components/ui/shard/card';
import { cn, } from '@/lib/utils';

interface Schedule {
  id: string;
  hour: string;
}

interface Service {
  id: string;
  title: string;
  sportId: string;
}

interface ScheduleListProps {
  onSelectSchedule: any;
  services: Service[];
  schedules: Schedule[];
  schoolId: string;
  setShowForm: (value: boolean) => void;
}

export function ScheduleList({ services, schedules, setShowForm, onSelectSchedule, }: ScheduleListProps) {
  const [selectedSchedule, setSelectedSchedule,] = useState<string | null>(null);

  const handleSchedule = (schedule: Schedule) => {
    setSelectedSchedule(schedule.id);
    onSelectSchedule(schedule);
    setShowForm(true);
  };

  return (
    <div className="space-y-4 p-4">
      {services.map((service) => (
        <Card key={service.id}>
          <CardHeader>
            <CardTitle className="text-lg">{service.title}</CardTitle>
          </CardHeader>
          <CardContent>
            {schedules.map((schedule) => {
              const isSelected = schedule.id === selectedSchedule;
              return (
                <div
                  key={schedule.id}
                  className={`flex items-center justify-between mt-2 p-2 rounded-lg transition-all
                  ${isSelected ? 'bg-blue-100 border border-blue-500' : 'bg-white'}`}
                >
                  <span className="text-gray-600">{schedule.hour}</span>
                  <Button
                    onClick={() => handleSchedule(schedule)}
                    variant={isSelected ? 'default' : 'outline'}
                    className={cn(isSelected && 'bg-blue-500 text-white')}
                  >
                    {isSelected ? 'Selecionado' : 'Selecionar'}
                  </Button>
                </div>
              );
            })}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
