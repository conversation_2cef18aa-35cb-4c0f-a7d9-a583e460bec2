'use client';

import { useEffect, useState, } from 'react';
import { useParams, } from 'next/navigation';
import { getSchool, getSportsBySchool, getServicesBySport, getSchedulesByService, } from './actions';
import { SportsSelector, } from './components/sports-selector';
import { ScheduleCalendar, } from './components/calendar';
import { ScheduleList, } from './components/schedule-list';
import { ClassForm, } from './components/form';
import { toast, } from '@/components/ui/shard/use-toast';

interface Sport {
  id: string;
  name: string;
}

interface Service {
  id: string;
  title: string;
  sportId: string;
}

export default function SchedulingPage() {
  const { domain, } = useParams();
  const [school, setSchool,] = useState<any>(null);
  const [sports, setSports,] = useState<Sport[]>([]);
  const [selectedSport, setSelectedSport,] = useState<string | null>(null);
  const [selectedService, setSelectedService,] = useState<string | null>(null);
  const [selectedDate, setSelectedDate,] = useState<Date>();
  const [selectedSchedule, setSelectedSchedule, ] = useState<any>();
  const [services, setServices,] = useState<Service[]>([]);
  const [schedules, setSchedules,] = useState([]);
  const [showForm, setShowForm,] = useState(false);

  useEffect(() => {
    const fetchSchool = async () => {
      if (typeof domain === 'string') {
        const response = await getSchool(domain);
        if (response.status === 200){
          setSchool(response.data);
        } else {
          toast({
            variant: 'destructive',
            title: response.message,
          });
        }
      }
    };
    fetchSchool();
  }, [domain,]);

  useEffect(() => {
    const fetchSports = async () => {
      if (school?.id) {
        const response = await getSportsBySchool(school.id);

        if (response.status === 200) {
          setSports(response.data);
        } else {
          toast({
            variant: 'destructive',
            title: response.message,
          });
        }
      }
    };
    fetchSports();
  }, [school,]);

  useEffect(() => {
    const fetchServices = async () => {
      if (selectedSport) {
        const response = await getServicesBySport(selectedSport);

        if (response.status === 200) {
          setServices(response.data);
          setSelectedService(null);
        } else {
          toast({
            variant: 'destructive',
            title: response.message,
          });
        }
      }
    };
    fetchServices();
  }, [selectedSport,]);

  useEffect(() => {
    const fetchSchedules = async () => {
      if (selectedService && selectedDate) {
        const dayOfWeek = selectedDate.getDay();
        const response = await getSchedulesByService(selectedService, dayOfWeek);

        if (response.status === 200) {
          setSchedules(response.data);
        } else {
          toast({
            variant: 'destructive',
            title: response.message,
          });
        }
      }
    };
    fetchSchedules();
  }, [selectedService, selectedDate,]);

  if (!school) return <p>Carregando...</p>;

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="container mx-auto max-w-3xl">
        <div className="my-8 text-center">
          <h2 className="text-2xl font-bold">Seja bem-vindo a Escola {school.name}</h2>
          <p className="mt-2 text-gray-600">Agende sua aula experimental!</p>
        </div>

        <div className="bg-white rounded-lg shadow-sm">
          <h3 className="p-4 text-lg font-semibold">Escolha um esporte:</h3>

          <SportsSelector
            sports={sports}
            selectedSport={selectedSport}
            onSelectSport={setSelectedSport}
            selectedService={selectedService}
            onSelectService={setSelectedService}
          />

          {selectedService && (
            <>
              <ScheduleCalendar onSelectDate={setSelectedDate} />
              {selectedDate && (
                <ScheduleList
                  onSelectSchedule={setSelectedSchedule}
                  services={services.filter(service => service.id === selectedService)}
                  schedules={schedules}
                  schoolId={school.id}
                  setShowForm={setShowForm}
                />
              )}
              {showForm &&
              <ClassForm
                schoolId={school.id}
                service={services.find(service => service.id === selectedService) || {}}
                day={selectedDate}
                schedule={selectedSchedule}
              />}
            </>
          )}
        </div>
      </main>
    </div>
  );
}
