'use server';

import { createClientAdmin, } from '@/utils/supabase/server';
import { MISSING_PARAMS, } from '@/utils/constants';
import { School, } from '@/utils/constants/types';
import { PostgrestMaybeSingleResponse, } from '@supabase/supabase-js';
import { userInfo, } from '@/supabase/verifications/user-info';

const supabaseAdmin = createClientAdmin();


export const sendInvite = async (name: string, email: string, day: string, schedule: any, service: object, ) => {
     const user = await userInfo();

    if (!name || !email) return {
      message: MISSING_PARAMS, status: 400,
    };
      const { data: school, }: PostgrestMaybeSingleResponse<School> = await supabaseAdmin
        .from('school')
        .select()
        .match({ id: user?.user_metadata.schoolId, })
        .maybeSingle();

      if (!school) return {
        message: 'Escola não encontrada.',
        status: 400,
      };

    const emailResponse = await supabaseAdmin.functions.invoke('book_experimental', {
      body: { day, email, schedule, service, school, name, },
    });

    if (emailResponse.error) console.log(emailResponse.error);

    if (emailResponse.error) return {
      message: 'Algum erro aconteceu ao enviar convite.',
      status: 400,
    };
    return { status: 200, };
};


export async function getSchool(domain: string) {
  const { data, error, } = await supabaseAdmin
    .from('school')
    .select()
    .eq('domain', domain)
    .maybeSingle();

  if (error) {
    return { message: 'Erro ao buscar escola.', status: 400, };
  }

  return { data, status: 200, };
}

export async function getSportsBySchool(schoolId: string) {
  const { data, error, } = await supabaseAdmin
    .from('sport_school')
    .select('sport(id, name)')
    .eq('schoolId', schoolId);

  if (error) {
    return { message: 'Erro ao buscar esportes.', status: 400, };
  }

  return {
    data: data.map(s => ({ id: s.sport.id, name: s.sport.name, })),
    status: 200,
  };
}

export async function getServicesBySport(sportId: string) {
  const { data, error, } = await supabaseAdmin
    .from('service')
    .select()
    .eq('sportId', sportId);

  if (error) {
    return { message: 'Erro ao buscar serviços.', status: 400, };
  }

  return { data, status: 200, };
}

export async function getSchedulesByService(serviceId: string, dayOfWeek: number) {
  const { data, error, } = await supabaseAdmin
    .from('schedule')
    .select()
    .eq('serviceId', serviceId)
    .eq('number', dayOfWeek);

  if (error) {
    return { message: 'Erro ao buscar horários.', status: 400, };
  }

  return { data, status: 200, };
}

export async function createLead(name: string, email: string, phone: string, schoolId: string) {
  const { data, error, } = await supabaseAdmin
    .from('lead')
    .insert([{ name, email, phone, schoolId, },]);

  if (error) {
    return { message: 'Erro ao criar lead.', status: 400, };
  }

  return { data, status: 200, };
}
