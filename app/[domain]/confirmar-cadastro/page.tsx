'use client';

import {
  <PERSON><PERSON>,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/index';
import { useSearch<PERSON>ara<PERSON>, useRouter, } from 'next/navigation';
import type { EmailOtpType, } from '@supabase/supabase-js';
import { createAsaasAccount, getSchoolSports, saveSport, sendEmailNewUser, verifyUser, } from './actions';
import { toast, } from '@/components/ui/shard/use-toast';
import { type ChangeEvent, useEffect, useState, } from 'react';
import type { Rank, Sport, } from '@/utils/constants/types';
import { getRanksBySport, } from '@/app/actions';
import { z, } from 'zod';
import { handleCpfChange, validateCPF, } from '@/lib/utils';
import { requiredMessage, } from '@/utils/supabase/constants';
import { useForm, useFieldArray, } from 'react-hook-form';
import { zodResolver, } from '@hookform/resolvers/zod';
import useNewPasswordForm from '@/hooks/use-new-password';
import { createProfile, } from '../registrar/actions';
import { Plus, Trash2, } from 'lucide-react';

const sportEntrySchema = z.object({
  sportId: z.string({ required_error: requiredMessage, }),
  rankId: z.string().optional(),
});

const formSchema = z.object({
  cpf: z
    .string({ required_error: requiredMessage, })
    .regex(/^(\d{3}\.\d{3}\.\d{3}-\d{2})|^$/, 'Formato inválido.')
    .refine(validateCPF, { message: 'CPF inválido.', }),
  sports: z.array(sportEntrySchema).min(1, 'Selecione pelo menos um esporte'),
});

type FormValues = z.infer<typeof formSchema>

export default function ChangePassword({ params: { domain, }, }: { params: { domain: string } }) {
  const [label, setLabel,] = useState('Confirmar cadastro');
  const [sports, setSports,] = useState<Sport[] | null>(null);
  const [ranksByField, setRanksByField,] = useState<{ [key: number]: Rank[] }>({});
  const searchParams = useSearchParams();
  const router = useRouter();
  const newPassword = useNewPasswordForm();
  const token_hash = searchParams.get('token_hash');
  const type = searchParams.get('type') as EmailOtpType | null;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      sports: [{ sportId: '', rankId: '', },],
    },
  });

  const { fields, append, remove, } = useFieldArray({
    control: form.control,
    name: 'sports',
  });

  const loadRanksForSport = async (sportId: string, index: number) => {
    if (!sportId) return;
    const response = await getRanksBySport({ sportId, });
    if (response.status === 200) {
      setRanksByField((prev) => ({
        ...prev,
        [index]: response.data,
      }));
    } else {
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
  };

  const confirmAccount = async (formData: FormValues) => {
    setLabel('Confirmando cadastro');
    const isPasswordValid = await newPassword.form.trigger(['password', 'passwordConfirmation',]);

    if (!isPasswordValid) {
      setLabel('Confirmar cadastro');
      return;
    }

    const verifiedUser = await verifyUser({ tokenHash: token_hash, type, });

    if (verifiedUser.status !== 200) {
      setLabel('Confirmar cadastro');
      toast({
        variant: 'destructive',
        title: verifiedUser.message,
      });
      return;
    }

    const authUser = verifiedUser.data;

    const createAsaasAccountResponse = await createAsaasAccount({
      cpf: formData.cpf,
      domain,
      profileId: authUser.id,
      name: authUser.user_metadata.name,
    });

    const profileResponse = await createProfile({
      profileId: authUser.id,
      formData: {
        id: authUser.id,
        status: 'active',
        cpf: formData.cpf,
        asId: createAsaasAccountResponse?.data?.id,
        type: authUser.user_metadata.role,
        schoolId: authUser.user_metadata.schoolId,
        name: authUser.user_metadata.name,
        email: authUser.user_metadata.email,
      },
    });

    if (profileResponse.status !== 200) {
      toast({
        variant: 'destructive',
        title: profileResponse.message,
      });
      return;
    }

    await newPassword.updatePassword();

    // Save all sports
    for (const sport of formData.sports) {
      await saveSport({
        profileId: authUser.id,
        sportId: sport.sportId,
        rankId: sport.rankId,
      });
    }

    await sendEmailNewUser({ profileId: authUser.id, });
    router.push('/cursos');
  };

  const onCpfChange = (e: ChangeEvent<HTMLInputElement>) => {
    const formattedValue = handleCpfChange(e.target.value);
    form.setValue('cpf', formattedValue, { shouldValidate: true, });
  };

  useEffect(() => {
    (async () => {
      const response = await getSchoolSports({ domain, });
      if (response.status === 200) {
        setSports(response.data);
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })();
  }, [domain,]);

  return (
    <Dialog open>
      <DialogContent className="flex flex-col justify-center w-full px-4">
        <DialogHeader>
          <DialogTitle>Informações obrigatórias</DialogTitle>
          <DialogDescription>Pra concluir o cadastro, essas informações são necessárias.</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(confirmAccount)} className="flex flex-col gap-4 w-full">
            <FormField
              control={form.control}
              name="cpf"
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>CPF</FormLabel>
                  <FormControl>
                    <Input id="cpf" placeholder="000.000.000-00" {...field} onChange={onCpfChange} maxLength={14} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={newPassword.form.control}
              name="password"
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>Senha</FormLabel>
                  <FormControl>
                    <Input type="password" {...field} placeholder="*******" />
                  </FormControl>
                  <FormMessage>{newPassword.errors.password}</FormMessage>
                </FormItem>
              )}
            />
            <FormField
              control={newPassword.form.control}
              name="passwordConfirmation"
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>Confirmar senha</FormLabel>
                  <FormControl>
                    <Input type="password" {...field} placeholder="*******" />
                  </FormControl>
                  <FormMessage>{newPassword.errors.passwordConfirmation}</FormMessage>
                </FormItem>
              )}
            />

            <div className="space-y-4">
              {fields.map((field, index) => (
                <div key={field.id} className="flex gap-4 items-end">
                  <div className="flex flex-row justify-between items-center gap-4 w-full">
                    <FormField
                      control={form.control}
                      name={`sports.${index}.sportId`}
                      render={({ field, }) => (
                        <FormItem className='flex-1'>
                          <FormLabel>Esporte</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              loadRanksForSport(value, index);
                            }}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecionar" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {sports?.map((sport) => (
                                <SelectItem key={sport.id} value={sport.id}>
                                  {sport.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {ranksByField[index]?.length > 0 && (
                      <FormField
                        control={form.control}
                        name={`sports.${index}.rankId`}
                        render={({ field, }) => (
                          <FormItem className='flex-1'>
                            <FormLabel>Graduação</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Nível" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {ranksByField[index]?.map((rank) => (
                                  <SelectItem key={rank.id} value={rank.id}>
                                    {rank.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                    {fields.length > 1 && (
                      <Button type="button" variant="ghost" size="icon" className='mt-5' onClick={() => remove(index)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <Button
              type="button"
              variant="outline"
              className="mt-2"
              onClick={() => append({ sportId: '', rankId: '', })}
            >
              <Plus className="h-4 w-4 mr-2" />
              Adicionar outro esporte
            </Button>

            <Button type="submit" className="mt-4">
              {label}
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

