'use server';

import { PostgrestMaybeSingleResponse, User, type EmailOtpType, } from '@supabase/supabase-js';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { MISSING_PARAMS, } from '@/utils/constants';
import { Profile, RankStudent, School, Sport, SportProfile, } from '@/utils/constants/types';

type Props = { tokenHash?: string | null, type: EmailOtpType | null }

type Response = {
  data: User,
  status: 200
} | {
  message: string,
  status: 400
}

type SaveSportResponse = {
  status: 200
} | {
  message: string,
  status: 400
}

type SaveSportProps = {
  profileId: string,
  sportId?: string,
  rankId?: string
}

type ErrorCodes = {
  message: string,
  status: 400 | 401
}

type GetSchoolSportsResponse = {
  data: Sport[]
  status: 200
} | ErrorCodes

export const verifyUser = async ({ tokenHash, type, }: Props): Promise<Response> => {
  if (!tokenHash || !type) return { message: MISSING_PARAMS, status: 400, };

  const supabase = createClient();
  const { data: { user, }, error, } = await supabase.auth.verifyOtp({
    type,
    token_hash: tokenHash,
  });
  console.log('🚀 ~ GET ~ user:', user);
  console.log('🚀 ~ GET ~ error:', error);

  if (error || !user) return { message: 'Token expirado ou inválido, peça outro convite ao professor.', status: 400, };
  return { data: user, status: 200, };
};

export const saveSport = async ({ profileId, sportId, rankId, }: SaveSportProps): Promise<SaveSportResponse> => {
  const supabaseAdmin = createClientAdmin();
  const { data: profile, error, }: PostgrestMaybeSingleResponse<Profile> = await supabaseAdmin
  .from('profile')
  .select()
  .match({ id: profileId, })
  .maybeSingle();

  console.log('🚀 ~ saveSport ~ error:', error);
  console.log('🚀 ~ saveSport ~ profile:', profile);
  if (!profile) return { message: 'Erro ao buscar usuário.', status: 400, };

  if (sportId) {
    const { data, error, }: PostgrestMaybeSingleResponse<SportProfile> = await supabaseAdmin
    .from('sport_profile')
    .insert({
      profileId,
      sportId,
    })
    .select()
    .maybeSingle();

    console.log('🚀 ~ saveSport ~ error:', error);
    if (!data) return { message: 'Erro ao salvar esporte.', status: 400, };
  }
  if (rankId) {
    const { data, }: PostgrestMaybeSingleResponse<RankStudent> =  await supabaseAdmin
      .from('rank_student')
      .insert({
        profileId,
        rankId,
        sportId,
      });

    if (!data) return { message: 'Erro ao salvar grau.', status: 400, };
  }
  return { status: 200, };
};

export const sendEmailNewUser = async ({ profileId, }: { profileId: string }) => {
  const supabaseAdmin = createClientAdmin();
  await supabaseAdmin.functions.invoke('new_active_user', {
    body: {
      userId: profileId,
    },
  });
};

export const getSchoolSports = async ({ domain, }: { domain: string }): Promise<GetSchoolSportsResponse> => {
  const supabaseAdmin = createClientAdmin();
  const { data: school, }: PostgrestMaybeSingleResponse<School> = await supabaseAdmin
    .from('school')
    .select()
    .match({ domain, })
    .maybeSingle();

  if (!school) return { message: 'Erro ao buscar escola.', status: 400, };

  const { data, } = await supabaseAdmin
    .from('sport_school')
    .select('sport(*)')
    .match({ schoolId: school.id, })
    .returns<{ sport: Sport }[]>();

  if (!data) return { message: 'Erro ao buscar esportes.', status: 400, };

  if (!data.length) return { message: 'Escola não tem esporte cadastrado.', status: 400, };
  const sports = data.map(schoolSport => schoolSport.sport);
  console.log('🚀 ~ getSchoolSports ~ sports:', sports);
  return { data: sports, status: 200, };
};

type CreateAccountParams = {
  cpf: string,
  domain: string,
  profileId: string,
  name: string
};

type CreateAccountResponse = { data: { id: string }, status: 200 } | { message: string, status: 400 }

export const createAsaasAccount = async (formData: CreateAccountParams): Promise<CreateAccountResponse> => {
  const tokenName = `AS_API_KEY_${formData.domain}`;
  const token = process.env[tokenName];
  const headers = {
    access_token: token,
    'User-Agent': 'meu-mestre',
  };

  const response = await fetch('https://api.asaas.com/v3/customers', {
    body: JSON.stringify({
      cpfCnpj: formData.cpf,
      name: formData.name,
      notificationDisabled: true,
    }),
    headers,
    method: 'POST',
  });
  console.log('🚀 ~ createAsaasAccount ~ response:', response);

  if (response.status !== 200) return {
    message: 'Erro ao criar conta no ASAAS.',
    status: 400,
  };

  const data = await response.json();
  return { data, status: 200, };
};