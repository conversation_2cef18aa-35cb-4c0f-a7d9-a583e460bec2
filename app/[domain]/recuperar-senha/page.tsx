'use client';

import { useState, } from 'react';
import {
  <PERSON>ton,
  Card,
  CardDescription,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  useToast,
} from '@/components/index';
import { zodResolver, } from '@hookform/resolvers/zod';
import { useForm, } from 'react-hook-form';
import { z, } from 'zod';
import { createClient, } from '@/utils/supabase/client';
import H1 from '@/components/ui/typography/h1';
import { useRouter, } from 'next/navigation';
import { Subdomains, } from '@/utils/constants/types';

export default function ChangePassword({ params: { domain, }, }: { params: { domain: Subdomains } }) {
  const [sendButtonLabel, setSendButtonLabel,] = useState('Enviar');
  const { toast, } = useToast();
  const route = useRouter();
  const supabase = createClient();

  const formSchema = z.object({
    email: z.string().email({ message: 'Email inválido', }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  const resetPassword = async ({ email, }: z.infer<typeof formSchema>) => {
    setSendButtonLabel('Enviando');
    await supabase.auth.resetPasswordForEmail(
      email,
      {
        redirectTo: `https://${domain}.meumestre.com.br`,
      }
    );
    toast({
      title: 'Email enviado.',
    });
    route.push('/login');
  };

  return (
    <main className='flex items-center flex-col gap-14 justify-center h-screen'>
      <H1>Nova Senha</H1>
      <Form {...form}>
        <Card className='p-4'>
          <CardDescription>Enviaremos um link pro seu email pra configurar uma nova senha.</CardDescription>
          <form onSubmit={form.handleSubmit(resetPassword)} className="space-y-4 w-full mt-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="m@example" type='email' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className='w-full'>{sendButtonLabel}</Button>
          </form>
        </Card>
      </Form>
    </main>
  );
}
