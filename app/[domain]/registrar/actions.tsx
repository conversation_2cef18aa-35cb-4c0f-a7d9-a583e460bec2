'use server';

import dictionary from '@/lib/dictionary';
import { Profile, School, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestMaybeSingleResponse, Session, User, } from '@supabase/supabase-js';

type Params = {
  domain: string,
  email: string,
  password: string
}

export const signIn = async ({ domain, email, password, }: Params) => {
  const supabaseAdmin = createClientAdmin();

  const { data: school, }: { data: School | null } = await supabaseAdmin
  .from('school')
  .select()
  .match({ domain, })
  .maybeSingle();

  if (!school) return {
    message: 'Não autorizado.',
  };

  const { data: profile, } = await supabaseAdmin
    .from('profile')
    .select()
    .match({ email: email.toLowerCase(), schoolId: school.id, })
    .maybeSingle();

  if (!profile) return {
    message: 'Não autorizado.',
  };

  const supabase = createClient();
  const { data: signInResponse, error, } = await supabase.auth.signInWithPassword({
    email: email,
    password: password,
  });

  if (error) {
    return {
      message: dictionary[error.code] || 'Erro ao logar usuário',
      status: error.status,
    };
  }

  if (signInResponse.user) {
    return { status: 200, };
  }
};


type FormData = {
  name: string,
  email: string,
  schoolId: string,
  role?: 'student' | 'teacher'
}
export const signUp = async (formData: FormData, domain: string): Promise<{ status: 200 } | { message: string, status: 400 }> => {
  const supabase = createClient();
  const supabaseAdmin = createClientAdmin();
  const { data: userExists, } = await supabaseAdmin
    .from('profile')
    .select()
    .match({ email: formData.email, })
    .maybeSingle();

  if (userExists) return {
    message: 'Usuário já existe.',
    status: 400,
  };

  const { error, } = await supabase.auth.signInWithOtp(
    {
      email: formData.email,
      options: {
        data: {
          name: formData.name,
          schoolId: formData.schoolId,
          role: formData.role || 'student',
        },
        emailRedirectTo: `https://${domain}.meumestre.com.br`,
      },
    }
  );
  if (error) return {
    message: 'Algum erro aconteceu ao registrar usuário.',
    status: 400,
  };

  return { status: 200, };
};

type CreateProfileProps = {
  formData: {
    type?: 'student' | 'teacher',
    schoolId: string,
    name: string,
    email: string,
    id: string,
    status: 'pending' | 'active',
  }
}
type CreateProfileResponse = { data: Profile, status: 200 } | { message: string, status: 400 }
export const createProfile = async ({ formData, }: CreateProfileProps): Promise<CreateProfileResponse> => {
  const supabaseAdmin = createClientAdmin();
  const { data: newUser, error, }: PostgrestMaybeSingleResponse<Profile> = await supabaseAdmin
    .from('profile')
    .insert(formData)
    .select()
    .maybeSingle();

  if (!newUser) {
    return { message: dictionary[error.message] || 'Não foi possível criar usuário', status: 400, };
  }

  return { data: newUser, status: 200, };
};

type Response = { data: School, status: 200 } | { message: string, status: 401 | 404 | 500 }

export const getSchoolName = async (domain: string): Promise<Response> => {
  try {
    const supabase = createClientAdmin();

    const { data, error, } = await supabase
      .from('school')
      .select()
      .match({ domain, })
      .maybeSingle();

    if (!data || error) return {
      message: 'Não encontramos a escola.',
      status: 404,
    };

    return { data, status: 200, };
  } catch (error) {
    const supabase = createClientAdmin();
    await supabase
      .from('error')
      .insert({ value: `getSchoolName: ${JSON.stringify(error)}`,});
    return {
      message: 'Algum erro desconhecido aconteceu.',
      status: 500,
    };
  }
};
