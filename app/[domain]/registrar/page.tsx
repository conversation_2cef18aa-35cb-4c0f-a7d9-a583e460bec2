'use client';

import {
  <PERSON><PERSON>,
  Card,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
} from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import { useEffect, useState, } from 'react';
import { getSchoolByDomain, } from '@/app/actions';
import { z, } from 'zod';
import { requiredMessage, } from '@/utils/supabase/constants';
import { useForm, } from 'react-hook-form';
import { zodResolver, } from '@hookform/resolvers/zod';
import H1 from '@/components/ui/typography/h1';
import { getSchoolName, signUp, } from './actions';
import { useSearchParams, } from 'next/navigation';

const SignUp = ({ params: { domain, }, }: { params: { domain: string } }) => {
  const [label, setLabel,] = useState('Verificar email');
  const [schoolName, setSchoolName,] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting,] = useState(false);
  const searchParams = useSearchParams();

  const formSchema = z.object({
    email: z.string({ required_error: requiredMessage, })
      .trim()
      .email()
      .max(50, 'Email precisa ter menos de 50 letras.'),
    name: z.string({ required_error: requiredMessage, })
      .trim()
      .max(100, 'Nome precisa ter menos que 100 letras.')
      .refine(value => /\s/.test(value), {
        message: 'Sobrenome é necessário.',
      }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: searchParams.get('name'),
      email: searchParams.get('email'),
    },
  });

  const confirmAccount = async (formData: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    setLabel('Enviando email');
    const schoolByDomainResponse = await getSchoolByDomain({ domain, });
    if (schoolByDomainResponse.status !== 200) {
      toast({
        variant: 'destructive',
        title: schoolByDomainResponse.message,
      });
      return null;
    }

    const school = schoolByDomainResponse.data;

    const signUpResponse = await signUp({
      ...formData,
      role: searchParams.get('type') as 'student' | 'teacher',
      schoolId: school.id,
    }, domain);

    if (signUpResponse.status !== 200) {
      toast({
        variant: 'destructive',
        title: signUpResponse.message,
      });
      return null;
    }

    toast({
      title: 'Convite enviado ao email.',
    });
    setLabel('Verificar email');
  };

  useEffect(() => {
    (async () => {
      const response = await getSchoolName(domain);
      if (response.status === 200) {
        setSchoolName(response.data.name);
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })();
  }, []);

  return (
    <main className='flex items-center flex-col gap-14 justify-center h-svh px-4'>
      <H1>{schoolName}</H1>
      <Form {...form}>
        <Card className='p-4 w-full sm:w-96'>
          <form onSubmit={form.handleSubmit(confirmAccount)} className='space-y-4 w-full'>
            <FormField
              control={form.control}
              name='name'
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>Nome e Sobrenome</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder='João da Silva'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='email'
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder='m@example' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type='submit' disabled={isSubmitting}>{label}</Button>
          </form>
        </Card>
      </Form>
    </main>
  );
};

export default SignUp;