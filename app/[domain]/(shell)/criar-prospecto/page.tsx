'use client';

import {
  <PERSON><PERSON>,
  Card,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
} from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import { ChangeEvent, useState, } from 'react';
import { z, } from 'zod';
import { requiredMessage, } from '@/utils/supabase/constants';
import { useForm, } from 'react-hook-form';
import { zodResolver, } from '@hookform/resolvers/zod';
import { formatPhoneNumber, } from '@/lib/utils';
import { saveProspect, } from './actions';
import { H2, } from '@/components/ui/typography';

const Lead = () => {
  const [label, setLabel,] = useState('Salvar prospecto');
  const [isSubmitting, setIsSubmitting,] = useState(false);

  const formSchema = z.object({
    name: z.string({ required_error: requiredMessage, })
      .trim()
      .max(100, 'Nome precisa ter menos que 100 letras.')
      .refine(value => /\s/.test(value), {
        message: 'Sobrenome é necessário.',
      }),
    phone: z.string().min(14, { message: 'Telefone deve ter pelo menos 14 caracteres.', })
    .optional(),
    email: z.string()
      .email()
      .max(50, 'Email precisa ter menos que 50 letras.')
      .optional()
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });

  const createProspect = async (formData: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    setLabel('Salvando prospecto');
    const saveProspectResponse = await saveProspect(formData);
    if (saveProspectResponse.status !== 200) {
      toast({
        variant: 'destructive',
        title: saveProspectResponse.message,
      });
      return null;
    } else {
      toast({
        title: 'Prospecto salvo com sucesso.',
      });
    }
  };

  const handlePhoneChange = (e: ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    form.setValue('phone', formatted, { shouldValidate: true, });
  };

  return (
    <div className='flex flex-col gap-10 items-center justify-center w-full'>
      <H2>Cadastrar possível cliente</H2>
      <Form {...form}>
        <Card className='p-4 w-full sm:w-96'>
          <form onSubmit={form.handleSubmit(createProspect)} className='space-y-4 w-full'>
            <FormField
              control={form.control}
              name='name'
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>Nome e Sobrenome</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder='João da Silva'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='phone'
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>Telefone</FormLabel>
                  <FormControl>
                  <Input
                    id='phone'
                    type='phone'
                    {...field}
                    onChange={handlePhoneChange}
                    placeholder='(00) 00000-0000'
                  />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='email'
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder='m@example' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type='submit' disabled={isSubmitting}>{label}</Button>
          </form>
        </Card>
      </Form>
    </div>
  );
};

export default Lead;