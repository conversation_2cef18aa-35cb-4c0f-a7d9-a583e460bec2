'use server';

import { userInfo, } from '@/supabase/verifications/user-info';
import { createClientAdmin, } from '@/utils/supabase/server';

export const saveProspect = async ({ name, email, phone, }: { name: string, email?: string, phone: string }): Promise<{ status: 200 } | { message: string, status: 400 }> => {
  const user = await userInfo();
  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
    .from('lead')
    .insert({
      name,
      email,
      phone,
      schoolId: user?.user_metadata.schoolId,
    })
    .select();
    console.log("🚀 ~ saveProspect ~ error:", error)

  if (error) return {
    message: 'Erro ao salvar prospecto.',
    status: 400,
  };
  return { status: 200, };
};