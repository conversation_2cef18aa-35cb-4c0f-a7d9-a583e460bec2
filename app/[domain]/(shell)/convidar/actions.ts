'use server';

import { userInfo, } from '@/supabase/verifications/user-info';
import { MISSING_PARAMS, UNAUTHORIZED, } from '@/utils/constants';
import { School, Subdomains, } from '@/utils/constants/types';
import { createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestMaybeSingleResponse, } from '@supabase/supabase-js';

type FormData = {
  domain: Subdomains,
  email: string,
  name: string,
  type: 'student' | 'teacher',
  sportId?: string,
  rankId?: string,
}

export const sendInvite = async (formData: FormData) => {
  if (!formData.name || !formData.email || !formData.type) return {
    message: MISSING_PARAMS, status: 400,
  };

  const user = await userInfo();

  if (user?.user_metadata.role !== 'admin') return {
    message: UNAUTHORIZED, status: 401,
  };

  const supabaseAdmin = createClientAdmin();
  const { data: school, }: PostgrestMaybeSingleResponse<School> = await supabaseAdmin
    .from('school')
    .select()
    .match({ id: user.user_metadata.schoolId, })
    .maybeSingle();

  if (!school) return {
    message: 'Escola não encontrada.',
    status: 400,
  };

  const { data: userExists, } = await supabaseAdmin
    .from('profile')
    .select()
    .match({ email: formData.email, })
    .maybeSingle();

  if (userExists) return {
    message: 'Usuário já existe.',
    status: 400,
  };

  const emailResponse = await supabaseAdmin.functions.invoke('invite_user', {
    body: {
      name: formData.name,
      email: formData.email,
      type: formData.type,
      school,
    },
  });

  if (emailResponse.error) return {
    message: 'Algum erro aconteceu ao enviar convite.',
    status: 400,
  };
  return { status: 200, };
};
