'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  useToast,
} from '@/components/index';
import { zodResolver, } from '@hookform/resolvers/zod';
import { useState, } from 'react';
import { useForm, } from 'react-hook-form';
import { z, } from 'zod';
import { Subdomains, } from '@/utils/constants/types';
import { requiredMessage, } from '@/utils/supabase/constants';
import { sendInvite, } from './actions';

const Invite = ({ params: { domain, }, }: { params: { domain: Subdomains } }) => {
  const [emailLoginLabel, setEmailLoginLabel,] = useState('Enviar');
  const [isDisabled, setIsDisabled,] = useState(false);
  const { register, } = useForm();
  const { toast, } = useToast();

  const formSchema = z.object({
    email: z.string({ required_error: requiredMessage, })
      .email()
      .max(50, { message: 'No máximo 50 letras.', }),
    name: z.string({ required_error: requiredMessage, })
      .trim()
      .max(100, 'Nome precisa ter menos que 100 letras.')
      .refine(value => /\s/.test(value), {
        message: 'Sobrenome é necessário.',
      }),
    type: z.enum(['student', 'teacher',], { required_error: requiredMessage, }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      name: '',
      type: 'student',
    },
  });

  const submit = async (formData: z.infer<typeof formSchema>) => {
    setEmailLoginLabel('Enviando');
    const response = await sendInvite({
      domain: domain,
      email: formData.email,
      name: formData.name,
      type: formData.type,
    });
    setEmailLoginLabel('Enviado');
    if (response.status === 200) {
      toast({
        title: 'Convite enviado com sucesso.',
      });
      setIsDisabled(true);
    }
    else {
      toast({
        variant: 'destructive',
        title: response.message,
      });
      setEmailLoginLabel('Enviar');
    }
  };

  function cleanForm() {
    setIsDisabled(!isDisabled);
    setEmailLoginLabel('Enviar');
    form.reset();
  }


  return (
    <Card className='w-full p-4 h-full'>
      <CardHeader>
        <CardTitle>Convidar aluno ou professor</CardTitle>
        <CardDescription>Enviaremos um email pra pessoa pra que ele se registre.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(submit)}  className="flex flex-col gap-4 w-full">
            <FormField
            {...register('' ,{disabled : isDisabled,})}
              control={form.control}
              name="name"
              render={({ field, }) => (
                <FormItem  className='flex-[3_3_0%]'>
                  <FormControl>
                    <Input placeholder="João da Silva" {...field} type='text' />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              {...register('' ,{disabled : isDisabled,})}
              control={form.control}
              name="email"
              render={({ field, }) => (
                <FormItem className='flex-[3_3_0%]'>
                  <FormControl>
                    <Input placeholder="m@example" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='type'
              render={({ field, }) => (
                <FormItem>
                  <FormControl>
                    <Select onValueChange={field.onChange} >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder='Tipo de usuário' />
                      </SelectTrigger>
                      <SelectContent>
                        {[{ id: 'student', label: 'Aluno', }, { id: 'teacher', label: 'Professor', },].map(type => (
                          <SelectItem key={type.id} value={type.id}>{type.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {!isDisabled &&
              <Button className='flex-1'>{emailLoginLabel}</Button>
            }
            {isDisabled &&
              <Button onClick={cleanForm} className='flex-1' >Enviar outro convite</Button>
            }
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default Invite;