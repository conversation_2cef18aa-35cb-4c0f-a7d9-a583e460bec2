'use client';

import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
} from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import { ChangeEvent, useEffect, useState, } from 'react';
import { useForm, } from 'react-hook-form';
import { z, } from 'zod';
import { zodResolver, } from '@hookform/resolvers/zod';
import { getPacks, saveSale, } from './actions';
import { Pack, Profile, } from '@/utils/constants/types';
import { formatDate, formatToBrazilianReal, getToday, } from '@/lib/utils';
import { getStudents, } from '@/app/actions';
import { addDays, format, } from 'date-fns';

const requiredMessage = 'Campo obrigatório.';
const formSchema = z.object({
  billingDate: z.string({ required_error: 'Campo obrigatório.', }).regex(/^\d{2}\/\d{2}\/\d{4}$/, 'Formato inválido.'),
  description: z.string({ required_error: requiredMessage, })
    .min(10, { message: 'Descrição muito curta, mínimo 10 letras.', })
    .max(500, { message: 'Descrição muito longa, máximo 40 letras.', }).optional(),
  name: z.string({ required_error: requiredMessage, })
    .max(40, { message: 'Nome muito longo, máximo 40 letras.', }),
  method: z.string().optional(),
  installments: z.string().optional(),
  value: z.string({ required_error: requiredMessage, })
    .min(3, { message: 'Preço não permitido.', })
    .max(11, { message: 'Preço não permitido.', }),
  packId: z.string().optional(),
  clientId: z.string().uuid().optional(),
});

export type DebtFormData = z.infer<typeof formSchema>;

const CreateDebt = () => {
  const [isLoading, setIsLoading,] = useState(false);
  const [packs, setPacks,] = useState<Pack[] | null>(null);
  const [students, setStudents,] = useState<Profile[] | null>(null);
  const [type, setType,] = useState<string>('');
  const [packUses, setPackUses,] = useState<number | null>(null);
  const [used, setUsed,] = useState(0);
  const [expireDate, setExpireDate,] = useState('');

  const form = useForm<DebtFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      installments: '1',
      billingDate: format(new Date(), 'dd/MM/yyyy'),
    },
  });

  const watchPackId = form.watch('packId');

  const onSubmit = async (formData: z.infer<typeof formSchema>) => {
    setIsLoading(true);
    const response = await saveSale({ formData, used, expireDate, });
    if (response.status === 201) {
      toast({
        description: 'Despesa salva com sucesso.',
      });
    } else {
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
    setIsLoading(false);
  };

  const formatPrice = (value: string) => {
    const digits = value.replace(/\D/g, '');

    const formatter = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    const numericValue = parseInt(digits, 10) / 100;
    return formatter.format(numericValue);
  };

  const handlePriceChange = (event: ChangeEvent<HTMLInputElement>) => {
    const rawValue = event.target.value.replace(/\D/g, '');
    const formattedValue = formatPrice(rawValue);
    form.setValue('value', formattedValue, { shouldValidate: true, });
  };

  const handleBillingDateChange = (e: ChangeEvent<HTMLInputElement>) => {
    const formatted = formatDate(e.target.value);
    form.setValue('billingDate', formatted, { shouldValidate: true, });
  };

  useEffect(() => {
    (async () => {
      const response = await getStudents();
      if (response.status === 200) {
        setStudents(response.data);
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      if (type === 'pack') {
        const response = await getPacks();
        if (response.status === 200) {
          setPacks(response.data);
        } else {
          toast({
            variant: 'destructive',
            title: response.message,
          });
        }
      }
    })();
  }, [type,]);

  useEffect(() => {
    if (watchPackId) {
      const pack = packs?.find(pack => pack.id === watchPackId);
      if (!pack) return;
      form.setValue('name', pack.name);
      form.setValue('value', formatToBrazilianReal(pack.price));
      const today = getToday();
      setExpireDate(format(addDays(today, pack.expires), 'yyyy-MM-dd'));
      setPackUses(pack.use);
    }
  }, [watchPackId,]);

  console.log(form.formState.errors);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 w-full">
        <Label>Tipo do item</Label>
        <Select onValueChange={setType} >
          <FormControl>
            <SelectTrigger className="w-full">
              <SelectValue placeholder='Selecionar' />
            </SelectTrigger>
          </FormControl>
          <SelectContent>
            <SelectItem value='pack'>Pacote</SelectItem>
            <SelectItem value='product'>Produto</SelectItem>
          </SelectContent>
        </Select>
        {type === 'pack' && packs && (
          <FormField
            control={form.control}
            name='packId'
            render={({ field, }) => (
              <FormItem>
                <FormLabel className=''>
                  Pacotes
                </FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} >
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder='Selecionar' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {packs.map(pack => (
                          <SelectItem key={pack.id} value={pack.id}>{pack.name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
        {packUses !== null && (
          <div className='flex flex-col gap-2'>
            <Label>Aulas já usadas</Label>
            <Select onValueChange={setUsed} defaultValue='0' >
              <SelectTrigger className="w-full">
                <SelectValue placeholder='Selecionar' />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: packUses + 1, }, (_, i) => i).map((num) => (
                  <SelectItem key={num} value={num.toString()}>
                    {num} aula(s)
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
        <FormField
          control={form.control}
          name='clientId'
          render={({ field, }) => (
            <FormItem>
              <FormLabel className=''>
                Cliente
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder='Selecionar' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {students && students.map(student => (
                        <SelectItem key={student.id} value={student.id}>{student.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='name'
          render={({ field, }) => (
            <FormItem>
              <FormLabel>Nome</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder='Nome da venda'
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='description'
          render={({ field, }) => (
            <FormItem>
              <FormLabel className=''>
                Descrição
              </FormLabel>
              <Textarea {...field} placeholder='Detalhes sobre a venda' />
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='value'
          render={({ field, }) => (
            <FormItem>
              <FormLabel htmlFor="value">Valor</FormLabel>
              <FormControl>
                <Input
                  id="value"
                  {...field}
                  onChange={handlePriceChange}
                  placeholder="R$ 0,00"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='billingDate'
          render={({ field, }) => (
            <FormItem>
              <FormLabel htmlFor="billingDate">Data da fatura</FormLabel>
              <FormControl>
                <Input
                  id="billingDate"
                  {...field}
                  placeholder='01/01/2025'
                  onChange={handleBillingDateChange}
                  maxLength={10}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='installments'
          render={({ field, }) => (
            <FormItem>
              <FormLabel htmlFor="installments">Parcelas</FormLabel>
              <FormControl>
                <Input
                  id="installments"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='method'
          render={({ field, }) => (
            <FormItem>
              <FormControl>
                <Select onValueChange={field.onChange} >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder='Método de pagamento' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='CREDIT_CARD'>Cartão de crédito</SelectItem>
                      <SelectItem value='PIX'>Pix</SelectItem>
                      <SelectItem value='BOLETO'>Boleto</SelectItem>
                    </SelectContent>
                  </Select>
              </FormControl>
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full">
          {isLoading ? 'Salvando...' : 'Salvar venda'}
        </Button>
      </form>
    </Form>
  );
};

export default CreateDebt;