'use server';

import { createClientAdmin, } from '@/utils/supabase/server';
import { DebtFormData, } from './page';
import { Pack, Sale, } from '@/utils/constants/types';
import { userInfo, } from '@/supabase/verifications/user-info';
import { UNAUTHORIZED, } from '@/utils/constants';
import { getDate, parseISO, } from 'date-fns';
import { formatFromBrDate, } from '@/lib/utils';

export const parseBrazilianReal = (realValue: string) => {
  const cleanValue = realValue.replace(/[R$.,\s]/g, '');
  return cleanValue.replace(/(\d{2})$/, '.$1');
};

export const saveSale = async ({ formData, used, expireDate, }: { formData: DebtFormData, used: number, expireDate: string }) => {
  console.log('🚀 ~ formData:', formData);
  const user = await userInfo();

  if (!user || user.user_metadata.role !== 'admin') return {
    message: UNAUTHORIZED,
    status: 401,
  };

  const supabaseAdmin = createClientAdmin();
  const { data: saleRoot, error, }: { data: Sale | null } = await supabaseAdmin
    .from('sale_root')
    .insert({
      ...formData,
      billingDate: formatFromBrDate(formData.billingDate),
      billingDay: getDate(parseISO(formatFromBrDate(formData.billingDate))),
      value: await parseBrazilianReal(formData.value),
      schoolId: user.user_metadata?.schoolId,
    })
    .select()
    .maybeSingle();
  console.log('🚀 ~ saveSale ~ saleRoot:', saleRoot);
  console.log('🚀 ~ sale:', saleRoot);
  console.log('🚀 ~ error:', error);
  console.log('🚀 ~ saveSale ~ formData:', formData);

  if (error) return { message: 'Erro ao salvar venda.', status: 400, };

  if (formData.packId) {
    const { data, error, } = await supabaseAdmin
      .from('pack_profile')
      .insert({
        packId: formData.packId,
        profileId: formData.clientId,
        saleId: saleRoot?.id,
        purchaseStatus: 'active',
        paymentStatus: 'received',
        used,
        expireDate,
      })
      .select()
      .maybeSingle();

      console.log('🚀 ~ saveSale pack_profile~ error:', error);
      console.log('🚀 ~ saveSale pack_profile~ data:', data);
      if (error) return {
      message: 'Erro ao adicionar créditos ao aluno.',
      status: 400,
    };

    if (error) return { message: 'Erro ao salvar pacote do usuário.', status: 400, };
  }





  return { status: 201, };
};

export const getPacks = async (): Promise<{ data: Pack[] | null, status: 200 } | { message: string, status: 400 | 401 }> => {
  const user = await userInfo();

  if (!user) return {
    message: UNAUTHORIZED,
    status: 401,
  };

  const supabase = createClientAdmin();

  const { data: packs, } = await supabase
    .from('pack')
    .select()
    .match({ schoolId: user.user_metadata.schoolId, active: true, });

  if (packs && packs.length > 0) return {
    data: packs,
    status: 200,
  };

  return {
    message: 'Não foi possível buscar os pacotes.',
    status: 400,
  };
};
