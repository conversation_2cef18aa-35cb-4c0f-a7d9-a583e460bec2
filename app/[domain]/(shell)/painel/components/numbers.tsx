import { Payment, } from '../schema';
import { Profile, } from '@/utils/constants/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/index';
import { BookOpen, HandPlatter, Package, Repeat, UserPen } from 'lucide-react';
import { createClientAdmin } from '@/utils/supabase/server';
import { userInfo } from '@/supabase/verifications/user-info';

type Props = {
  newProfiles: { [key: string]: Profile[] }
  debts?: Payment[],
  payments: Payment[],
  totalDebtsCurrentMonth?: number,
  totalDebtsLastMonth?: number,
  totalDebtCurrentYear?: number,
  totalDebtLastYear?: number,
  totalRevenueCurrentMonth: number,
  totalRevenueCurrentYear: number,
  totalRevenueLastMonth: number,
  totalRevenueLastYear: number,
}

const Numbers = async () => {
  const user = await userInfo();
  const supabaseAdmin = createClientAdmin();
  const { count, } = await supabaseAdmin
    .from('profile')
    .select('*', { count: 'exact', head: true, })
    .match({ type: 'student', schoolId: user?.user_metadata.schoolId });

  const { count: teachers, } = await supabaseAdmin
    .from('profile')
    .select('*', { count: 'exact', head: true, })
    .match({ type: 'teacher', schoolId: user?.user_metadata.schoolId });

  const { count: courses, } = await supabaseAdmin
    .from('course')
    .select('*', { count: 'exact', head: true, })
    .match({ schoolId: user?.user_metadata.schoolId });

  const { count: services, } = await supabaseAdmin
    .from('service')
    .select('*', { count: 'exact', head: true, })
    .match({ schoolId: user?.user_metadata.schoolId });

  const { count: plans, } = await supabaseAdmin
    .from('plan')
    .select('*', { count: 'exact', head: true, })
    .match({ schoolId: user?.user_metadata.schoolId });

  const { count: packs, } = await supabaseAdmin
    .from('pack')
    .select('*', { count: 'exact', head: true, })
    .match({ schoolId: user?.user_metadata.schoolId });

  return (
    <div className='w-full h-full flex flex-col gap-6'>
      <div className='grid grid-cols-[repeat(auto-fit,minmax(150px,1fr))] gap-4'>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Alunos</CardTitle>
            <UserPen className="h-4 w-4 ml-2 text-muted-foreground" />
          </CardHeader>
          <CardContent className='flex items-center justify-center w-auto'>
            <div className="text-2xl font-bold">{count}</div>
          </CardContent>
          </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Professores</CardTitle>
            <UserPen className="h-4 w-4 ml-2 text-muted-foreground" />
          </CardHeader>
          <CardContent className='flex items-center justify-center w-auto'>
            <div className="text-2xl font-bold">{teachers}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Serviços</CardTitle>
            <HandPlatter className="h-4 w-4 ml-2 text-muted-foreground" />
          </CardHeader>
          <CardContent className='flex items-center justify-center w-auto'>
            <div className="text-2xl font-bold">{services}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Planos</CardTitle>
            <Repeat className="h-4 w-4 ml-2 text-muted-foreground" />
          </CardHeader>
          <CardContent className='flex items-center justify-center w-auto'>
            <div className="text-2xl font-bold">{plans}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pacotes</CardTitle>
            <Package className="h-4 w-4 ml-2 text-muted-foreground" />
          </CardHeader>
          <CardContent className='flex items-center justify-center w-auto'>
            <div className="text-2xl font-bold">{packs}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cursos</CardTitle>
            <BookOpen className="h-4 w-4 ml-2 text-muted-foreground" />
          </CardHeader>
          <CardContent className='flex items-center justify-center w-auto'>
            <div className="text-2xl font-bold">{courses}</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Numbers;
