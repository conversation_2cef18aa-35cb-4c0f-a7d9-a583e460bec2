import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle, } from '@/components/index';
import { DollarSign, } from 'lucide-react';
import BarChart from './barchart';
import { formatToBrazilianReal, } from '@/lib/utils';
import { redirect, } from 'next/navigation';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { format, getDaysInMonth, isThisMonth, isThisYear, parseISO, } from 'date-fns';
import { isLastMonth, } from '@/lib/utils';
import { NothingFound, } from '@/components/index';
import { Debt, Sale } from '@/utils/constants/types';
import { z } from 'zod';
import { User } from '@supabase/supabase-js';
import { Payment, paymentSchema } from '../schema';

const getPaymentsLastTwoYears = async ({ payments, label, }: { payments: { externalReference: string, }[], label: 'plan' | 'pack' }) => {
  const filteredPayments = payments
  .filter(payment => {
    if (!payment.externalReference) return false;
    if (label === 'plan') return payment.externalReference.includes('plan');
    return payment.externalReference.includes('pack');
  })
  .map(payment => ({
    ...payment,
    type: 'in',
  }));
  return z.array(paymentSchema).parse(filteredPayments);
};

const getSalesLastTwoYears = async ({ schoolId, }: { schoolId: string }) => {
  const lastYear = new Date();
  lastYear.setFullYear(new Date().getFullYear() - 1);
  lastYear.setDate(1);
  lastYear.setMonth(0);
  lastYear.setUTCHours(0, 0, 0, 0);

  const today = new Date();
  today.setFullYear(new Date().getFullYear());
  today.setDate(getDaysInMonth(today));
  today.setUTCHours(23, 59, 59);

  const supabaseAdmin = createClientAdmin();
  const { data: sales, } = await supabaseAdmin
    .from('sale')
    .select()
    .lte('billingDate', format(today, 'yyyy-MM-dd'))
    .gte('billingDate', format(lastYear, 'yyyy-MM-dd'))
    .order('billingDate', { ascending: false, })
    .match({ schoolId, })
    .returns<Sale[] | null>();

  if (!sales || !sales.length) return [];
  return sales;

};

const getDebtsLastTwoYears = async ({ user, }: { user: User }) => {
  const lastYear = new Date();
  lastYear.setFullYear(new Date().getFullYear() - 1);
  lastYear.setDate(1);
  lastYear.setMonth(0);
  lastYear.setUTCHours(0, 0, 0, 0);

  const today = new Date();
  today.setFullYear(new Date().getFullYear());
  today.setDate(getDaysInMonth(today));
  today.setUTCHours(23, 59, 59);

  const supabaseAdmin = createClientAdmin();
  const { data: debts, } = await supabaseAdmin
    .from('debt')
    .select()
    .lte('billingDate', format(today, 'yyyy-MM-dd'))
    .gte('billingDate', format(lastYear, 'yyyy-MM-dd'))
    .order('billingDate', { ascending: false, })
    .eq('schoolId', user.user_metadata.schoolId)
    .returns<Debt[] | null>();

  let debtsFormatted = (debts || []).map(debt => ({
    paymentDate: debt.billingDate,
    product: {
      name: debt.name,
    },
    profile: {
      name: 'Escola',
    },
    status: debt.paid ? 'approved' : 'pending',
    netValue: debt.value,
    type: 'out',
  }));
  return z.array(paymentSchema).parse(debtsFormatted);
};

const getPackSales = ({ sales, }: { sales: Sale[] }) => {
  let salesFormatted = (sales || []).filter(sale => sale.packId !=null ).map(sale => ({
    id: sale.id,
    name: sale.name,
    paymentDate: sale.billingDate,
    status: 'confirmed',
    netValue: sale.value,
    type: 'in',
  }));
  return z.array(paymentSchema).parse(salesFormatted);
};

const getProductSales = ({ sales, }: { sales: Sale[] }) => {
  let salesFormatted = (sales || []).filter(sale => sale.packId === null).map(sale => ({
    id: sale.id,
    name: sale.name,
    paymentDate: sale.billingDate,
    status: 'confirmed',
    netValue: sale.value,
    type: 'in',
  }));
  return z.array(paymentSchema).parse(salesFormatted);
};

const Financial = async ({ domain }: { domain: string }) => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user || user?.user_metadata.role !== 'admin') redirect('/login');

  const tokenName = `AS_API_KEY_${domain}`;
  const token = process.env[tokenName];
  const year = new Date();
  year.setFullYear(new Date().getFullYear() - 1);
  year.setDate(1);
  year.setMonth(0);
  const queryParams = `limit=5000&status=RECEIVED&dateCreated[ge]=${format(year, 'yyyy-MM-dd')}`;
  const response = await fetch(`https://api.asaas.com/v3/payments?${queryParams}`, {
    headers: {
      access_token: token,
      'User-Agent': 'meu-mestre',
    },
  });

  if (response.status !== 200) return <NothingFound label='Nenhum pagamento encontrado.' />;

  const { data, } = await response.json();

  const paymentsPlanZod = await getPaymentsLastTwoYears({ payments: data, label: 'plan', });
  const paymentsPackZod = await getPaymentsLastTwoYears({ payments: data, label: 'pack', });
  const sales = await getSalesLastTwoYears({ schoolId: user.user_metadata.schoolId, });
  const packSales = getPackSales({ sales, });
  const productSales = getProductSales({ sales, });

  const totalRevenueCurrentMonthPlan = paymentsPlanZod.reduce((total, payment: Payment) => isThisMonth(parseISO(payment.paymentDate)) ? total + payment.netValue : total, 0);
  const totalRevenueCurrentMonthPack = [...paymentsPackZod, ...packSales,].reduce((total, payment: Payment) => isThisMonth(parseISO(payment.paymentDate)) ? total + payment.netValue : total, 0);
  const totalRevenueCurrentMonthSalesProducts = productSales.reduce((total, payment: Payment) => isThisMonth(parseISO(payment.paymentDate)) ? total + payment.netValue : total, 0);
  const totalRevenueCurrentMonth = (totalRevenueCurrentMonthPack + totalRevenueCurrentMonthPlan + totalRevenueCurrentMonthSalesProducts);

  const totalRevenueLastMonthPlan = paymentsPlanZod.reduce((total, payment: Payment) => isLastMonth(parseISO(payment.paymentDate)) ? total + payment.netValue : total, 0);
  const totalRevenueLastMonthPack = [...paymentsPackZod, ...packSales,].reduce((total, payment: Payment) => isLastMonth(parseISO(payment.paymentDate)) ? total + payment.netValue : total, 0);
  const totalRevenueLastMonthSalesProducts = productSales.reduce((total, payment: Payment) => isThisMonth(parseISO(payment.paymentDate)) ? total + payment.netValue : total, 0);
  const totalRevenueLastMonth = (totalRevenueLastMonthPlan + totalRevenueLastMonthPack + totalRevenueLastMonthSalesProducts);

  const totalRevenueCurrentYearPlan = paymentsPlanZod.reduce((total, payment: Payment) => isThisYear(parseISO(payment.paymentDate)) ? total + payment.netValue : total, 0);
  const totalRevenueCurrentYearPack = [...paymentsPackZod, ...packSales,].reduce((total, payment: Payment) => isThisYear(parseISO(payment.paymentDate)) ? total + payment.netValue : total, 0);
  const totalRevenueCurrentYearSalesProducts = productSales.reduce((total, payment: Payment) => isThisYear(parseISO(payment.paymentDate)) ? total + payment.netValue : total, 0);
  const totalRevenueCurrentYear = (totalRevenueCurrentYearPlan + totalRevenueCurrentYearPack + totalRevenueCurrentYearSalesProducts);

  const totalRevenueLastYearPlan = paymentsPlanZod.reduce((total, payment: Payment) => !isThisYear(parseISO(payment.paymentDate)) ? total + payment.netValue : total, 0);
  const totalRevenueLastYearPack = [...paymentsPackZod, ...packSales,].reduce((total, payment: Payment) => !isThisYear(parseISO(payment.paymentDate)) ? total + payment.netValue : total, 0);
  const totalRevenueLastYearSalesProducts = productSales.reduce((total, payment: Payment) => !isThisYear(parseISO(payment.paymentDate)) ? total + payment.netValue : total, 0);
  const totalRevenueLastYear = (totalRevenueLastYearPlan + totalRevenueLastYearPack + totalRevenueLastYearSalesProducts);

  const debts = await getDebtsLastTwoYears({ user, });
  const totalDebtsCurrentMonth = debts.reduce((total, debt) => isThisMonth(parseISO(debt.paymentDate)) ? total + debt.netValue : total, 0);
  const totalDebtsLastMonth = debts.reduce((total, debt) => isLastMonth(parseISO(debt.paymentDate)) ? total + debt.netValue : total, 0);
  const totalDebtCurrentYear = debts.reduce((total, debt) => isThisYear(parseISO(debt.paymentDate)) ? total + debt.netValue : total, 0);
  const totalDebtLastYear = debts.reduce((total, debt) => !isThisYear(parseISO(debt.paymentDate)) ? total + debt.netValue : total, 0);

  const payments = [...paymentsPlanZod, ...paymentsPackZod, ...packSales, ...productSales,];

  return (
    <div className='w-full h-full'>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 my-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Receita do mês</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatToBrazilianReal(totalRevenueCurrentMonth)}</div>
            <p className="text-xs text-muted-foreground">Mês passado: {formatToBrazilianReal(totalRevenueLastMonth)}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Receita {new Date().getFullYear()}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatToBrazilianReal(totalRevenueCurrentYear)}</div>
            <p className="text-xs text-muted-foreground">Receita {new Date().getFullYear() - 1}: {formatToBrazilianReal(totalRevenueLastYear)}</p>
          </CardContent>
        </Card>
      </div>
      {!!totalDebtsCurrentMonth && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 my-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Despesas do mês</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatToBrazilianReal(totalDebtsCurrentMonth)}</div>
              <p className="text-xs text-muted-foreground">Mês passado: {formatToBrazilianReal(totalDebtsLastMonth)}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Despesas {new Date().getFullYear()}</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatToBrazilianReal(totalDebtCurrentYear)}</div>
              <p className="text-xs text-muted-foreground">Despesas {new Date().getFullYear() - 1}: {formatToBrazilianReal(totalDebtLastYear)}</p>
            </CardContent>
          </Card>
        </div>
      )}
      {!!totalDebtsCurrentMonth && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 my-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total do mês</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatToBrazilianReal(totalRevenueCurrentMonth - totalDebtsCurrentMonth)}</div>
              <p className="text-xs text-muted-foreground">Mês passado: {formatToBrazilianReal(totalRevenueLastMonth - totalDebtsLastMonth)}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total {new Date().getFullYear()}</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatToBrazilianReal(totalRevenueCurrentYear - totalDebtCurrentYear)}</div>
              <p className="text-xs text-muted-foreground">Total {new Date().getFullYear() - 1}: {formatToBrazilianReal(totalRevenueLastYear - totalDebtLastYear)}</p>
            </CardContent>
          </Card>
        </div>
      )}
      <BarChart label='Faturamento ano atual' payments={payments} />
      {debts && <BarChart label='Custos ano atual' payments={debts} />}
      <BarChart label='Comparação de faturamento com ano passado' payments={payments} multiple />
      {debts && <BarChart label='Comparação de gastos com ano passado' payments={debts} multiple />}
    </div>
  );
};

export default Financial;
