'use client';

import { <PERSON>, <PERSON>Content, CardHeader, CardTitle, ChartContainer, } from '@/components/index';
import { ChartConfig, ChartTooltip, ChartTooltipContent, } from '@/components/ui/shard/chart';
import { Profile, } from '@/utils/constants/types';
import { createClient } from '@/utils/supabase/client';
import { useEffect, useState } from 'react';
import { Bar, BarChart, XAxis, } from 'recharts';
import { getNewProfiles } from '../actions';

const months = {
  1: 'jan',
  2: 'fev',
  3: 'mar',
  4: 'abr',
  5: 'mai',
  6: 'jun',
  7: 'jul',
  8: 'ago',
  9: 'set',
  10: 'out',
  11: 'nov',
  12: 'dez',
};

const chartConfig = {
  subscription: {
    label: 'Subscriptions',
    theme: {
      light: 'black',
      dark: 'white',
    },
  },
} satisfies ChartConfig;


const getMonth = (dateString: string) => {
  const date = new Date(dateString);
  return date.getMonth() + 1;
};

const groupByMonth = (data: Profile[] | []): { [key: string]: Profile[] } => {
  const groupedData: { [key: string]: Profile[] } = {};
  for (const user of data) {
    const month = getMonth(user.createdAt);
    if (!groupedData[month]) {
      groupedData[month] = [];
    }
    groupedData[month].push(user);
  }
  return groupedData;
};

const NewStudents = () => {
  const [stats, setStats] = useState<[]>([]);

  useEffect(() => {
    (async () => {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      console.log("🚀 ~ user:", user)
      if (!user) return null;

      let newProfiles = await getNewProfiles({ user, });
      const newProfilesFormatted = groupByMonth(newProfiles);

      const year = Array.from({ length: 12, }, (_, i) => i + 1);
      const stats = year.map(month => ({
        alunos: newProfilesFormatted[month] ? newProfilesFormatted[month].length : 0,
        label: newProfilesFormatted[month] ? newProfilesFormatted[month].length : month,
        month: months[month],
      }));
      setStats(stats)
    })();
  }, [])

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 w-full">
        <CardTitle className="text-sm font-semibold">Novos alunos</CardTitle>
      </CardHeader>
      <CardContent className='w-full'>
        <ChartContainer config={chartConfig} className="mt-2 h-[80px] w-full">
          <BarChart data={stats}>
            <XAxis
              dataKey="month"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  hideLabel
                  className="w-full"
                />
              }
            />
            <Bar
              dataKey="alunos"
              fill="var(--color-subscription)"
              radius={4}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};

export default NewStudents;