import { Avatar, AvatarFallback, AvatarImage, Card, CardContent, CardHeader, CardTitle, } from '@/components/index';
import { getDateInExtenseIfSameWeek, } from '@/lib/utils';
import { userInfo, } from '@/supabase/verifications/user-info';
import { createClientAdmin, } from '@/utils/supabase/server';

const BirthDays = async () => {
  const user = await userInfo();
  const supabaseAdmin = createClientAdmin();
  const { data: profiles, } = await supabaseAdmin.rpc('get_upcoming_birthdays', { school: user?.user_metadata.schoolId, });

  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-semibold'>Aniversariantes nos próximos dias</CardTitle>
      </CardHeader>
      <CardContent className='flex flex-col gap-4'>
      {profiles && profiles.map((profile) => (
        <Card key={profile.id} className='w-full'>
          <CardContent className='flex items-center justify-between p-4'>
            <div className='flex items-center space-x-4'>
              <Avatar className='h-10 w-10 flex-shrink-0 hidden sm:block'>
                <AvatarImage src={profile.profileImage} alt={profile.name} />
                <AvatarFallback>{profile.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className='min-w-0'>
                <h3 className='font-semibold truncate'>{profile.name}</h3>
                <p className='text-sm text-gray-500 truncate'>{profile.email}</p>
              </div>
            </div>
            <div className='flex flex-col items-end space-y-1'>
              <p className='font-medium text-right truncate'>{getDateInExtenseIfSameWeek({ date: profile.birthdate, ignoreYear: true, })}</p>
            </div>
          </CardContent>
        </Card>
      ))}
      {(!profiles || !profiles.length) && <span>Sem aniversariantes nos próximos dias.</span>}
      </CardContent>
    </Card>
  );
};

export default BirthDays;
