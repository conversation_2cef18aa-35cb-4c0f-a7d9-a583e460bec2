'use client';

import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis, } from 'recharts';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/shard/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/shard/chart';
import { isThisYear, parseISO, } from 'date-fns';
import { formatToBrazilianReal, groupTransactionsByMonth, } from '@/lib/utils';
import { CSSProperties, } from 'react';

const chartConfig = {
  lastYear: {
    label: new Date().getFullYear() - 1,
    color: 'hsl(var(--chart-2))',
  },
  current: {
    label: new Date().getFullYear(),
    color: 'hsl(var(--chart-3))',
  },
} satisfies ChartConfig;


const Chart = ({ label, multiple = false, payments, }: { label: string, multiple?: boolean, payments: any, }) => {
  const currentYear = new Date().getFullYear();
  const lastYear = new Date().getFullYear() - 1;
  const paymentsThisYear = payments.filter((payment) => isThisYear(parseISO(payment.paymentDate)));
  const paymentsLastYear = payments.filter((payment) => !isThisYear(parseISO(payment.paymentDate)));

  const chartData = multiple ?
  groupTransactionsByMonth(paymentsThisYear, paymentsLastYear) :
  groupTransactionsByMonth(paymentsThisYear);

  return (
    <Card className='mt-6'>
      <CardHeader>
        <CardTitle>{label}</CardTitle>
        {multiple && <CardDescription>{lastYear} - {currentYear}</CardDescription>}
        {!multiple && <CardDescription>mês a mês</CardDescription>}
      </CardHeader>
      <CardContent className='flex items-center justify-center w-full'>
        <ChartContainer config={chartConfig} className='w-full h-40' >
          <BarChart accessibilityLayer data={chartData}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="month"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  hideLabel
                  className="w-[180px]"
                  formatter={(value, name) => (
                    <>
                      <div
                        className="h-2.5 w-2.5 shrink-0 rounded-[2px] bg-[--color-bg]"
                        style={
                          {
                            '--color-bg': `var(--color-${name})`,
                          } as CSSProperties
                        }
                      />
                      {chartConfig[name as keyof typeof chartConfig]?.label ||
                        name}
                      <div className="ml-auto flex items-baseline font-mono font-medium tabular-nums text-foreground">
                        {formatToBrazilianReal(value)}
                      </div>
                    </>
                  )}
                />
              }
            />
            <Bar dataKey="lastYear" fill="var(--color-lastYear)" radius={4} />
            <Bar dataKey="current" fill="var(--color-current)" radius={4} />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};

export default Chart;
