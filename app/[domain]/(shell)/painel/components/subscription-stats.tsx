import { Avatar, AvatarFallback, AvatarImage, Card, CardContent, CardHeader, CardTitle, } from '@/components/index';
import { formatToBrazilianReal, } from '@/lib/utils';
import { createClientAdmin, } from '@/utils/supabase/server';
import { differenceInDays, } from 'date-fns';

const SubscriptionStats = async () => {
  const supabaseAdmin = createClientAdmin();
  const { data: payments, } = await supabaseAdmin
    .from('payment')
    .select('*, profile(*)')
    .eq('status', 'overdue')
    .not('planId', 'is', null);

  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-semibold'>Mensalidades atrasadas</CardTitle>
      </CardHeader>
      <CardContent>
      {payments && payments.map((payment) => (
        <Card key={payment.id} className='w-full'>
          <CardContent className='flex items-center justify-between p-4'>
            <div className='flex items-center space-x-4'>
              <Avatar className='h-10 w-10 flex-shrink-0'>
                <AvatarImage src={payment.profile.profileImage} alt={payment.profile.name} />
                <AvatarFallback>{payment.profile.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className='min-w-0'>
                <h3 className='font-semibold truncate'>{payment.profile.name}</h3>
                <p className='text-sm text-gray-500 truncate'>{payment.profile.email}</p>
              </div>
            </div>
            <div className='flex flex-col items-end space-y-1'>
              <p className='font-medium text-right truncate'>{payment.description}</p>
              <span className='font-bold'>{formatToBrazilianReal(payment.value)}</span>
              {payment.status === 'overdue' && (
                <div className='flex items-center space-x-1 text-red-600'>
                  <span className='text-base'>{differenceInDays(new Date(), payment.dueDate)} dia(s)</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
      {payments && payments.length === 0 && <span>Sem mensalidades atrasadas.</span>}
      </CardContent>
    </Card>
  );
};

export default SubscriptionStats;
