'use server';

import { Profile } from "@/utils/constants/types";
import { createClientAdmin } from "@/utils/supabase/server";
import { User } from "@supabase/supabase-js";

export const getNewProfiles = async ({ user, }: { user: User }): Promise<Profile[] | []> => {
  const currentYear = new Date();
  currentYear.setFullYear(new Date().getFullYear());
  currentYear.setDate(1);
  currentYear.setMonth(0);
  currentYear.setUTCHours(0, 0, 0, 0);
  const supabaseAdmin = createClientAdmin();
  const { data: profiles, } = await supabaseAdmin
  .from('profile')
  .select()
  .eq('schoolId', user.user_metadata.schoolId)
  .eq('type', 'student')
  .gte('createdAt', currentYear.toISOString().slice(0, 19).replace('T', ' '))
  .returns<Profile[] | null>();
  if (profiles) return profiles;
  else return [];
};