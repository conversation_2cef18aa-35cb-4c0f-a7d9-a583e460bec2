import { Metadata, } from 'next';

import { Tabs, } from '@radix-ui/react-tabs';
import { TabsContent, TabsList, TabsTrigger, } from '@/components/ui/shard/tabs';
import General from './components/general';
import Financial from './components/financial';

export const metadata: Metadata = {
  title: 'Informações financeiras.',
  description: 'Visualização de gerenciamento financeiro.',
};

const Dashboard = async ({ params: { domain, }, }: { params: { domain: string } }) => {
  return (
    <Tabs defaultValue='geral' className='w-full' activationMode='manual'>
      <TabsList className='flex w-full mb-6'>
        <TabsTrigger value='geral' className='flex-1'>Geral</TabsTrigger>
        <TabsTrigger value='financial' className='flex-1'>Financeiro</TabsTrigger>
      </TabsList>
      <TabsContent value='geral'>
        <General />
      </TabsContent>
      <TabsContent value='financial'>
        <Financial domain={domain} />
      </TabsContent >
    </Tabs>
  );
};

export default Dashboard;