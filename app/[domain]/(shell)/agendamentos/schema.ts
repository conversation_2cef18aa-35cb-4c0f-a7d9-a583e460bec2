import { z, } from 'zod';

// We're keeping a simple non-relational schema here.
// IRL, you will have a schema for your data models.
export const taskSchema = z.object({
  code: z.string(),
  profile: z.object({
    name: z.string(),
    id: z.string().optional(),
  }),
  status: z.string(),
  equipment: z.array(
    z.object({
    name: z.string(),
  })),
  day: z.string(),
  schedule: z.object({
    hour: z.string(),
  }),
});

export type Task = z.infer<typeof taskSchema>