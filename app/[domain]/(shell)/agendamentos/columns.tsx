'use client';

import { ColumnDef, } from '@tanstack/react-table';
import { statuses, } from './data';
import { Task, } from './schema';
import { DataTableColumnHeader, } from './data-table-column-header';
import { DataTableRowActions, } from './data-table-row-actions';
import { Badge, } from '@/components/ui/shard/badge';
import { Calendar, Clock, } from 'lucide-react';
import Link from 'next/link';
import { getDateInExtenseIfSameWeek, } from '@/lib/utils';

export const columns: ColumnDef<Task>[] = [
  {
    accessorKey: 'code',
    header: ({ column, }) => (
      <DataTableColumnHeader column={column} title="Código" />
    ),
    cell: ({ row, }) => <div className="w-[80px]">{(`${row.getValue('code').slice(0, 3)}-${row.getValue('code').slice(3,5)}`).toUpperCase()}</div>,
  },
  {
    accessorKey: 'profile',
    header: ({ column, }) => (
      <DataTableColumnHeader column={column} title="Aluno" />
    ),
    cell: ({ row, }) => {
      const profile = row.getValue('profile') as { name: string };
      const id = row.original.profile.id;
      return (
        <div className="flex space-x-2">
          {id && (
            <Link
              href={`/alunos/${id}`} className="max-w-[500px] truncate font-medium">
              {profile.name || 'Sem nome'}
            </Link>
          )}
          {!id && (
            <span className="max-w-[500px] truncate font-medium">
              {profile.name || 'Sem nome'}
            </span>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'equipment',
    header: ({ column, }) => (
      <DataTableColumnHeader column={column} title="Equipamento" />
    ),
    cell: ({ row, }) => {
      const equipments = row.getValue('equipment') as { name: string }[];
      if (equipments?.length === 0) return null;
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {equipments.map(equipment => <div key={equipment.name}>{equipment.name}</div>)}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: ({ column, }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row, }) => {
      const status = statuses.find(
        (status) => status.value === row.getValue('status')
      );

      if (!status) {
        return null;
      }

      return (
        <div className="flex w-[100px] items-center">
          <Badge variant={status.color}>{status.label}</Badge>
        </div>
      );
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: 'day',
    header: ({ column, }) => (
      <DataTableColumnHeader column={column} title="Dia" />
    ),
    cell: ({ row, }) => {
      const day = getDateInExtenseIfSameWeek({ date: row.getValue('day'), });
      return (
        <div className="flex items-center">
          <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
          <span className='capitalize'>{day}</span>
        </div>
      );
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: 'schedule',
    header: ({ column, }) => (
      <DataTableColumnHeader column={column} title="Hora" />
    ),
    cell: ({ row, }) => {
      const schedule = row.getValue('schedule') as { hour: string };
      return (
        <div className="flex items-center">
          <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
          <span>{schedule.hour}</span>
        </div>
      );
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'actions',
    cell: ({ row, }) => <DataTableRowActions row={row} />,
  },
];