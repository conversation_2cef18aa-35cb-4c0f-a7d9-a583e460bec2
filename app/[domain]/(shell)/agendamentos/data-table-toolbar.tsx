'use client';

import { Table, } from '@tanstack/react-table';
import { X, } from 'lucide-react';

import { DataTableViewOptions, } from './data-table-view-options';

import { statuses, } from './data';
import { DataTableFacetedFilter, } from './data-table-faceted-filter';
import { Button, Input, } from '@/components/index';
import { format, isSameWeek, isToday, isTomorrow, isYesterday, parseISO, } from 'date-fns';
import { ptBR, } from 'date-fns/locale';

interface DataTableToolbarProps<TData> {
  table: Table<TData>
}

export function DataTableToolbar<TData>({
  table,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0;

  const getReadableStatus = (rowValue: string) => {
    const day = format(parseISO(rowValue), 'yyyy, MM, dd');
    if (isToday(day)) return 'Hoje';
    if (isTomorrow(day)) return 'Amanhã';
    if (isYesterday(day)) return 'Ontem';
    const weekDay = format(parseISO(rowValue), 'EEEE',{locale: ptBR,});
    return weekDay;
  };

  const getDayValues = (column: any) => {
    const days = [];
    for (const row of column.getFacetedUniqueValues()) {
      if(isSameWeek(new Date(), row[0], { weekStartsOn: 1,})) {
        days.push({
          label: getReadableStatus(row[0]),
          value: row[0],
        });
      }
    }
    return days;
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <Input
          placeholder="Procurar por código"
          value={(table.getColumn('code')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('code')?.setFilterValue(event.target.value)
          }
          className="h-8 w-[150px] lg:w-[250px]"
        />
        {table.getColumn('day') && (
          <DataTableFacetedFilter
            column={table.getColumn('day')}
            title="Dia"
            options={getDayValues(table.getColumn('day'))}
          />
        )}
        {table.getColumn('status') && (
          <DataTableFacetedFilter
            column={table.getColumn('status')}
            title="Status"
            options={statuses}
          />
        )}
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            Limpar
            <X />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  );
}