import { Metadata, } from 'next';

import { columns, } from './columns';
import { DataTable, } from './data-table';
import { redirect, } from 'next/navigation';
import { Button, NothingFound, } from '@/components/index';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestResponse, } from '@supabase/supabase-js';
import { Booking, Equipment, Lead, Profile, Schedule, Service, } from '@/utils/constants/types';
import Link from 'next/link';
import { Plus, } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Agendamentos',
  description: 'Visualização de gerenciamento de todos os agendamentos.',
};

type BookingsProps = Booking & { profile: Profile, lead: Lead, schedule: Schedule, service: Service, equipment: Equipment };

const Bookings = async () => {
  const supabase = createClient();
  const supabaseAdmin = createClientAdmin();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user || (user?.user_metadata.role !== 'admin' && user?.user_metadata.role !== 'teacher')) redirect('/login');

  const { data: bookings, error, }: PostgrestResponse<BookingsProps> = await supabaseAdmin.rpc('get_all_bookings', {
    school: user?.user_metadata.schoolId,
  });
  if (error || bookings.length === 0) return <NothingFound label='Nenhum agendamento encontrado.' />;

  const formattedBookings = bookings.map(booking => ({
    ...booking,
    ...(booking.profile?.name ? { profile: booking.profile, } : { profile: booking.lead, }),
    equipment: booking.equipment || [],
  }));
  console.log('🚀 ~ Bookings ~ formattedBookings:', formattedBookings);

  return (
    <div className='w-full'>
      <DataTable data={formattedBookings} columns={columns} />
      <div className="fixed bottom-12 md:bottom-6 right-6 z-50">
        <Button asChild size="lg" className="rounded-full shadow-lg h-14 w-14 p-0 md:w-auto md:px-4 md:h-12">
          <Link href='/agendar'>
            <Plus className="h-6 w-6" />
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default Bookings;