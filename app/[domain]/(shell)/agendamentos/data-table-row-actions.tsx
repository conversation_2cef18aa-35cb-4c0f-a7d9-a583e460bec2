'use client';

import { Row, } from '@tanstack/react-table';
import { MoreHorizontal, Pen, } from 'lucide-react';

import { taskSchema, } from './schema';
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/index';
import { URLS, } from '@/utils/supabase/constants';
import { toast, } from '@/components/ui/shard/use-toast';
import { useEffect, useState, } from 'react';
import { getProfile, } from '@/app/actions';

interface DataTableRowActionsProps<TData> {
  row: Row<TData>
}

export function DataTableRowActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const [hasAccess, setHasAccess,] = useState(false);
  const booking = taskSchema.parse(row.original);

  const changeBookStatus = async (code: string, status: string) => {
    const response = await fetch(URLS.BOOKING_STATUS, {
      method: 'PATCH',
      body: JSON.stringify({ code, status, }),
    });
    if (response.status === 200) {
      toast({
        title: 'Status do agendamento alterado.',
      });
    } else {
      const data = await response.json();
      toast({
        variant: 'destructive',
        title: data.message,
      });
    }
  };

  useEffect(() => {
    (async () => {
      const profileResponse = await getProfile();
      if (profileResponse.status !== 200) return null;
      const profile = profileResponse.data;
      setHasAccess(profile.type === 'admin');
    })();
  }, []);

  if (!hasAccess) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
        >
          <MoreHorizontal />
          <span className="sr-only">Abrir menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        {
          booking.status !== 'used' && (
            <DropdownMenuItem onSelect={() => changeBookStatus(booking.code, 'used')} className='cursor-pointer'>Usado</DropdownMenuItem>
          )
        }
        {
          booking.status !== 'missed' && (
            <DropdownMenuItem onSelect={() => changeBookStatus(booking.code, 'missed')} className='cursor-pointer'>Faltou</DropdownMenuItem>
          )
        }
        {booking.status !== 'approved' && (
          <DropdownMenuItem onSelect={() => changeBookStatus(booking.code, 'approved')}>Confirmar</DropdownMenuItem>
        )}
        {booking.status !== 'canceled' && (
          <DropdownMenuItem onSelect={() => changeBookStatus(booking.code, 'canceled')} className='cursor-pointer'>
            <span className='text-red-400'>Cancelar</span>
            <DropdownMenuShortcut>
              <span className='text-red-400'>⌫</span>
            </DropdownMenuShortcut>
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}