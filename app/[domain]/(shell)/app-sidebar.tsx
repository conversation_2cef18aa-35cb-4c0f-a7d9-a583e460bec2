'use client';

import type * as React from 'react';

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/index';
import { NavMain, } from './nav-main';
import { NavUser, } from './nav-user';
import { linksAdmin, linksStudent, linksTeacher, } from '@/utils/constants';
import Link from 'next/link';
import { Profile, } from '@/utils/constants/types';

export function AppSidebar({ user, schoolName, ...props }: { user: Profile, schoolName: string, } & React.ComponentProps<typeof Sidebar>) {
  let items: typeof linksAdmin | typeof linksTeacher | typeof linksStudent = [];
  switch (user.type) {
    case 'admin':
      items = linksAdmin;
      break;
    case 'teacher':
      items = linksTeacher;
      break;
    case 'student':
      items = linksStudent;
      break;
    default:
      break;
  }
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
            <Link href='/'>
              <span className='ml-4 text-xl font-semibold'>{schoolName}</span>
            </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={items} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={user} />
      </SidebarFooter>
    </Sidebar>
  );
}
