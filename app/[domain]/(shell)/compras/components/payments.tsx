'use server';

import React from 'react';
import {
  Badge,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/index';
import { Calendar, } from 'lucide-react';
import SeeInvoice from './see-invoice';
import { format, parseISO, } from 'date-fns';
import { ptBR, } from 'date-fns/locale';
import { createClientAdmin, } from '@/utils/supabase/server';

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-500';
    case 'received':
    case 'confirmed':
      return 'bg-green-500';
    case 'overdue':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
};

const getStatusName = (status: string) => {
  switch (status) {
    case 'pending':
      return 'Pendente';
    case 'received':
    case 'confirmed':
      return 'Confirmado';
    case 'overdue':
      return 'Atrasado';
    default:
      return '';
  }
};

const Payments = async ({ subscriptionId, }: { subscriptionId: string }) => {
  const supabaseAdmin = createClientAdmin();
  const { data: payments, } = await supabaseAdmin
    .from('payment')
    .select()
    .match({ subscriptionId, });

  if (!payments || payments.length === 0) return null;

  return (
    <Card className="w-full bg-white shadow-lg rounded-lg overflow-hidden">
      <CardHeader className="bg-gray-50 border-b border-gray-200">
        <CardTitle className="flex justify-between items-center">
        <span className="text-xl font-bold text-gray-800">Cobranças</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col p-0">
        <Table>
          <TableHeader className="hidden sm:table-header-group">
            <TableRow>
              <TableHead className="w-[120px]">Status</TableHead>
              <TableHead>Plano</TableHead>
              <TableHead className="w-[180px]">Vencimento</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {payments.map((payment) => (
              <TableRow key={payment.id} className="flex flex-col sm:table-row py-4">
                <>
                  <TableCell className="flex justify-between items-center py-2 sm:table-cell sm:w-[120px]">
                    <span className="sm:hidden font-medium">Status:</span>
                    <Badge className={`${getStatusColor(payment.status)} text-primary-foreground w-24 justify-center`}>
                      {getStatusName(payment.status)}
                    </Badge>
                  </TableCell>
                  <TableCell className="py-2 sm:table-cell">
                    <div className="flex items-center justify-between sm:justify-start">
                      <span className="sm:hidden font-medium">Plano:</span>
                      <p className="text-sm">{payment.description}</p>
                    </div>
                  </TableCell>
                  <TableCell className="py-2 sm:table-cell sm:w-[180px]">
                    <div className="flex items-center justify-between sm:justify-start">
                      <span className="sm:hidden font-medium">Vencimento:</span>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 flex-shrink-0" />
                        <span className="text-sm">{format(parseISO(payment.dueDate), 'dd LLL yy', { locale: ptBR, })}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="py-2 sm:table-cell sm:w-[100px] sm:text-right">
                    <SeeInvoice url={payment.invoiceUrl} />
                  </TableCell>
                </>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default Payments;