'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ead<PERSON>, CardTitle, } from '@/components/index';
import { CustomSubscription, } from '@/utils/constants/types';

const CreditCard = ({ subscription, }: { subscription: CustomSubscription }) => {
  return (
    <Card className="w-full bg-white shadow-lg rounded-lg overflow-hidden">
      <CardHeader className="bg-gray-50 border-b border-gray-200">
        <CardTitle className="flex justify-between items-center">
          <span className="text-xl font-bold text-gray-800">Cartão de crédito</span>
        </CardTitle>
      </CardHeader>
      <CardContent className='flex flex-col sm:grid-cols-2 gap-6 p-6'>
        <div className="flex items-end justify-between gap-2">
          <span className='text-base font-semibold'>Número:</span>
          <span className='text-muted-foreground'>**** **** **** {subscription.cardNumber}</span>
        </div>
        <div className="flex items-end justify-between gap-2">
          <span className='text-base font-semibold'>Operadora:</span>
          <span className='text-muted-foreground'>{subscription.cardBrand}</span>
        </div>
      </CardContent>
    </Card>
  );
};

export default CreditCard;