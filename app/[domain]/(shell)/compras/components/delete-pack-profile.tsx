'use client';

import { Button, } from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import React, { useState, } from 'react';
import { deletePackProfile, } from '../../alunos/actions';

const DeletePackProfile = ({ packProfileId, }: { packProfileId: number }) => {
  const [label, setLabel,] = useState('Desativar');

  const onClick = async () => {
    setLabel('Desativando...');
    const response = await deletePackProfile({ packProfileId, });
    if (response.status === 200) {
      setLabel('Desativado');
      toast({
        title: 'Pacote desativado com sucesso.',
        variant: 'default',
      });
    } else {
      setLabel('Desativar');
      toast({
        title: response.message,
        variant: 'destructive',
      });
    }
  };

  return (
    <Button variant='destructive' onClick={onClick}>{label}</Button>
  );
};

export default DeletePackProfile;