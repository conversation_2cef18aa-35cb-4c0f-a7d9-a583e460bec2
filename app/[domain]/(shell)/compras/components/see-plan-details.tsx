'use client';

import { createClient } from '@/utils/supabase/client';
import { User } from '@supabase/supabase-js';
import { ChevronRightIcon, } from 'lucide-react';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';
import Loading from '../loading';

const SeePlanDetailsLink = ({ userId, subscriptionId, }: { userId?: string, subscriptionId: string }) => {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    (async () => {
      const supabase = createClient();
      const { data: { user: authUser } } = await supabase.auth.getUser();
      setUser(authUser)
    })()
  },[])

  if (!user) <Loading />

  if (user?.user_metadata.role === 'admin') {
    return (
      <Link href={`/alunos/${userId}/assinatura/${subscriptionId}`} className='text-sm'><ChevronRightIcon /></Link>
    );
  }

  return (
    <Link href={`/compras/assinatura/${subscriptionId}`} className='text-sm'><ChevronRightIcon /></Link>
  );
};

export default SeePlanDetailsLink;