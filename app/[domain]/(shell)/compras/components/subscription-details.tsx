'use server';

import { format, } from 'date-fns';
import { ptBR, } from 'date-fns/locale';
import { paymentMethodsPt, } from '@/utils/supabase/constants';
import { CustomSubscription, } from '@/utils/constants/types';
import { Badge, Card, CardContent, CardHeader, CardTitle, Skeleton, } from '@/components/index';
import SeePlanDetailsLink from './see-plan-details';
import { formatToBrazilianReal, recurrencyLabel, } from '@/lib/utils';
import { Suspense, } from 'react';

type Props = {
  domain: string,
  userId?: string,
  subscription: CustomSubscription,
  withLink?: boolean
}

const getInvoicesLabel = (count: number) => {
  if (count === 0) return 'Em dia';
  return 'Atrasada';
};

const NextInvoiceDate = async ({ token, subscriptionId, }: { token: string, subscriptionId: string }) => {
  const pendingPaymentsResponse = await fetch(`https://api.asaas.com/v3/subscriptions/${subscriptionId}/payments?status=PENDING&limit=1`, {
    headers: {
      access_token: token,
      'User-Agent': 'meu-mestre',
    },
  });

  const pendingPayments = await pendingPaymentsResponse.json();

  if (pendingPayments.data && pendingPayments.data.length) {
    return (
      <div className="flex items-end justify-between gap-2">
      <span className='text-base font-semibold'>Próxima cobrança:</span>
      <span className='text-muted-foreground'>{format(pendingPayments.data[0]?.dueDate, 'dd MMM yyyy', { locale: ptBR, })}</span>
    </div>
    );
  }
};

const NextInvoiceDateSuspense = () => {
  return (
    <div className="flex items-end justify-between gap-2">
      <span className='text-base font-semibold'>Próxima cobrança:</span>
      <Skeleton className="h-4 w-[50px]" />
    </div>
  );
};

const InvoicesStatus = async ({ token, subscriptionId, }: { token: string, subscriptionId: string }) => {
  const response = await fetch(`https://api.asaas.com/v3/subscriptions/${subscriptionId}/payments?status=OVERDUE`, {
    headers: {
      access_token: token,
      'User-Agent': 'meu-mestre',
    },
  });

  const invoiceStatus = await response.json();

  return (
    <div className="flex items-end justify-between gap-2">
      <span className='text-base font-semibold'>Faturas:</span>
      <span className='text-muted-foreground'>{getInvoicesLabel(invoiceStatus.totalCount)}</span>
    </div>
  );
};

const InvoicesStatusSuspense = () => {
  return (
    <div className="flex items-end justify-between gap-2">
      <span className='text-base font-semibold'>Faturas:</span>
      <Skeleton className="h-4 w-[50px]" />
    </div>
  );
};

const SubscriptionCard = async  ({ domain, userId, subscription, withLink = false, }: Props) => {
  const tokenName = `AS_API_KEY_${domain}`;
  const token = process.env[tokenName] as string;

  return (
    <Card className="w-full bg-white shadow-lg rounded-lg overflow-hidden">
      <CardHeader className="bg-gray-50 border-b border-gray-200">
        <CardTitle className="flex justify-between items-center">
          <div>
            <span className="text-xl font-bold text-gray-800">{subscription.plan.name}</span>
            <Badge variant={subscription.status === 'ACTIVE' ? 'green' : 'secondary'} className="text-sm ml-4 font-medium">
              {subscription.status === 'ACTIVE'  ? 'Ativo' : 'Inativo'}
            </Badge>
          </div>
          <div>
            {withLink && <SeePlanDetailsLink userId={userId} subscriptionId={subscription.id} />}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col p-0">
        <div className='flex flex-col sm:grid-cols-2 gap-4 p-6'>
          <div className="flex items-end justify-between gap-2">
            <span className='text-base font-semibold'>Meio de pagamento:</span>
            <span className='text-muted-foreground'>{paymentMethodsPt[subscription.billingType as string]}</span>
          </div>
          <Suspense fallback={<InvoicesStatusSuspense />}>
            <InvoicesStatus token={token} subscriptionId={subscription.id}  />
          </Suspense>
          {subscription.planId && (
            <div className="hidden">
              <span className='text-base font-semibold'>Id Plano:</span>
              <span className='text-muted-foreground'>{subscription.planId}</span>
            </div>
          )}
          <div className="flex items-end justify-between gap-2">
            <span className='text-base font-semibold'>Frequência:</span>
            <span className='text-muted-foreground'>{recurrencyLabel(subscription.plan.frequency, subscription.plan.frequencyType)}</span>
          </div>
          <div className="hidden">
            <span className='text-base font-semibold'>Id Assinatura:</span>
            <span className='text-muted-foreground'>{subscription.id}</span>
          </div>
          {subscription.plan && (
            <div className="flex items-end justify-between gap-2">
              <span className='text-base font-semibold'>Preço:</span>
              <span className='text-muted-foreground'>{formatToBrazilianReal(subscription.plan.price)}</span>
            </div>
          )}
          {subscription.description && (
            <div className="flex items-end justify-between gap-2">
              <span className='text-base font-semibold'>Descrição:</span>
              <span className='text-muted-foreground'>{subscription.description}</span>
            </div>
          )}
          <Suspense fallback={<NextInvoiceDateSuspense />}>
            <NextInvoiceDate token={token} subscriptionId={subscription.id} />
          </Suspense>
        </div>
      </CardContent>
    </Card>
  );
};


export default SubscriptionCard;