'use server';

import React from 'react';
import SubscriptionCard from '../../components/subscription-details';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { CustomSubscription, Profile, } from '@/utils/constants/types';
import { NothingFound, } from '@/components/index';
import CreditCard from '../../components/credit-card';
import { redirect, } from 'next/navigation';
import Payments from '../../components/payments';

const SubscriptionDetails = async ({ params: { domain, subscriptionId, }, }: { params: { domain: string, subscriptionId: string } }) => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user) redirect('/login');

  const supabaseAdmin = createClientAdmin();

  const { data: subscription, }: { data: CustomSubscription | null } = await supabaseAdmin
    .from('subscription')
    .select('*, plan(*)')
    .match({
      id: subscriptionId,
      profileId: user.id,
    })
    .maybeSingle();

  if (!subscription || !subscription.plan) {
    return <NothingFound label='Não encontramos a assinatura.' />;
  }

  const { data: profile, }: { data: Profile | null } = await supabaseAdmin
    .from('profile')
    .select()
    .match({ id: subscription.profileId, })
    .maybeSingle();

  if (!profile) redirect('/login');

  return (
    <div className='flex flex-col w-full gap-10'>
      <SubscriptionCard domain={domain} subscription={subscription} />
      {subscription.billingType === 'CREDIT_CARD' && <CreditCard subscription={subscription}  />}
      <Payments subscriptionId={subscription.id} />
    </div>
  );
};

export default SubscriptionDetails;