'use server';

import { <PERSON>Found, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger, } from '@/components/index';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { redirect, } from 'next/navigation';
import PackDetails from '../../../../components/ui/pack-details';
import SubscriptionCard from './components/subscription-details';
import { CreditCardToken, CustomSubscription, Profile, Pack, PackProfile, Payment, SaleRoot, } from '@/utils/constants/types';

const Purchases = async ({ params: { domain, }, }: { params: { domain: string } }) => {
  const supabase = createClient();
  const supabaseAdmin = createClientAdmin();
  const { data: { user, },} = await supabase.auth.getUser();
  if (!user) return redirect('/login');

  const { data: profile, }: { data: Profile | null } = await supabaseAdmin.from('profile').select().match({ id: user.id, }).maybeSingle();

  if (!profile) return redirect('/login');

  let { data: subscriptions, }: { data: (CustomSubscription & { card: CreditCardToken })[] | null } = await supabaseAdmin
    .from('subscription')
    .select('*, plan(*)')
    .order('status', { ascending: true, })
    .match({ profileId: profile.id, });

  let { data: packProfiles, } = await supabaseAdmin
  .from('pack_profile')
  .select('*, pack(*), payment(*), sale_root(*)')
  .match({ profileId: user.id, })
  .order('purchaseStatus', { ascending: false, })
  .order('createdAt', { ascending: false, })
  .returns<(PackProfile & { pack: Pack, payment: Payment, sale_root: SaleRoot })[] | null>();

  return (
    <Tabs defaultValue='packs' className="container mx-auto p-4">
      <TabsList>
        <TabsTrigger value="plans">Assinatura</TabsTrigger>
        <TabsTrigger value="packs">Pacotes</TabsTrigger>
      </TabsList>
      <TabsContent value='plans' className='flex flex-col gap-10'>
        {(!subscriptions || !subscriptions.length) && (
          <NothingFound label='Nenhuma assinatura encontrada.' />
        )}
        {subscriptions && subscriptions.map(subscription => (
          <SubscriptionCard
            domain={domain}
            key={subscription.id}
            subscription={subscription}
            withLink={true} />
        ))}
      </TabsContent>
      <TabsContent value='packs' className='flex flex-col gap-10'>
        {(!packProfiles || !packProfiles.length) && (
          <NothingFound label='Nenhuma pacote encontrado.' />
        )}
        {packProfiles && packProfiles.map(packProfile => (
          <PackDetails
            key={packProfile.id}
            userId={user.id}
            packProfile={packProfile}
          />
        ))}
      </TabsContent>
    </Tabs>
  );
};

export default Purchases;