'use client';

import { formatToBrazilianReal, recurrencyLabel, } from '@/lib/utils';
import PaymentForm from './components/payment-form';
import { Plan, Profile, Subdomains, } from '@/utils/constants/types';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/index';
import { useEffect, useState, } from 'react';
import { getPlan, getProfile, } from './actions';
import { toast, } from '@/components/ui/shard/use-toast';
import Loading from '../loading';
import BoletoPix from './components/boleto-pix';

const Plans = ({ params: { domain, planId, }, }: { params: { domain: Subdomains, planId: string } }) => {
  const [activeTab, setActiveTab,] = useState('credit-card');
  const [plan, setPlan,] = useState<Plan | null>(null);
  const [profile, setProfile,] = useState<Profile | null>(null);

  useEffect(() => {
    if (!planId) return;
    (async () => {
      const profileResponse = await getProfile();
      if (profileResponse.data) {
        setProfile(profileResponse.data);
      }

      if (profileResponse.error) {
        toast({
          variant: 'destructive',
          title: 'Erro ai buscar perfil',
        });
        return;
      }
      const response = await getPlan(planId);
      if (response.status === 200) {
        setPlan(response.data);
      } else {
        toast({
          variant: 'destructive',
          description: response.message,
        });
      }
    })();
  }, [planId,]);

  if (!plan) return <Loading />;

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">Finalizar Compra</CardTitle>
        <CardDescription className="text-center">
          Você está contratando o plano <span className="font-semibold">{plan.name}</span> no valor de{' '}
          <span className="font-semibold">{formatToBrazilianReal(plan.price)}</span>
        </CardDescription>
        <p className="text-sm text-muted-foreground text-center mt-2">
          Esse valor será cobrado com recorrência {recurrencyLabel(plan.frequency, plan.frequencyType)}.
        </p>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="credit-card">Cartão</TabsTrigger>
            <TabsTrigger value="boleto">Boleto</TabsTrigger>
            <TabsTrigger value="pix">Pix</TabsTrigger>
          </TabsList>
          <TabsContent value="credit-card">
            <PaymentForm plan={plan} domain={domain} profile={profile} />
          </TabsContent>
          <TabsContent value="boleto">
            <BoletoPix domain={domain} plan={plan} profile={profile} type="BOLETO" />
          </TabsContent>
          <TabsContent value="pix">
            <BoletoPix domain={domain} plan={plan} profile={profile} type="PIX" />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default Plans;