'use server';

import { BillingType, Cycles, Plan, Profile, School, Subdomains, } from '@/utils/constants/types';
import { FormData, } from './components/payment-form';
import { createClientAdmin, } from '@/utils/supabase/server';
import { UNAUTHORIZED, } from '@/utils/constants';
import { getCycle, nextDateWithDay, } from '@/lib/utils';
import { headers, } from 'next/headers';
import { format, } from 'date-fns';
import { userInfo, } from '@/supabase/verifications/user-info';
import { PostgrestSingleResponse, } from '@supabase/supabase-js';

export async function getIPAddress() {
    return headers().get('x-forwarded-for');
}

type CreditCardType = { type: 'CREDIT_CARD', formData: FormData }
type OtherType = {
  type: Exclude<BillingType, 'CREDIT_CARD'>,
  formData: { selectedDay: string }
}

type FormDataProp = {
  domain: Subdomains
  plan: Plan,
} & (CreditCardType | OtherType)

type SubscriptionAs = {
	object: 'subscription',
	id: string,
	dateCreated: Date,
	customer: string,
	value: number,
	nextDueDate: Date,
	cycle: Cycles,
	description: string,
	billingType: BillingType,
	deleted: false,
	status: 'ACTIVE',
	externalReference: null,
}
type ResponseType = { data: SubscriptionAs, status: 201 } | { message: string, status: 400 | 401 }

type Body = {
  billingType: BillingType,
  customer: string | null,
  creditCard?: {
    holderName: string,
    number: string,
    expiryMonth: string,
    expiryYear: string,
    ccv: string,
  },
  creditCardHolderInfo?: {
    name: string,
    email: string,
    cpfCnpj: string,
    postalCode: string,
    phone: string,
    addressNumber: string,
  }
  cycle: Cycles,
  description: string,
  externalReference: string,
  value: number,
  nextDueDate?: string | null,
  remoteIp: string | null,
  interest?: {
    value: number | null
  },
  fine?: {
    value: number | null,
    type: 'FIXED' | 'PERCENTAGE' | null
  },
}

type GetBody = {
  asId: string,
  type: BillingType,
  plan: Plan
}

export const getProfile = async () => {
  const user = await userInfo();
  if (!user) return { message: UNAUTHORIZED, status: 401, };

  const supabaseAdmin = createClientAdmin();
  const { data, error, }: PostgrestSingleResponse<Profile | null> = await supabaseAdmin
  .from('profile')
  .select()
  .match({ id: user.id, })
  .maybeSingle();

  return { data, error, };
};

const getSchool = async ({ schoolId, }: { schoolId: string }) => {
  const supabaseAdmin = createClientAdmin();
  const { data, error, }: PostgrestSingleResponse<School | null> = await supabaseAdmin
    .from('school')
    .select()
    .match({ id: schoolId, })
    .maybeSingle();
  return { data, error, };
};

const getBody = async ({ asId, type, plan, }: GetBody) => {
  const ipAddress = await getIPAddress();
  let body: Body = {
    customer: asId,
    billingType: type,
    value: plan.price,
    cycle: getCycle(plan.frequency, plan.frequencyType),
    externalReference: `plan:${plan.id}`,
    remoteIp: ipAddress,
    description: plan.name,
    ...(plan.fine && { fine: { value: plan.fine, type: plan.fineType, }, }),
    ...(plan.interest && { interest: { value: plan.interest, }, }),
  };

  return body;
};

const getCreditCardInfo = ({ formData, }: { formData: FormData }) => {
  const today = new Date();
  today.setUTCHours(today.getUTCHours() - 3);
  const nextDay = format(today, 'yyyy-MM-dd');

  return {
    creditCard: {
      holderName: formData.holderName,
      number: formData.cardNumber,
      expiryMonth: formData.expiryMonth,
      expiryYear: formData.expiryYear,
      ccv: formData.ccv,
    },
    creditCardHolderInfo: {
      name: formData.name,
      email: formData.email,
      cpfCnpj: formData.cpf,
      postalCode: formData.cep,
      phone: formData.phone,
      addressNumber: formData.addressNumber,
    },
    nextDueDate: nextDay,
  };
};

export const createSubscription = async ({ formData, plan, type, }: FormDataProp): Promise<ResponseType> => {
  const profileResponse = await getProfile();

  if (profileResponse.error) {
    return { message: UNAUTHORIZED, status: 401, };
  }

  if (profileResponse.data) {
    if (!profileResponse.data.asId) return { message: 'Usuário sem conta no asaas.', status: 400, };

    const schoolResponse = await getSchool({ schoolId: profileResponse.data.schoolId, });

    if (schoolResponse.error) return { message: 'Domínio não encontrado.', status: 400, };

    if (schoolResponse.data) {
      let body = await getBody({
        asId: profileResponse.data.asId,
        type,
        plan,
      });

      if (type === 'CREDIT_CARD') {
        const creditCardInfo = getCreditCardInfo({ formData, });
        body = { ...body, ...creditCardInfo, };
      }

      if (type !== 'CREDIT_CARD') {
        body.nextDueDate = nextDateWithDay(parseInt(formData.selectedDay));
      }
      console.log('🚀 ~ BODY', body);
      const response = await fetch('https://api.asaas.com/v3/subscriptions/', {
        body: JSON.stringify(body),
        headers: {
          access_token: process.env[`AS_API_KEY_${schoolResponse.data.domain}`],
          'User-Agent': 'meu-mestre',
        },
        method: 'POST',
      });
      console.log('🚀 ~ createSubscription ~ response:', response);

      if (response.status === 200) {
        const data = await response.json();
        console.log('🚀 ~ createSubscription ~ data:', data);
        return { data, status: 201, };
      }
    }
  }

  return { message: 'Não foi possível criar assinatura.', status: 400, };
};

export const getPlan = async (planId: string): Promise<({ data: Plan, status: 200 } | { message: string, status: 404 })> => {
  const supabase = createClientAdmin();
  const { data, } = await supabase
    .from('plan')
    .select()
    .match({ id: planId, })
    .maybeSingle();

  if (!data) return { message: 'Nenhum plano encontado.', status: 404, };

  return { data, status: 200, };
};
