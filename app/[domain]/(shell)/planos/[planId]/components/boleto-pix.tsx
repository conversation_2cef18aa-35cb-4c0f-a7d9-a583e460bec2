'use client';

import {
  Button,
  Label,
  NothingFound,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import { createSubscription, } from '../actions';
import { BillingType, Plan, Profile, Subdomains, } from '@/utils/constants/types';
import { useRouter, } from 'next/navigation';
import { getSchoolSettings, } from '@/app/actions';
import { useEffect, useState, } from 'react';

type Props = {
  domain: Subdomains,
  plan: Plan,
  type: Exclude<BillingType, 'CREDIT_CARD'>,
  profile: Profile | null
}

const BoletoPix = ({ domain, plan, type, profile, }: Props) => {
  const [selectedDay, setSelectedDay,] = useState<string>('5');
  const [label, setLabel,] = useState<string>(type === 'BOLETO' ? 'Enviar Boleto' : 'Enviar QR Code PIX');
  const [chargeDays, setChargeDays,] = useState([]);
  const router = useRouter();

  const submit = async () => {
    setLabel('Enviando');
    const response = await createSubscription({
      domain,
      formData: { selectedDay, },
      plan,
      type,
    });
    if (response.status === 201) {
      toast({
        description: 'Assinatura criada com sucesso. Verique seu email.',
      });
      router.push('/compras');
    } else {
      toast({
        variant: 'destructive',
        description: response.message,
      });
      setLabel('Assinar');
    }
  };

  useEffect(() => {
    (async () => {
      const response = await getSchoolSettings();
      if (response.status === 200) {
        setChargeDays(response.data.chargedays);
      }
    })();
  }, []);

  if (!profile) return null;

  {!profile.asId && (
    <NothingFound label='Usuário não tem conta no banco provedor.' />
  );}

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="boleto-days">Dia da fatura</Label>
        <Select onValueChange={setSelectedDay}>
          <SelectTrigger id="boleto-days">
            <SelectValue placeholder="Selecione o dia" />
          </SelectTrigger>
          <SelectContent>
            {chargeDays.map((day) => (
              <SelectItem key={day} value={day.toString()}>
                {day}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <Button onClick={submit} className="w-full">
        {label}
      </Button>
    </div>
  );
};

export default BoletoPix;