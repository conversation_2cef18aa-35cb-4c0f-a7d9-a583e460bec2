'use client';

import { <PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON>ooter, CardTitle, Separator, } from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import { formatToBrazilianReal, recurrencyLabel, } from '@/lib/utils';
import { Plan, } from '@/utils/constants/types';
import { Check, } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

const Subscription = ({ isAdminPage, plan, }: { isAdminPage?: boolean, plan: Plan }) => {
  return (
    <Card key={plan.id} className='flex flex-col w-full max-w-96 h-fit'>
      <div className="py-4 p-5 flex flex-row justify-between items-center">
        <CardTitle>{plan.name}</CardTitle>
        <CardTitle>{formatToBrazilianReal(plan.price)} <span className='text-xs'>{recurrencyLabel(plan.frequency, plan.frequencyType)}</span></CardTitle>
      </div>
      <Separator />
      <CardContent className='flex flex-col p-6 gap-4'>
        <div className="flex items-center">
          <Check className="mr-2 h-4 w-4 text-primary" />
          <span>Acesso a todos os cursos e aulas</span>
        </div>
        <div className="flex items-center">
          <Check className="mr-2 h-4 w-4 text-primary" />
          <span>Receba notificações da sua academia</span>
        </div>
        <div className="flex items-center">
          <Check className="mr-2 h-4 w-4 text-primary" />
          <span>Acompanhe seu progresso</span>
        </div>
        <CardDescription className='text-xs text-center mx-auto block'>Valor cobrado agora e depois em intervalo {recurrencyLabel(plan.frequency, plan.frequencyType)}.</CardDescription>
      </CardContent>
      {isAdminPage && (
        <CardFooter>
          <Button onClick={() => {
            navigator.clipboard.writeText(`${window.location.href}/${plan.id}`);
            toast({
              title: 'Link copiado.',
            });
          }} className='w-full'>Copiar link</Button>
        </CardFooter>
      )}
      {!isAdminPage && (
        <CardFooter>
          <Link className='w-full' href={`/planos/${plan.id}`}><Button className='w-full'>Assinar agora</Button></Link>
        </CardFooter>
      )}
    </Card>
  );
};

export default Subscription;