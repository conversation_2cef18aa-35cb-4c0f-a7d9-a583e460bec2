import { Button, NothingFound, } from '@/components/index';
import SubscriptionCard from './components/subscription-card';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { Plan, } from '@/utils/constants/types';
import Link from 'next/link';
import { Plus, } from 'lucide-react';

const Plans = async () => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  const isAdmin = user?.user_metadata.role === 'admin';

  const supabaseAdmin = createClientAdmin();
  const { data: plans, } = await supabaseAdmin
  .from('plan')
  .select()
  .match({ schoolId: user?.user_metadata.schoolId, active: true, display: true, })
  .returns<Plan[] | null>();

  return (
    <div className='grid grid-cols-auto-fill-300 gap-10'>
      {plans && plans.length > 0 && plans.map((plan) => <SubscriptionCard key={plan.id} plan={plan} isAdminPage={isAdmin} />)}
      {!plans && (<NothingFound label='Erro ao buscar planos.' />)}
      {plans && plans.length === 0 && (<NothingFound label='Nenhum plano encontrado.' />)}
      <div className="fixed bottom-6 right-6 z-50">
        <Button asChild size="lg" className="rounded-full shadow-lg h-14 w-14 p-0 md:w-auto md:px-4 md:h-12">
          <Link href='/criar-plano'>
            <Plus className="h-6 w-6" />
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default Plans;