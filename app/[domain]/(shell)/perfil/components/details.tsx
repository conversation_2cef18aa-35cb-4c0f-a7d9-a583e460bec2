'use client';

import { ChangeEvent, useEffect, useState, } from 'react';
import { useF<PERSON>, Controller, } from 'react-hook-form';
import { zodResolver, } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button, Card, CardContent, Input, Label, ProfileImage, } from '@/components/index';
import NewPassword from '@/components/ui/new-password';
import { URLS, } from '@/utils/supabase/constants';
import { formatCep, formatDate, formatPhoneNumber, handleCpfChange, validateCPF, } from '@/lib/utils';
import { createClient, } from '@/utils/supabase/client';
import { format, parse, } from 'date-fns';
import { toast, } from '@/components/ui/shard/use-toast';
import { Profile, } from '@/utils/constants/types';

const profileSchema = z.object({
  birthdate: z.string({ required_error: 'Campo obrigatório.', }).regex(/^\d{2}\/\d{2}\/\d{4}$/, 'Formato inválido.'),
  cpf: z.string({ required_error: 'Campo obrigatório.', }).regex(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/, 'Formato inválido.').refine(validateCPF, { message: 'CPF inválido.',  }),
  cep: z.string({ required_error: 'Campo obrigatório.', }).length(9, { message: 'CEP deve ter 9 caracteres.', }),
  city: z.string({ required_error: 'Campo obrigatório.', }).max(50, { message: 'Cidade deve ter entre 5 e 50 caracteres.', }),
  name: z.string({ required_error: 'Campo obrigatório.', }).min(4, { message: 'Deve ter no mínimo 4 letras', }),
  neighborhood: z.string({ required_error: 'Campo obrigatório.', }).max(50, { message: 'Bairro deve ter entre 5 e 50 caracteres.', }),
  phone: z.string({ required_error: 'Campo obrigatório.', }).min(14, { message: 'Telefone deve ter pelo menos 14 caracteres.', }),
  state: z.string({ required_error: 'Campo obrigatório.', }).length(2, { message: 'Deve ter 2 letras', }),
  street: z.string({ required_error: 'Campo obrigatório.', }).max(100, { message: 'Endereço deve ter entre 4 e 100 caracteres', }),
});

type ProfileFormData = z.infer<typeof profileSchema>;

export default function UserProfile({ domain, profile, }: { domain: string, profile: Profile }) {
  const [profileImage, setProfileImage,] = useState<string | null>(null);
  const [saveLabel, setSaveLabel,] = useState<string>('Salvar');
  const supabase = createClient();

  const { control, handleSubmit, formState: { errors, }, setValue, getValues, } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      birthdate: '',
      cep: '',
      cpf: '',
      city: '',
      name: '',
      neighborhood: '',
      phone: '',
      state: '',
      street: '',
    },
  });

  useEffect(()=> {
    if(profile) {
      if (profile.birthdate) setValue('birthdate', format(profile.birthdate.replaceAll('-', '/'), 'dd/MM/yyyy'));
      if (profile.cep) setValue('cep', profile.cep);
      if (profile.city) setValue('city', profile.city);
      if (profile.cpf) setValue('cpf', profile.cpf);
      if (profile.name) setValue('name', profile.name);
      if (profile.neighborhood) setValue('neighborhood', profile.neighborhood);
      if (profile.phone) setValue('phone', profile.phone);
      if (profile.profileImage) setProfileImage(profile.profileImage);
      if (profile.state) setValue('state', profile.state);
      if (profile.street) setValue('street', profile.street);
    }
  }, [profile,]);


  const handleBirthdateChange = (e: ChangeEvent<HTMLInputElement>) => {
    const formatted = formatDate(e.target.value);
    setValue('birthdate', formatted, { shouldValidate: true, });
  };

  const handleCepChange = (e: ChangeEvent<HTMLInputElement>) => {
    const formattedValue = formatCep(e.target.value);

    setValue('cep', formattedValue, { shouldValidate: true, });
    if (formattedValue.length === 9) getAddressByCep();
  };

  const handlePhoneChange = (e: ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    setValue('phone', formatted, { shouldValidate: true, });
  };

  const onCpfChange = (e: ChangeEvent<HTMLInputElement>) => {
    const formattedValue = handleCpfChange(e.target.value);
    setValue('cpf', formattedValue, { shouldValidate: true, });
  };

  const onSubmit = async (formData: ProfileFormData) => {
    setSaveLabel('Salvando dados');
    await supabase.auth.updateUser({
      data: {
        name: getValues('name'),
      },
    });
    const response = await fetch(URLS.USER.UPDATE, {
      method: 'PATCH',
      body: JSON.stringify({
        ...formData,
        birthdate: format(parse(formData.birthdate, 'dd/MM/yyyy', new Date()), 'yyyy/MM/dd'),
      }),
    });
    if (response.status === 200) {
      toast({
        title: 'Dados salvos com sucesso.',
      });
      setSaveLabel('Salvo');
    } else {
      setSaveLabel('Salvar');
    }
  };

  const getAddressByCep = async () => {
    const response = await fetch(`${URLS.BRASIL_ABERTO}/${getValues('cep')}`);
    if (response.status === 200) {
      const data = await response.json();
      if (!data) return;
      setValue('state', data.state);
      setValue('city', data.city);
      setValue('neighborhood', data.neighborhood);
      setValue('street', data.street);
    }
  };

  return (
    <div className='container mx-auto p-4'>
      <Card className='mb-10 pt-4'>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
            <ProfileImage
              type='profile'
              domain={domain}
              folder='avatars'
              profileImage={profileImage || profile?.profileImage}
              faceImage={profile?.faceImage}
            />
            <div className='space-y-2'>
              <Label htmlFor='name'>Nome</Label>
              <Controller
                name='name'
                control={control}
                render={({ field, }) => <Input {...field} id='name' />}
              />
              {errors.name && <p className='text-red-500 text-sm'>{errors.name.message}</p>}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='birthdate'>Data de Nascimento</Label>
              <Controller
                name='birthdate'
                control={control}
                render={({ field, }) =>
                  <Input
                    id='birthdate'
                    {...field}
                    onChange={handleBirthdateChange}
                    maxLength={10}
                  />
                }
              />
              {errors.birthdate && <p className='text-red-500 text-sm'>{errors.birthdate.message}</p>}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='phone'>Telefone</Label>
              <Controller
                name='phone'
                control={control}
                render={({ field, }) =>
                  <Input
                    id='phone'
                    {...field}
                    onChange={handlePhoneChange}
                    maxLength={14}
                  />
                }
              />
              {errors.phone && <p className='text-red-500 text-sm'>{errors.phone.message}</p>}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='cpf'>CPF</Label>
              <Controller
                name='cpf'
                control={control}
                render={({ field, }) =>
                  <Input
                    id='cpf'
                    {...field}
                    onChange={onCpfChange}
                    maxLength={14}
                  />
                }
              />
              {errors.cpf && <p className='text-red-500 text-sm'>{errors.cpf.message}</p>}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='cep'>CEP</Label>
              <Controller
                name='cep'
                control={control}
                render={({ field, }) =>
                  <Input
                    id='cep'
                    placeholder='00000-000'
                    {...field}
                    onChange={handleCepChange}
                    maxLength={10}
                  />}
              />
              {errors.cep && <p className='text-red-500 text-sm'>{errors.cep.message}</p>}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='address'>Endereço</Label>
              <Controller
                name='street'
                control={control}
                render={({ field, }) => <Input {...field} id='address' placeholder='Rua e Número' />}
              />
              {errors.street && <p className='text-red-500 text-sm'>{errors.street.message}</p>}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='city'>Cidade</Label>
              <Controller
                name='city'
                control={control}
                render={({ field, }) => <Input {...field} id='city' />}
              />
              {errors.city && <p className='text-red-500 text-sm'>{errors.city.message}</p>}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='neighborhood'>Bairro</Label>
              <Controller
                name='neighborhood'
                control={control}
                render={({ field, }) => <Input {...field} id='neighborhood' />}
              />
              {errors.neighborhood && <p className='text-red-500 text-sm'>{errors.neighborhood.message}</p>}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='state'>Estado</Label>
              <Controller
                name='state'
                control={control}
                render={({ field, }) => <Input {...field} id='state' maxLength={2} />}
              />
              {errors.state && <p className='text-red-500 text-sm'>{errors.state.message}</p>}
            </div>
            <Button type='submit' className='min-w-28'>{saveLabel}</Button>
          </form>
        </CardContent>
      </Card>
      <NewPassword />
    </div>
  );
}