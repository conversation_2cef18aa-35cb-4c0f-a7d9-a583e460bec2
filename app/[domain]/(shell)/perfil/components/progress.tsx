'use client';

import { useState, useEffect, } from 'react';
import { Belt, Button, Calendar, Card, CardContent, CardHeader, CardTitle, Skeleton, Table, TableBody, TableCell, TableHead, TableHeader, TableRow, } from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import { Attendance, Rank, Sport, } from '@/utils/constants/types';
import { format, parseISO, } from 'date-fns';
import { ptBR, } from 'date-fns/locale';
import { deleteAttendance, getSchedulesOnDate, getUserInfo, saveAttendance, } from '../actions';
import { getToday, } from '@/lib/utils';

const groupAttendancesByService = (attendances: Attendance[]) => {
  const grouped = {};

  attendances.forEach(attendance => {
    const serviceId = attendance.service.id; // Assuming 'service' has an 'id'

    if (!grouped[serviceId]) {
      grouped[serviceId] = {
        name: attendance.service.title,
        serviceId: serviceId,
        attendances: [],
      };
    }

    grouped[serviceId].attendances.push({
      confirmed: attendance.confirmed,
      date: attendance.date,
      profile: attendance.profile,
      schedule: attendance.schedule,
      service: attendance.service,
      userId: attendance.userId,
    });
  });

  return Object.values(grouped);
};

const getStatusLabel = (attendance: Attendance) => {
  if (attendance.confirmed === false) return 'Aguardando Confirmação';
  if (attendance.confirmed === true) return 'Presente';
  return 'Faltou';
};

const getStatusColor = (attendance: Attendance) => {
  if (attendance.confirmed === false) return 'text-orange-700';
  if (attendance.confirmed === true) return 'bg-green-50 text-green-700';
  return 'bg-red-50 text-red-700';
};

const Progress = ({ sport, }: { sport: Sport }) => {
  const today = getToday();
  const [selectedDate, setSelectedDate,] = useState(today);
  const [schedules, setSchedules,] = useState<{ id: string, name: string, attendances: Attendance[] }[]>([]);
  const [rank, setRank,] = useState<Rank & { points: number, nextRank: Rank & { classes: number } } | null>(null);
  const [attendances, setAttendances,] = useState<Attendance[]>([]);
  const [revalidate, setRevalidatePath,] = useState(false);

  useEffect(() => {
    (async () => {
      if (!sport?.id) return;
      const response = await getUserInfo({ sportId: sport?.id, });
      if (response.status === 200) {
        setRank(response.data.rank);
        setAttendances(response.data.attendances || []);
      }
    })();
  }, [sport?.id, revalidate,]);

  useEffect(() => {
    (async () => {
      if (!selectedDate || !sport) return;
      const response = await getSchedulesOnDate({ date: selectedDate, });
      if (response.status === 200 && response.data) {
        const schedulesFormatted = response.data.map(attendance => {
          const matchedAttendance = attendances.find(registererAttendance => registererAttendance.date === attendance.date && registererAttendance.scheduleId === attendance.schedule.id && registererAttendance.serviceId === attendance.service.id);
          return {
            ...attendance,
            confirmed: matchedAttendance?.confirmed,
          };
        });
        const grouped = groupAttendancesByService(schedulesFormatted);
        setSchedules(grouped);
      }
    })();
  }, [selectedDate, sport, attendances,]);

  const handleCheckIn = async ({ scheduleId, serviceId, }: { scheduleId: string, serviceId: string, }) => {
    const saveAttendanceResponse = await saveAttendance({
      serviceId,
      date: selectedDate,
      scheduleId,
    });
    if (saveAttendanceResponse.status === 200) {
      toast({
        title: 'Check In feito com sucesso.',
      });
      setRevalidatePath(!revalidate);
    } else {
      toast({
        variant: 'destructive',
        title: saveAttendanceResponse.message,
      });
    }
  };

  const deleteCheckIn = async ({ scheduleId, serviceId, }: { scheduleId: string, serviceId: string, }) => {
    const deleteAttendanceResponse = await deleteAttendance({
      serviceId,
      date: selectedDate,
      scheduleId,
    });
    if (deleteAttendanceResponse.status === 200) {
      toast({
        title: 'Check In removido com sucesso.',
      });
      setRevalidatePath(!revalidate);
    } else {
      toast({
        variant: 'destructive',
        title: deleteAttendanceResponse.message,
      });
    }
  };

  return (
    <div className='flex flex-col gap-2'>
      <Card>
        <CardHeader>
          <CardTitle>{sport?.name}</CardTitle>
          {!!attendances.length && <p>{attendances.filter(att => att.confirmed).length} de {rank?.nextRank.classes} aulas nessa faixa.</p>}
          {rank && rank.points > 0 && (<p>{rank.points} pontos de desempenho.</p>)}
        </CardHeader>
        <CardContent className='space-y-6'>
          {rank && (
            <Belt
              rank={rank}
              required={rank.nextRank.classes}
              attendance={attendances.filter(att => att.confirmed).length + rank.points}
            />
          )}
          {!rank && (<Skeleton className='h-8 w-full' />)}
        </CardContent>
      </Card>
      <Card>
        <CardHeader className='p-4'><span className='text-muted-foreground'>Presenças</span></CardHeader>
        <CardContent>
          <Calendar
            mode='multiple'
            initialFocus
            locale={ptBR}
            selected={attendances.map(att => parseISO(att.date))}
            onDayClick={setSelectedDate}
            className='rounded-md border'
            classNames={{ day_selected: 'bg-green-300', }}
          />
          {selectedDate && <p className='text-lg font-semibold m-4'>{format(selectedDate, 'd MMMM yyyy', { locale: ptBR, })}</p>}
          <div className='flex flex-col gap-2'>
            {schedules.map(({ id, name, attendances, }) => (
              <Card key={id}>
                <CardHeader><span>{name}</span></CardHeader>
                <CardContent className='p-0'>
                  <Table key={id}>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Horário</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className='text-right'>Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody className='p-0'>
                      {attendances && attendances.map((attendance) => (
                        <TableRow key={id}>
                          <TableCell className='pl-4 sm:pl-5'>{attendance.schedule.hour}</TableCell>
                          <TableCell className='px-0'>
                            <span
                              className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                                getStatusColor(attendance)
                              }`}
                            >
                              {getStatusLabel(attendance)}
                            </span>
                          </TableCell>
                          <TableCell className='px-0 text-center text-right'>
                            {attendance.confirmed === undefined && (
                              <Button
                                variant='ghost'
                                size='sm'
                                onClick={() => handleCheckIn({
                                  serviceId: attendance.service.id,
                                  scheduleId: attendance.schedule.id,
                                })}
                              >
                                Atualizar
                              </Button>
                            )}
                            {attendance.confirmed !== undefined && (
                              <Button
                                variant='ghost'
                                size='sm'
                                onClick={() => deleteCheckIn({
                                  serviceId: attendance.service.id,
                                  scheduleId: attendance.schedule.id,
                                })}
                              >
                                Remover
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Progress;

