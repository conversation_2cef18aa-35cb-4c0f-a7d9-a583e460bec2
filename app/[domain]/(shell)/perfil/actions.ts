'use server';

import { userInfo, } from '@/supabase/verifications/user-info';
import { Attendance, Rank, Service, ServiceStudent, } from '@/utils/constants/types';
import { createClientAdmin, } from '@/utils/supabase/server';
import { format, getDay, } from 'date-fns';
import { revalidatePath, } from 'next/cache';

export const getStudentServices = async (): Promise<{ data: Service[], status: 200 } | { message: string, status: 400 }> => {
  const user = await userInfo();
  const supabaseAdmin = createClientAdmin();
  const { data: serviceStudent, } = await supabaseAdmin
    .from('service_student')
    .select('service(*)')
    .match({ profileId: user?.id, })
    .returns<{ service: Service }[] | null>();

  console.log('🚀 ~ getStudentServices ~ data:', serviceStudent);
  let services: Service[] | null = [];

  if (!serviceStudent) return {
    message: 'Erro ao buscar serviços.',
    status: 400,
  };

  if (serviceStudent.length === 0) {
    const { data, } = await supabaseAdmin
      .from('service')
      .select()
      .match({ schoolId: user?.user_metadata.schoolId, })
      .returns<Service[]>();

    services = data;

    console.log('🚀 ~ getCurrentService ~ services 2:', services);
    if (!services || services.length === 0) return {
      message: 'Não encontramos serviços cadastrados.',
      status: 400,
    };
  } else {
    services = serviceStudent.map(studentService => studentService.service);
  }

  return {
    data: services,
    status: 200,
  };
};

export const getSchedulesOnDate = async ({ date, }: { date: Date }) => {
  const user = await userInfo();
  const supabaseAdmin = createClientAdmin();
  const servicesResponse = await getStudentServices();

  if (servicesResponse.status === 400) return {
    message: 'Erro ao buscar serviços.',
    status: 400,
  };

  const services = servicesResponse.data;

  const { data: schedules, } = await supabaseAdmin
    .from('schedule')
    .select('*, service(*)')
    .match({ number: getDay(date), })
    .in('serviceId', services.map(service => service.id));

  if (!schedules) return {
    messsage: 'Erro ao buscar agenda.',
    status: 400,
  };

  const formatted = schedules.map(schedule => ({
    confirmed: false,
    date: format(date, 'yyyy-MM-dd'),
    profile: {
      id: user?.id,
      name: user?.user_metadata.name,
    },
    schedule,
    service: schedule.service,
    userId: user?.id,
  }));

  return {
    data: formatted,
    status: 200,
  };
};

export const saveAttendance = async ({ scheduleId, serviceId, date, confirmed = false, }: { scheduleId: string, serviceId: string, date: Date, confirmed?: boolean }) => {
  console.log('🚀 ~ saveAttendance ~ confirmed:', confirmed);
  const user = await userInfo();
  console.log('🚀 ~ saveAttendance ~ user:', user);
  const supabaseAdmin = createClientAdmin();

  const { data, } = await supabaseAdmin
    .from('attendance')
    .insert({
      scheduleId,
      serviceId,
      confirmed,
      userId: user.id,
      date: format(date, 'yyyy-MM-dd'),
    })
    .select();

  if (!data) return {
    message: 'Erro ao salvar presença.',
    status: 400,
  };

  revalidatePath('/perfil');

  return {
    data,
    status: 200,
  };
};

export const deleteAttendance = async ({ scheduleId, serviceId, date, }: { scheduleId: string, serviceId: string, date: Date }) => {
  const user = await userInfo();
  const supabaseAdmin = createClientAdmin();

  const { error, } = await supabaseAdmin
    .from('attendance')
    .delete()
    .match({
      scheduleId,
      serviceId,
      userId: user.id,
      date: format(date, 'yyyy-MM-dd'),
    });

  if (error) return {
    message: 'Erro ao salvar presença.',
    status: 400,
  };

  revalidatePath('/perfil');

  return {
    status: 200,
  };
};

type GetUserInfoResponse = {
  id: string,
  rank: Rank & { points: number, nextRank: Rank & { classes: number } }
  attendances: Attendance[]
}
export const getUserInfo = async ({ sportId, }: { sportId: string }): Promise<{ data: GetUserInfoResponse, status: 200 } | { message: string, status: 400 }> => {
  const user = await userInfo();
  const supabaseAdmin = createClientAdmin();
  const { data, error, } = await supabaseAdmin
    .rpc('get_user_info', {
      userid: user.id,
      sportid: sportId,
    })
    .select();

  if (error) return {
    message: 'Erro ao buscar dados do usuário.',
    status: 400,
  };

  return {
    data,
    status: 200,
  };
};