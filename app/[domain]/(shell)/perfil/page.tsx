'use server';

import Details from './components/details';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { redirect, } from 'next/navigation';
import { Profile, Subdomains, } from '@/utils/constants/types';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger, } from '@/components/index';
import Progress from './components/progress';
import { getProfileSports, } from '@/app/actions';
import { sportHasRank, } from '../alunos/actions';

const UserProfile = async ({ params: { domain, },}: { params: { domain: Subdomains } }) => {
  const supabase = createClient();
  const supabaseAdmin = createClientAdmin();
  const { data: { user, },} = await supabase.auth.getUser();
  if (!user) return redirect('/login');

  const { data: profile, }: { data: Profile | null } = await supabaseAdmin
    .from('profile')
    .select()
    .match({ id: user.id, })
    .maybeSingle();

  if (!profile) return redirect('/login');

  const profileSportsResponse = await getProfileSports();

  if (profileSportsResponse.status === 400) return null;

  const sport = profileSportsResponse.data[0];

  const sportHasRankResponse = await sportHasRank({ sportId: sport.id, });

  const hasRank = sportHasRankResponse.data;

  if (!hasRank || user.user_metadata.role === 'admin') return (
    <Details domain={domain} profile={profile} />
  );

  return (
    <Tabs defaultValue='progress' className="container mx-auto p-4">
      <TabsList>
        <TabsTrigger value="progress">Esporte</TabsTrigger>
        <TabsTrigger value='profile'>Info</TabsTrigger>
      </TabsList>
      <TabsContent value='progress' className='flex flex-col gap-10'>
        <Progress sport={sport} />
      </TabsContent>
      <TabsContent value='profile'>
        <Details domain={domain} profile={profile} />
      </TabsContent>
    </Tabs>
  );
};

export default UserProfile;