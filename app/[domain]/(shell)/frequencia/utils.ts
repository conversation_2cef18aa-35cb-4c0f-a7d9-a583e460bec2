export function getWeekDates(startDate: Date): Date[] {
  const week = [];
  for (let i = 0; i < 5; i++) {
    const nextDay = new Date(startDate);
    nextDay.setUTCHours(nextDay.getUTCHours() - 3);
    nextDay.setDate(startDate.getDate() + i);
    week.push(nextDay);
  }
  return week;
}

export function formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}

export function formatDateForDisplay(date: Date, format: 'short' | 'long'): string {
  if (format === 'short') {
    return date.toLocaleDateString('pt-BR', { weekday: 'short', month: 'numeric', day: 'numeric', });
  }
  return date.toLocaleDateString('pt-BR', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric', });
}

export function getWeekRange(startDate: Date): string {
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 4);

  const startMonth = startDate.toLocaleString('default', { month: 'short', });
  const endMonth = endDate.toLocaleString('default', { month: 'short', });

  const startDay = startDate.getDate();
  const endDay = endDate.getDate();
  const year = startDate.getFullYear();

  if (startMonth === endMonth) {
    return `${startMonth} ${startDay}-${endDay}, ${year}`;
  } else {
    return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${year}`;
  }
}

