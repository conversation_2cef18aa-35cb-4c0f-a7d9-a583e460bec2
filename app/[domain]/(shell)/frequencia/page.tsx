'use client';

import { useState, useEffect, } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, useToast, } from '@/components/index';
import { Profile, Schedule, Service, } from '@/utils/constants/types';
import { getAllSchoolServices, } from '@/app/actions';
import { Attendance, } from '@/utils/constants/types';
import AttendanceDashboard from './components/monthly-attendance';
import TopStudentsChart from './components/most-attendance';
import { RecentAttendances, } from './components/recent-attendances';

export type TodaysAttendanceResponse = Attendance & { schedule: Schedule, profile: Profile }

const AttendanceRanking = () => {
  const [services, setServices,] = useState<Service[] | []>([]);
  const [selectedServiceId, setSelectedServiceId,] = useState<string | undefined>(undefined);
  const { toast, } = useToast();

  useEffect(() => {
    (async () => {
      const response = await getAllSchoolServices();
      if (response.status === 200) {
        setServices(response.data);
        setSelectedServiceId(response.data[0].id);
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })();
  }, []);

  return (
    <section className='w-full h-full space-y-4'>
      <Select onValueChange={setSelectedServiceId} value={selectedServiceId}>
        <SelectTrigger className='font-medium'>
          <SelectValue placeholder='Serviço' />
        </SelectTrigger>
        <SelectContent>
          {services.map(service => (
            <SelectItem key={service.id} value={service.id} className='px-4'>
              {service.title}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {selectedServiceId && <RecentAttendances serviceId={selectedServiceId} />}
      {selectedServiceId && <AttendanceDashboard serviceId={selectedServiceId} /> }
      {selectedServiceId && <TopStudentsChart serviceId={selectedServiceId} />}
    </section>
  );
};

export default AttendanceRanking;