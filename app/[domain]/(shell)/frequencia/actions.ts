'use server';

import { userInfo, } from '@/supabase/verifications/user-info';
import { UNAUTHORIZED, } from '@/utils/constants';
import { createClientAdmin, } from '@/utils/supabase/server';
import { format, getYear, } from 'date-fns';
import { Profile, Schedule, Service, } from '@/utils/constants/types';

export const getAttendanceCount = async ({ serviceId, }: { serviceId: string }) => {
  const year = getYear(new Date());
  console.log('🚀 ~ getAttendanceCount ~ year:', year);
  const supabaseAdmin = createClientAdmin();
  const { data: attendances, } = await supabaseAdmin
    .from('attendance_count')
    .select()
    .match({ year, serviceId, });

  if (!attendances) return {
    message: 'Sem presenças confirmadas.',
    status: 400,
  };

  return { data: attendances, status: 200, };
};

export const confirmAttendance = async ({ date, profileId, serviceId, scheduleId, }: { date: string, profileId: string, serviceId: string, scheduleId: string, }): Promise<{ message: string, status: 400 | 401 } | { status: 200 } > => {
  const user = await userInfo();

  if (!user || user.user_metadata.role !== 'admin') return {
    message: UNAUTHORIZED, status: 401,
  };

  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
    .from('attendance')
    .update({ confirmed: true, })
    .match({ serviceId, scheduleId, userId: profileId, date, });
    console.log('🚀 ~ updateTodayAttendance ~ error:', error);
  if (error) return { message: 'Erro ao buscar presenças.', status: 400, };
  return { status: 200, };
};

export const deleteAttendance = async ({ date, profileId, serviceId, scheduleId, }: { date: string, profileId: string, serviceId: string, scheduleId: string, }): Promise<{ message: string, status: 400 | 401 } | { status: 200 } > => {
  const user = await userInfo();

  if (!user || user.user_metadata.role !== 'admin') return {
    message: UNAUTHORIZED, status: 401,
  };

  const supabaseAdmin = createClientAdmin();
  const { error, } = await supabaseAdmin
    .from('attendance')
    .delete()
    .match({ serviceId, scheduleId, userId: profileId, date, });
    console.log('🚀 ~ updateTodayAttendance ~ error:', error);
  if (error) return { message: 'Erro ao buscar presenças.', status: 400, };
  return { status: 200, };
};

type AttendanceResponse = { id: string, user: Profile, schedule: Schedule, service: Service, confirmed: boolean }
export const getRecentAttendances = async ({ date, serviceId, }: { date: Date, serviceId: string }): Promise<{ data: { date: string, attendances: AttendanceResponse[]}[], status: 200 } | { message: string, status: 400 | 401 } > => {
  const user = await userInfo();
  console.log('AttendanceResponse', format(date, 'yyyy-MM-dd'));

  if (!user || user.user_metadata.role !== 'admin') return {
    message: UNAUTHORIZED, status: 401,
  };
  const supabaseAdmin = createClientAdmin();

  const { data, } = await supabaseAdmin
    .from('attendance')
    .select('*, schedule(*), service(*), profile!attendance_userId_fkey(*)')
    .match({ date: format(date, 'yyyy-MM-dd'), serviceId, });

  if (!data) return { message: 'Erro ao buscar presenças.', status: 400, };

  return { data, status: 200, };
};