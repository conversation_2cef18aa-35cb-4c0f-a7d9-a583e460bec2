'use client';

import { useEffect, useState, } from 'react';
import { Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis, } from 'recharts';
import { Card, CardContent, CardHeader, Skeleton, } from '@/components/index';
import { getAttendanceCount, } from '../actions';
import { months, } from '@/utils/constants';

export default function AttendanceDashboard({ serviceId, }: { serviceId: string }) {
  const [attendances, setAttendances,] = useState<{ month: number, count: number }[]>([]);
  const [loading, setLoading,] = useState(true);

  useEffect(() => {
    (async () => {
      if (!serviceId) return null;
      const response = await getAttendanceCount({ serviceId, });
      if (response.status === 200) {
        setAttendances(response.data as { month: number, count: number }[]);
      }
      setLoading(false);
    })();
  }, [serviceId,]);

  if (loading) {
    return <AttendanceDashboard.Skeleton />;
  }

  const chartAttendanceData = attendances?.map((attendance) => ({
    month: months[attendance.month - 1],
    attendance: attendance.count || 0,
  }));

  return (
    <Card className="space-y-4">
      <CardHeader><span className='text-muted-foreground'>Presença mês a mês</span></CardHeader>
      <CardContent className="h-[200px]">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartAttendanceData}>
            <XAxis dataKey="month" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
            <YAxis
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => `${value}`}
            />
            <Tooltip
              content={({ active, payload, label, }) => {
                if (active && payload && payload.length) {
                  return (
                    <div className="rounded-lg border bg-background p-2 shadow-sm">
                      <div className="flex flex-col gap-2">
                        <div className="font-medium">{label}</div>
                        <div className="flex flex-col">
                          <span className="text-sm font-medium">{payload[0].value} alunos</span>
                        </div>
                      </div>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Line type="monotone" dataKey="attendance" stroke={'hsl(var(--primary))'} strokeWidth={2} dot={false} />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

AttendanceDashboard.Skeleton = function AttendanceTrendsSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton className="h-10 w-[200px]" />
        <div className="text-right">
          <Skeleton className="h-8 w-[60px]" />
          <Skeleton className="mt-1 h-4 w-[100px]" />
        </div>
      </div>
      <div className="h-[300px]">
        <Skeleton className="h-full w-full" />
      </div>
    </div>
  );
};

