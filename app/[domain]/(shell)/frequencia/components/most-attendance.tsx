'use client';

import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Axi<PERSON>, } from 'recharts';
import { Card, CardContent, CardHeader, Skeleton, } from '@/components/index';
import { useEffect, useState, } from 'react';
import { getAttendanceRank, } from '../../presenca/actions';
import { getMonth, } from 'date-fns';

export default function TopStudentsChart({ serviceId, }: { serviceId: string }) {
  const [students, setStudents,] = useState<{ name: string, profileImage: string, total: number }[]>([]);

  useEffect(() => {
    (async () => {
      if (!serviceId) return null;
      const today = new Date();
      const month = getMonth(today) +  1;
      const response = await getAttendanceRank({ month, serviceId, limit: 5, });
      if (response.status === 200) {
        setStudents(response.data);
      }
    })();
  }, [serviceId,]);

  const data = students.map(student => ({ name: student.name, image: student.profileImage, total: student.total, }));

  return (
    <Card>
      <CardHeader>
        <span className='text-muted-foreground'>Rank presença do mês</span>
      </CardHeader>
      <CardContent className='px-0 w-full'>
        <div className='h-[300px] w-full'>
          <ResponsiveContainer width='100%' height='100%'>
            <BarChart data={data} margin={{ right: 30, left: 0, }} >
              <XAxis
                dataKey='name'
                stroke='#888888'
                fontSize={12}
                tickLine={false}
                axisLine={false}
                interval={0}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis
                stroke='#888888'
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => `${value}`}
                />
              <Bar dataKey='total' fill='currentColor' radius={[4, 4, 0, 0,]} className='fill-primary' />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}

TopStudentsChart.Skeleton = function TopStudentsChartSkeleton() {
  return (
    <div className='space-y-8 py-6'>
      {Array.from({ length: 5, }).map((_, i) => (
        <div key={i} className='flex items-center gap-3 px-6'>
          <Skeleton className='h-10 w-10 rounded-full' />
          <Skeleton className='h-4 w-[100px]' />
        </div>
      ))}
    </div>
  );
};

