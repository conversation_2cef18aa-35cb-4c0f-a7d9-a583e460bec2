'use client';

import { addDays, format, subDays, } from 'date-fns';
import { CalendarIcon, Check, ChevronLeft, ChevronRight, X, } from 'lucide-react';
import {
  Button,
  Calendar,
  Card,
  CardContent,
  CardHeader,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  useToast,
} from '@/components/index';
import { useEffect, useState, } from 'react';
import { confirmAttendance, deleteAttendance, getRecentAttendances, } from '../actions';
import { Profile, Schedule, Service, } from '@/utils/constants/types';
import { ptBR, } from 'date-fns/locale';
import { cn, getToday, } from '@/lib/utils';

type Attendance = { id: string, date: string, profile: Profile, schedule: Schedule, service: Service, confirmed: boolean }

export function RecentAttendances({ serviceId, }: { serviceId: string }) {
  const [attendances, setAttendances,] = useState<Attendance[]>([]);
  const [loading, setLoading,] = useState(true);
  const [selectedAttendance, setSelectedAttendance,] = useState<{ profileId: string, name: string, date: string, confirmed: boolean, scheduleId: string } | null>(null);
  const [revalidate, setRevalidate,] = useState(false);
  const today = getToday();
  const [selectedDate, setSelectedDate,] = useState<Date>(today);
  const { toast, } = useToast();

  const updateStudentAttendance = async ({ confirmed, }: { confirmed: boolean }) => {
    if (!selectedAttendance) return;
    const params = {
      date: selectedAttendance.date,
      profileId: selectedAttendance.profileId,
      serviceId,
      scheduleId: selectedAttendance.scheduleId,
    };
    const updateAttendanceResponse = confirmed ? await confirmAttendance(params) : await deleteAttendance(params);
    if (updateAttendanceResponse.status === 200) {
      setRevalidate(prev => !prev);
      toast({
        title: 'Presença atualizada.',
      });
    } else {
      toast({
        title: updateAttendanceResponse.message,
      });
    }
    setSelectedAttendance(null);
  };

  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
  };

  const navigateDay = (direction: 'prev' | 'next') => {
    const newDate = direction === 'prev' ? subDays(selectedDate, 1) : addDays(selectedDate, 1);
    setSelectedDate(newDate);
  };

  useEffect(() => {
    (async () => {
      if (!serviceId) return;
      setLoading(true);
      const response = await getRecentAttendances({ date: selectedDate, serviceId, });
      if (response.status === 200) {
        setAttendances(response.data);
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
      setLoading(false);
    })();
  }, [selectedDate, serviceId, revalidate,]);


  if (loading) {
    return <RecentAttendances.Skeleton />;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => navigateDay('prev')}>
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={'outline'}
                className={cn(
                  'w-[200px] justify-start text-left font-normal',
                  !selectedDate && 'text-muted-foreground',
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {selectedDate ? format(selectedDate, 'd MMMM yyyy', { locale: ptBR, }) : <span>Selecionar data</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={(date) => date && handleDateChange(date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>

          <Button variant="outline" size="icon" onClick={() => navigateDay('next')}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className='space-y-8'>
        {!attendances.length && (
          <span>Nenhuma presença hoje.</span>
        )}
        {!!attendances.length && (
          <div className='space-y-4'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Aluno</TableHead>
                  <TableHead>Horário</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className='text-right'>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {attendances.map((attendance) => (
                  <TableRow key={attendance.id}>
                    <TableCell className='px-0'>{attendance.profile.name}</TableCell>
                    <TableCell className='px-0 text-center sm:text-left sm:pl-5'>{attendance.schedule.hour}</TableCell>
                    <TableCell className='px-0'>
                      <span
                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                          attendance.confirmed ? 'bg-green-50 text-green-700' : 'bg-yellow-50 text-yellow-700'
                        }`}
                      >
                        {attendance.confirmed ? 'Presente' : 'Pendente'}
                      </span>
                    </TableCell>
                    <TableCell className='px-0 text-center sm:text-right'>
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() =>
                          setSelectedAttendance({
                            profileId: attendance.profile.id,
                            name: attendance.profile.name,
                            date: attendance.date,
                            confirmed: attendance.confirmed,
                            scheduleId: attendance.schedule.id,
                          })
                        }
                      >
                        Atualizar
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      <Dialog open={!!selectedAttendance} onOpenChange={() => setSelectedAttendance(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Atualizar Presença</DialogTitle>
            <DialogDescription>
              Dia {selectedAttendance?.date && format(selectedAttendance.date, 'MMMM d, yyyy', { locale: ptBR, })}
            </DialogDescription>
          </DialogHeader>
          <div className='flex justify-end space-x-2 pt-4'>
            <Button variant='destructive' onClick={() => updateStudentAttendance({ confirmed: false, })}>
              <X className='mr-2 h-4 w-4' />
              Dar falta
            </Button>
            <Button onClick={() => updateStudentAttendance({ confirmed: true, })}>
              <Check className='mr-2 h-4 w-4' />
              Dar presença
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

RecentAttendances.Skeleton = function ServiceAttendanceSkeleton() {
  return (
    <div className='space-y-8'>
        <div className='space-y-4'>
          <Skeleton className='h-6 w-[200px]' />
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Aluno</TableHead>
                <TableHead>Horário</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className='text-right'>Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 3, }).map((_, j) => (
                <TableRow key={j}>
                  <TableCell>
                    <Skeleton className='h-4 w-[150px]' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-[150px]' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-[80px]' />
                  </TableCell>
                  <TableCell className='text-right'>
                    <Skeleton className='ml-auto h-8 w-[60px]' />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
    </div>
  );
};

