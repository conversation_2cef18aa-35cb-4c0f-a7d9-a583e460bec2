'use server';

import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { P, H2, H3, } from '@/components/ui/typography';
import { Separator, } from '@/components/ui/shard/separator';
import CourseContent from '@/components/ui/course-content';
import { Button, } from '@/components/ui/shard/button';
import { Card, CardHeader, CardContent, CardTitle, CardDescription, CardFooter, } from '@/components/ui/shard/card';
import Link from 'next/link';
import { redirect, } from 'next/navigation';
import { Lock, } from 'lucide-react';
import MuxPlayer from '@mux/mux-player-react';
import { Tables, } from '@/database.types';
import { Progress, Subdomains, } from '@/utils/constants/types';
import { NothingFound, } from '@/components/index';

function findUniqueNumber(array1: number[], array2: number[]) {
  const set1 = new Set(array1);
  const set2 = new Set(array2);

  // Find elements unique to array1
  const uniqueToSet1 = [...set1,].filter(item => !set2.has(item));

  // Find elements unique to array2
  const uniqueToSet2 = [...set2,].filter(item => !set1.has(item));

  // Combine unique elements from both arrays
  return [...uniqueToSet1, ...uniqueToSet2,][0];
}

type NextOrFirstLessonProps = {
  sequenceNumberOfCompleteLessons: { lessonSequence: number | null }[]
  lessonsFromCourse: Tables<'lesson'>[]
}

const getNextOrFirstLesson = ({ sequenceNumberOfCompleteLessons, lessonsFromCourse, }: NextOrFirstLessonProps): Tables<'lesson'> => {
  const completeLessonSequenceNumbers= sequenceNumberOfCompleteLessons.map(sequence => sequence.lessonSequence).filter(sequence => sequence != null);
  const lessonSequenceNumbers = lessonsFromCourse.map(lesson => lesson.sequence);
  const notCompleteLessonSequenceNumber = findUniqueNumber(completeLessonSequenceNumbers, lessonSequenceNumbers);
  return lessonsFromCourse.find(lesson => lesson.sequence === notCompleteLessonSequenceNumber) || lessonsFromCourse[0];
};

type Props = {
  params: {
    slug: string,
    domain: Subdomains
  },
}


export default async function Course({ params: { slug, }, }: Props) {
  const supabase = createClient();
  const supabaseAdmin = createClientAdmin();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user) redirect('/login');

  const { data: sequenceNumberOfCompleteLessons, } = await supabaseAdmin
    .from('progress')
    .select('lessonSequence')
    .match({ status: 'complete', userId: user.id, })
    .returns<Progress[]>();

  const { data: course, } = await supabaseAdmin
  .from('course')
  .select('*, lesson(*)')
  .match({ slug, })
  .order('sequence', { referencedTable: 'lesson', })
  .maybeSingle();

  if (!sequenceNumberOfCompleteLessons || !course) return (
    <NothingFound label='Nenhum conteúdo encontrado.' />
  );

  const lesson = getNextOrFirstLesson({ sequenceNumberOfCompleteLessons, lessonsFromCourse: course.lesson, });

  if (!lesson) return (
    <NothingFound label='Nenhuma aula encontrada.' />
  );

  // const { data: subscription } = await supabase.from('subscription').select().maybeSingle();
  redirect(`/cursos/${slug}/aula/${lesson.slug}`);

  return (
    <div className='flex flex-col gap-2 text-left w-full h-full'>
      <div className='lg:col-span-6'>
        <div className='flex flex-col sm:flex-row justify-between'>
          <MuxPlayer
            className='max-w-3xl'
            playbackId={course?.video?.publicPlaybackId}
            metadata={{
              video_id: course?.video?.videoId,
              video_title: course?.video?.name,
              viewer_user_id: user?.id,
            }}
          />

          <Card className='w-96'>
              <CardHeader>
                <CardTitle><Lock className='inline-block w-5 mr-5' /> Assine e tenha acesso</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>Tenha acesso a não só esse mas todos os cursos do catálogo.</CardDescription>
              </CardContent>
              <CardFooter>
                <Link href='/assinatura' className='w-full'><Button className='w-full'>Veja os Planos</Button></Link>
              </CardFooter>
            </Card>
        </div>
        <div className='w-full p-x-5 mt-10'>
          <H2>{course.name}</H2>
          <H3>O que vou aprender?</H3>
          <Separator className='mt-2' />
          <P>{course.description}</P>
        </div>
        <div className='w-full p-x-5'>
          <H3>Conteúdo</H3>
          <Separator className='mt-2' />
          <CourseContent courseId={course.id} isLink={false} />
        </div>
      </div>
    </div>
  );
}
