'use server';

import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { redirect, } from 'next/navigation';
import VideoPlayer from '@/components/ui/video-player';
import Lessons from '../components/lessons';
import { Subdomains, } from '@/utils/constants/types';
import { NothingFound, } from '@/components/index';

type Props = { params: { domain: Subdomains, slug: string, id: string } }

export default async function Lesson({ params: { slug, id, }, }: Props) {
  const supabase = createClient();
  const supabaseAdmin = createClientAdmin();
  const { data: { user, }, } = await supabase.auth.getUser();
  const { data: course, } = await supabaseAdmin.from('course').select('id, slug, name, module(*, lesson(*))').match({ slug, }).single();
  const { data: lesson, } = await supabaseAdmin.from('lesson').select('*, video(*)').match({ slug: id, courseId: course?.id, }).maybeSingle();

  if (!user) redirect('/login');

  if (!course || !lesson || !lesson.video) {
    return (
      <NothingFound label='Nada encontrado.' />
    );
  }

  return (
    <div>
      <VideoPlayer
        courseId={course.id}
        courseSlug={course.slug}
        lessonId={lesson.id}
        lessonSequence={lesson.sequence}
        viewerUserId={user.id}
        playbackId={lesson.video.publicPlaybackId}
        videoId={lesson.video.id}
        videoTitle={lesson.video.name}
      />
      <Lessons courseName={course.name} courseId={course.id} lessonSlug={lesson.slug} modules={course.module} className='block' />
    </div>
  );
}
