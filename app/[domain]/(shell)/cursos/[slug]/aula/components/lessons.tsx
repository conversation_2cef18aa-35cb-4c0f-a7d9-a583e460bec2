'use server';

import { createClientAdmin, } from '@/utils/supabase/server';
import Link from 'next/link';
import { SquarePlay, } from 'lucide-react';
import { cn, } from '@/lib/utils';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/shard/accordion';
import { H2, } from '@/components/ui/typography';
import { Tables, } from '@/database.types';
import { Progress, } from '@/utils/constants/types';

type WasCompleteParams = {
  sequences: Progress[]
  currentSequence: number
}

const wasComplete = ({ sequences = [], currentSequence, }: WasCompleteParams): boolean => {
  return !!sequences.find(sequence => sequence.lessonSequence === currentSequence);
};

type ModuleWithLesson = Tables<'module'> & { lesson: Tables<'lesson'>[] };

type Props = {
  className?: string
  lessonSlug: string
  courseName: string
  courseId: number
  modules: ModuleWithLesson[]
}

const Lessons = async ({ courseName, courseId, lessonSlug, modules, className = '', }: Props) => {
  const supabase = createClientAdmin();
  const { data: sequences, } = await supabase.from('progress').select().match({ courseId, status: 'complete', });

  const modulesWithProgressData = modules.map(module => ({
    ...module,
    lesson: module.lesson.map(lesson => ({
      ...lesson,
      complete: !sequences || wasComplete({ sequences, currentSequence: lesson.sequence, }),
    })),
  }));

  const openModules = modules.map(module => module.id.toString());

  return (
    <div className={cn('p-4', className)}>
      <H2 className='pl-2'>{courseName}</H2>
      {modulesWithProgressData && modulesWithProgressData.length && modulesWithProgressData.map(module => (
        <Accordion key={module.id} type='multiple' defaultValue={openModules} className='w-full' >
          <AccordionItem value={module.id.toString()}>
            <AccordionTrigger className='pl-2'>{module.name}</AccordionTrigger>
            {module?.lesson.length > 0 && module.lesson.map(lesson => (
              <Link key={lesson.slug} href={lesson.slug} className={lesson.complete ? 'text-green-600': ''}>
              <AccordionContent className={cn('flex items-center gap-5 py-4 pl-2 hover:bg-zinc-200', lessonSlug === lesson.slug && 'bg-zinc-200')}>
                <SquarePlay size={20} color={lesson.complete ? 'green' : 'black'} />
                {lesson.name}
              </AccordionContent></Link>

            ))}
          </AccordionItem>
        </Accordion>
      ))}
    </div>
  );
};

export default Lessons;