'use server';

import React, { Suspense, } from 'react';
import { createClientAdmin, } from '@/utils/supabase/server';
import { CourseCard, CourseCardSkeleton, } from './components/card';
import { NothingFound, } from '@/components/index';
import { userInfo, } from '@/supabase/verifications/user-info';

export default async function Courses() {
  const user = await userInfo();
  const supabase = createClientAdmin();
  const { data: coursesPublic, } = await supabase
    .from('course')
    .select()
    .match({ schoolId: user?.user_metadata.schoolId, });

    console.log('🚀 ~ Courses ~ coursesPublic:', coursesPublic);
  const { data: coursesAdmin, } = await supabase
    .from('course')
    .select()
    .is('schoolId', null);

    console.log('🚀 ~ Courses ~ coursesAdmin:', coursesAdmin);
  const isAdmin = user?.user_metadata.role === 'admin';
  let courses = coursesPublic || [];
  if (isAdmin) courses = [...courses, ...coursesAdmin || [],];
  console.log('🚀 ~ Courses ~ courses:', courses);

  if (courses.length === 0) return <NothingFound label='Escola ainda não tem curso cadastrado.' />;

  return (
    <div className="grid p-4 gap-14 grid-cols-auto-fit-300 text-center w-full h-full">
      {courses?.map(course => {
        return (
          <Suspense key={course.id} fallback={<CourseCardSkeleton />}>
            <CourseCard key={course.id} course={course} />
          </Suspense>
        );
      })}
    </div>
  );
}
