import Image from 'next/image';
import Link from 'next/link';
import { cn, } from '@/lib/utils';
import { AspectRatio, Button, Skeleton, } from '@/components/index';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { Tables, } from '@/database.types';
import { redirect, } from 'next/navigation';

type Props = {
  course: Tables<'course'>
  className?: string
}

export async function CourseCard({
  course,
  className,
  ...props
}: Props) {
  const supabase = createClient();
  const supabaseAdmin = createClientAdmin();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user) redirect('/login');
  const { data: lessons, } = await supabaseAdmin
    .from('lesson')
    .select()
    .match({ courseId: course?.id, });

  if (!lessons) {
    return (
      <div className="flex flex-col items-center gap-1 text-center">
        <h3 className="text-2xl font-bold tracking-tight">
          Curso não disponível no momento.
        </h3>
        <Link href="/assinatura"><Button className="mt-4">Assinar</Button></Link>
      </div>
    );
  }

  return (
    <div className={cn('space-y-3', className)} {...props}>
      <div>
          <AspectRatio ratio={16 / 9} >
          {course.featureImg && (
            <Image
              src={course.featureImg}
              alt='Photo by Drew Beamer'
              fill
              className='h-full rounded-tl-sm rounded-tr-sm object-cover'
            />
          )}
        </AspectRatio>
        <div className='p-4 border rounded-bl-sm rounded-br-sm text-left'>
          <div className='flex justify-between'>
            <h3 className='font-medium leading-none'>{course.name}</h3>
            <span className='text-xs text-muted-foreground'>{`${lessons.length} aulas`}</span>
          </div>
          <p className='text-xs text-muted-foreground my-4'>{course.description}</p>
          {/* {profile?.type !== 'admin' && !subscription?.active && <Link href='/assinatura'><Button className='w-full h-7'>Assinar</Button></Link>} */}
          {<Link href={`/cursos/${course.slug}`}><Button className='w-full h-7'>Assistir</Button></Link>}
        </div>
      </div>
    </div>
  );
}

export const CourseCardSkeleton = () => (
  <div className='space-y-3'>
    <Skeleton className="h-48 w-full rounded-tl-sm rounded-tr-sm" />
    <div className="p-4 border rounded-bl-sm rounded-br-sm">
      <div className="flex justify-between">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-8" />
      </div>
      <Skeleton className="h-4 w-full my-4" />
      <Skeleton className="h-8 w-full" />
    </div>
  </div>
);
