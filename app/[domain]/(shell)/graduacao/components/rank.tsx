'use client';

import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Input,
} from '@/components/index';
import { useForm, } from 'react-hook-form';
import { Rank, } from '@/utils/constants/types';
import { saveRank, } from '../actions';
import { toast, } from '@/components/ui/shard/use-toast';
import { useState, } from 'react';

const RankProgress = ({ classes, rank, }: { classes: number, rank: Rank }) => {
  const [label, setLabel,] = useState('Salvar');

  const form = useForm<{ classes: string }>({
    defaultValues: {
      classes: classes.toString(),
    },
  });

  const submit = async ({ classes, }: { classes: string }) => {
    setLabel('Salvando');
    const response = await saveRank({ rankId: rank.id, classes, });
    if (response.status === 200) {
      toast({
        title: 'Graduação salva com sucesso.',
      });
      setLabel('Salvo');
    } else {
      setLabe<PERSON>('<PERSON>var');
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(submit)}  className="flex flex-col gap-4 w-full">
        <div key={rank.id} className='flex items-center justify-between'>
          <span>{rank.name}</span>
          <div className='flex gap-4'>
            <FormField
              control={form.control}
              name='classes'
              render={({ field, }) => (
                <FormItem>
                  <FormControl>
                    <Input {...field} placeholder='Número de aulas.'/>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button className='w-20'>{label}</Button>
          </div>
        </div>
      </form>
    </Form>
  );
};

export default RankProgress;