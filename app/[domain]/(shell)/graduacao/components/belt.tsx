import { Button, Input, } from '@/components/index';
import React, { useEffect, useState, } from 'react';
import { getRequiredClasses, saveRank, } from '../actions';
import { toast, } from '@/components/ui/shard/use-toast';

interface MartialArtsBeltProps {
  id: string,
  name: string,
  color: string
  stripes: number
}

export function MartialArtsBelt({ id, name, color, stripes, }: MartialArtsBeltProps) {
  const [requiredClasses, setRequiredClasses,] = useState(0);
  const [label, setLabel,] = useState('Salvar');
  const isBlackBelt = color === 'black';

  const saveClasses = async () => {
    setLabel('Salvando');
    const saveRankResponse = await saveRank({ rankId: id, classes: requiredClasses, });
    if (saveRankResponse.status === 200) {
      toast({
        title: 'Graduação salva com sucesso.',
      });
      setLabel('Salvo');
    } else {
      toast({
        variant: 'destructive',
        title: saveRankResponse.message,
      });
      setLabel('Salvar');
    }
  };

  useEffect(() => {
    (async() => {
      const response = await getRequiredClasses({ rankId: id, });
      if (response.status === 200) {
        setRequiredClasses(response.data);
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })();
  }, []);

  return (
    <div className="bg-white p-4 rounded-lg shadow-md border border-gray-200">
      <div className="flex justify-between items-center mb-3">
        <h2 className="text-lg font-semibold">{name}</h2>
        <div>
          <span>Aulas necessárias pra concluir a faixa:</span>
          <div className='flex gap-2'>
            <Input value={requiredClasses} onChange={(e) => setRequiredClasses(e.target.value)} />
            <Button onClick={saveClasses}>{label}</Button>
          </div>
        </div>
      </div>
      <div className='space-y-2'>
        <div
          className={`relative w-full h-10 sm:h-12 rounded-md overflow-hidden shadow-md cursor-pointer transition-transform duration-150 ${color === 'white' ? 'border border-gray-300' : ''}`}
          role='button'
          tabIndex={0}
        >
          <div className='w-full h-full bg-gray-200'>
            <div
              className={'h-full transition-all duration-1000 ease-in-out'}
              style={{ backgroundColor: color, width: '100%', }}
            ></div>
          </div>
          {stripes > 0 && (
            <div
              className={`absolute top-0 h-full flex items-stretch justify-evenly ${isBlackBelt ? 'bg-red-600' : 'bg-black'} px-0.5 sm:px-1`}
              style={{ left: '75%', width: '20%', }}
            >
              {[...Array(stripes),].map((_, index) => (
                <div key={index} className='w-1 sm:w-1.5 md:w-2 h-full bg-white' aria-hidden='true'></div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}