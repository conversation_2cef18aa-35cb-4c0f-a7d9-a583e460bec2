'use server';

import { UNAUTHORIZED, } from '@/utils/constants';
import { Rank, RankSchool, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';

export const getRanksSchool = async (): Promise<{ data: RankSchool[], status: 200 } | { message: string, status: 400 | 401 }> => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user) return { message: UNAUTHORIZED, status: 401, };

  const supabaseAdmin = createClientAdmin();
  const { data: ranksSchool, } = await supabaseAdmin
    .from('rank_school')
    .select()
    .match({ schoolId: user.user_metadata.schoolId, })
    .returns<RankSchool[] | null>();

  if (ranksSchool) return { data: ranksSchool, status: 200, };
  return { message: 'Erro ao buscar graduações.', status: 400, };
};

export const saveRank = async ({ rankId, classes, }: { rankId: string, classes: number }): Promise<{ status: 200 } | { message: string, status: 400 | 401}> => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user) return { message: UNAUTHORIZED, status: 401, };

  const supabaseAdmin = createClientAdmin();
  const { data: ranks, } = await supabaseAdmin
    .from('rank')
    .select('id')
    .match({ baseRankId: rankId, })
    .returns<Rank[] | null>();

  if (!ranks) return { message: 'Erro ao buscar graduações.', status: 400, };
  if (!ranks.length) return { message: 'Não existem graduações.', status: 400, };

  const ranksIds = ranks.map(rank => rank.id);
  console.log('🚀 ~ saveRank ~ ranksIds:', ranksIds);
  const ranksUpdatesPromises = ranksIds.map(async rank => {
  return supabaseAdmin
    .from('rank_school')
    .upsert({
      rankId: rank,
      schoolId: user.user_metadata.schoolId,
      classes: classes/(ranksIds.length || 1),
    }, { onConflict: 'rankId, schoolId', })
    .select()
    .returns<RankSchool[] | null>();
  });
  console.log('🚀 ~ saveRank ~ ranksUpdatesPromises:', ranksUpdatesPromises);

  const response = await Promise.all(ranksUpdatesPromises);
  console.log('🚀 ~ saveRank ~ response:', response);
  const successfullUpdate = response.every(r => r.status === 201);
  if (!successfullUpdate) return { message: 'Erro ao salvar graduação.', status: 400, };
  return { status: 200, };
};

export const getRequiredClasses = async ({ rankId, }: { rankId: string }) => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user) return { message: UNAUTHORIZED, status: 401, };

  const supabaseAdmin = createClientAdmin();
  const { data: ranks, } = await supabaseAdmin
    .from('rank')
    .select('id')
    .match({ baseRankId: rankId, })
    .returns<Rank[] | null>();

  if (!ranks) return { message: 'Erro ao buscar graduações.', status: 400, };
  if (!ranks.length) return { message: 'Não existem graduações.', status: 400, };

  const ranksIds = ranks.map(rank => rank.id);

  const { data: ranksSchool, } = await supabaseAdmin
    .from('rank_school')
    .select()
    .in('rankId', ranksIds, )
    .eq('schoolId', user.user_metadata.schoolId)
    .returns<RankSchool[] | null>();

  if (!ranksSchool) return {
    message: 'Erro ao buscar graduações',
    status: 400,
  };

  const classes = ranksSchool.reduce((count, rankSchool) => count + (rankSchool?.classes || 0), 0);
  return { data: classes, status: 200, };
};