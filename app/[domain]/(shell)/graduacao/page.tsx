'use client';

import React, { useEffect, useState, } from 'react';
import { MartialArtsBelt, } from './components/belt';
import { BaseRank, Sport, } from '@/utils/constants/types';
import Loading from '../loading';
import { getAllSchoolSports, getBaseRanksBySport, } from '@/app/actions';
import { toast, } from '@/components/ui/shard/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/index';

export default function Home() {
  const [ranks, setRanks,] = useState<BaseRank[]>([]);
  const [sports, setSports,] = useState<Sport[]>([]);
  const [selectedSportId, setSelectedSportId,] = useState<string | null>(null);


  useEffect(() => {
    (async () => {
      if (!selectedSportId) return;
      const response = await getBaseRanksBySport({ sportId: selectedSportId, });
      if (response.status === 200) {
        setRanks(response.data);
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })();
  }, [selectedSportId,]);

  useEffect(() => {
    (async () => {
      const response = await getAllSchoolSports();
      if (response.status === 200) {
        setSports(response.data);
        if (response.data.length === 1) {
          setSelectedSportId(response.data[0].id);
        }
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })();
  }, []);

  if (!sports) return <Loading />;

  return (
    <div className='flex flex-col gap-4 w-full h-full items-center'>
      <div className='w-full'>
        <h1 className='text-3xl font-bold text-center mb-8'>Sistema de graduação</h1>
        {sports.length > 1 && (
          <Select onValueChange={setSelectedSportId} required>
            <SelectTrigger id='service' className='w-52'>
              <SelectValue placeholder='Selecione um esporte' />
            </SelectTrigger>
            <SelectContent className='w-auto'>
              {sports && sports.map((sport) => (<SelectItem key={sport.id} value={sport.id} className='cursor-pointer'>{sport.name}</SelectItem>))}
            </SelectContent>
          </Select>
        )}
      </div>
      <div className='space-y-6 w-full'>
        {ranks.map((rank) => (
          <MartialArtsBelt key={rank.id} id={rank.id} name={rank.name} color={rank.color} stripes={rank.stripes} />
        ))}
      </div>
    </div>
  );
}
