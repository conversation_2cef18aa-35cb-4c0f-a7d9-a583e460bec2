'use server';

import { userInfo, } from '@/supabase/verifications/user-info';
import { UNAUTHORIZED, } from '@/utils/constants';
import { Profile, Schedule, Service, ServiceTeacher, TeacherSchedule, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestResponse, } from '@supabase/supabase-js';
import { addDays, getDay, parseISO, } from 'date-fns';
import { format, } from 'date-fns/format';

type TeacherScheduleCustom = TeacherSchedule & { profile: Profile, schedule: Schedule }

export const getSchedules = async ({ serviceId, }: { serviceId: string }): Promise<{ data: Schedule[], status: 200 } | { message: string, status: 400 }> => {
  const supabaseAdmin = createClientAdmin();
  const { data: schedules, } = await supabaseAdmin
    .from('schedule')
    .select()
    .match({ serviceId, active: true, })
    .returns<Schedule[] | null>();

  if (!schedules) return { message: 'Não foi possível buscar os horários da escola.', status: 400, };
  return { data: schedules, status: 200, };
};

export const getServices = async (): Promise<{ data: Service[], status: 200 } | { message: string, status: 400 }> => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user) return { message: 'Não foi possível achar o usuário.', status: 400, };

  const supabaseAdmin = createClientAdmin();
  const { data: services, } = await supabaseAdmin
    .from('service')
    .select()
    .match({ schoolId: user.user_metadata.schoolId, })
    .returns<Service[] | null>();

  if (!services) return { message: 'Não foi possível buscar os serviços da escola.', status: 400, };
  return { data: services, status: 200, };
};


type TeacherSchedules = { profileName: string, profileId: string, scheduleHour: string, scheduleId: string, day: string }[]
type Response = { data: TeacherSchedules, status: 200 } | { message: string, status: 400 }
export const getTeachersSchedule = async ({ serviceId, weekStart, }: { serviceId: string, weekStart: Date }): Promise<Response> => {
  const formatDate = format(weekStart, 'yyyy-MM-dd');
  const supabaseAdmin = createClientAdmin();
  const { data, } = await supabaseAdmin
    .rpc('get_unique_teacher_schedules', {
      service: serviceId,
      startDate: formatDate,
      endDate: format(addDays(weekStart, 7), 'yyyy-MM-dd'),
    });

  if (!data) return { message: 'Não foi possível buscar a escala dos professores.', status: 400, };
  return { data, status: 200, };
};

export const getTeacherSchedule = async ({ teacherId, serviceId, }: { teacherId: string, serviceId: string }): Promise<{ data: (TeacherSchedule & { schedule: Schedule } )[], status: 200 } | { message: string, status: 400 }> => {
  const today = new Date();
  today.setUTCHours(today.getUTCHours() - 3);
  const formatDate = format(today, 'yyyy-MM-dd');
  const supabaseAdmin = createClientAdmin();
  const { data: schedules, } = await supabaseAdmin
    .from('teacher_schedule')
    .select('*, schedule(hour)')
    .gte('day', formatDate)
    .match({
      serviceId,
      profileId: teacherId,
    })
    .order('day', { ascending: true, })
    .returns<TeacherScheduleCustom[] | null>();

  if (!schedules) return { message: 'Não foi possível buscar a escala dos professores.', status: 400, };
  return { data: schedules, status: 200, };
};

export const removeTeacherSchedule = async ({ teacherId, day, scheduleId, }: { teacherId: string, day?: string, scheduleId?: string }): Promise<{ status: 200 } | { message: string, status: 400 }> => {
  const supabaseAdmin = createClientAdmin();
  const today = new Date();
  today.setUTCHours(today.getUTCHours() - 3);
  const { data: teacherSchedules, error, } = await supabaseAdmin
    .from('teacher_schedule')
    .select()
    .gte('day', format(today, 'yyyy-MM-dd'))
    .match(scheduleId ? { scheduleId, profileId: teacherId, } : { day, profileId: teacherId,  });

    if (error) return { message: 'Não foi possível buscar a escala dos professores.', status: 400, };

    const { data: bookings, } = await supabaseAdmin
    .from('booking')
    .select()
    .in('teacherScheduleId', teacherSchedules?.map(ts => ts.id))
    .in('status', ['pending', 'approved',]);

    const teacherSchedulesWithBookings = getMatchedBookings(teacherSchedules, bookings || []);

    teacherSchedulesWithBookings.map(async ({ id, }) => {
      const { error, } = await supabaseAdmin
      .from('booking')
      .update({
        status: 'canceled',
      })
      .match({ id, })
      .select();
      console.log('🚀 ~ removeTeacherSchedule ~ error:', error);
      if (error) return { message: 'Não foi possível cancelar o agendamento.', status: 400, };
    });

  console.log('🚀 ~ removeTeacherSchedule ~ teacherSchedules:', teacherSchedules);
  const ids = teacherSchedules.map(({ id, }) => id);
  console.log('🚀 ~ removeTeacherSchedule ~ ids:', ids);
  const { error: deletionError, } = await supabaseAdmin
    .from('teacher_schedule')
    .delete()
    .in('id', ids);

    console.log('🚀 ~ removeTeacherSchedule ~ error:', error);
    if (deletionError) return { message: 'Não foi possível cancelar o agendamento.', status: 400, };

  return { status: 200, };
};

export const addTeacherSchedule = async ({ teacherId, day, scheduleId, serviceId, }: { teacherId: string, day: string, scheduleId: string, serviceId: string }): Promise<{ status: 200 } | { message: string, status: 400 }> => {
  const supabaseAdmin = createClientAdmin();

  // Get the service to check the policy value
  const { data: service, error: serviceError, } = await supabaseAdmin
    .from('service')
    .select('policy')
    .eq('id', serviceId)
    .single();

  if (serviceError) {
    return { message: 'Não foi possível buscar informações do serviço.', status: 400, };
  }

  // Determine how many times to insert based on the policy
  const repeatCount = service.policy || 1;

  // Check if the teacher schedule already exists
  const { data: existingSchedules, error: checkError, } = await supabaseAdmin
    .from('teacher_schedule')
    .select()
    .match({ profileId: teacherId, day, scheduleId, serviceId, });

  if (checkError) {
    return { message: 'Não foi possível verificar a existência da escala.', status: 400, };
  }

  // If the schedule already exists with the required count, return success
  if (existingSchedules && existingSchedules.length >= repeatCount) {
    return { status: 200, };
  }

  // Calculate how many more entries we need to create
  const existingCount = existingSchedules ? existingSchedules.length : 0;
  const entriesToCreate = repeatCount - existingCount;

  if (entriesToCreate <= 0) {
    return { status: 200, };
  }

  // Create the required number of teacher schedule entries
  const teacherSchedulesToInsert = Array(entriesToCreate).fill({
    profileId: teacherId,
    day,
    scheduleId,
    serviceId,
  });

  const { error: insertError, } = await supabaseAdmin
    .from('teacher_schedule')
    .insert(teacherSchedulesToInsert);

  if (insertError) {
    return { message: 'Não foi possível adicionar o professor à escala.', status: 400, };
  }

  return { status: 200, };
};

/**
 * Retrieves teachers associated with a specific service
 * @param serviceId - The unique identifier of the service
 * @returns A list of teacher profiles or an error message
 * @throws Returns 401 if unauthorized, 400 if no teachers found or an error occurs
 */
export const getTeachersByService = async ({
  serviceId,
  day,
  hour,
}: {
  serviceId: string,
  day?: Date,
  hour?: string
}): Promise<{ data: Profile[], status: 200 } | { message: string, status: 400 }> => {
  const supabaseAdmin = createClientAdmin();

  // If day and scheduleId are provided, use the function to get available teachers
  if (day && hour) {
    const { data, error, } = await supabaseAdmin.rpc(
      'get_off_duty_teachers',
      {
        service_id: serviceId,
        schedule_day: day,
        day_number: getDay(new Date(day)),
        schedule_hour: hour,
      }
    );

    if (error || !data) {
      return {
        message: 'Não foi possível buscar os professores disponíveis.',
        status: 400,
      };
    }

    return {
      data,
      status: 200,
    };
  } else {
    // If day and scheduleId are not provided, get all teachers for the service
    const { data, error, } = await supabaseAdmin
      .from('service_teacher')
      .select('profile:profileId(id, name)')
      .eq('serviceId', serviceId);

    if (error || !data) {
      return {
        message: 'Não foi possível buscar os professores do serviço.',
        status: 400,
      };
    }

    // Transform the data to match the expected format
    const teachers = data.map(item => item.profile);

    return {
      data: teachers,
      status: 200,
    };
  }
};

const getMatchedBookings = (teacherSchedules: TeacherSchedule[], bookings: any[]) => {
  const matchedBookings: any[] = [];

  teacherSchedules.forEach(ts => {
    const bookingMatch = bookings.find(b => b.teacherScheduleId === ts.id);
    if (bookingMatch) {
      matchedBookings.push(bookingMatch); // Push the entire booking object
    }
  });

  return matchedBookings;
};

