import { Schedule, } from '@/utils/constants/types';
import { format, getDay, isSameDay, } from 'date-fns';

export const getDaysInMonthExcludingWeekdays = (date: Date, weekdaysToExclude: number[], datesToExclude: string[]) => {
  /**
   * Generates an array of dates for all days in the given date's month, excluding specified weekdays and specific dates.
   * @param {Date} date The input Date object.
   * @param {number[]} weekdaysToExclude An array of weekdays to exclude (0 = Sunday, 1 = Monday, ..., 6 = Saturday).
   * @param {string[]} datesToExclude An array of dates to exclude in 'yyyy-MM-dd' format.
   * @returns {Date[]} An array of Date objects, or null if the input is invalid.
   */
  if (!(date instanceof Date) || isNaN(date)) {
    return null; // Handle invalid date input
  }

  const year = date.getFullYear();
  const month = date.getMonth();

  const nextMonth = new Date(year, month + 1, 1);
  const daysInMonth = [];
  let currentDay = new Date(year, month, 1);

  while (currentDay < nextMonth) {
    const formattedCurrentDay = format(currentDay, 'yyyy-MM-dd');

    if (!weekdaysToExclude.includes(getDay(currentDay)) && !datesToExclude.includes(formattedCurrentDay)) {
      daysInMonth.push(new Date(currentDay)); // Create a copy
    }
    currentDay.setDate(currentDay.getDate() + 1);
  }

  return daysInMonth;
};

export const removeDuplicateDates = (dates: Date[]): (Date[] | null) => {
  /**
   * Removes duplicate Date objects from an array using isSameDay from date-fns.
   * @param {Date[]} dates The input array of Date objects.
   * @returns {Date[]} A new array with duplicate dates removed.
   */
  if (!Array.isArray(dates) || !dates.every(date => date instanceof Date)) {
    return null; // Handle invalid input
  }

  let uniqueDates = [];

  for (const date of dates) {
    if (!uniqueDates.some(uniqueDate => isSameDay(date, uniqueDate))) {
      uniqueDates.push(new Date(date)); // Create a copy
    }
  }

  return uniqueDates;
};

export const removeDuplicates = (data: ({
  createdAt: string;
  day: string;
  id: string;
  profileId: string;
  scheduleId: string;
  serviceId: string;
} & {
  schedule: Schedule;
})[]) => {
  const uniqueObjects = [];
  const seenDays = new Set();

  for (const obj of data) {
      if (!seenDays.has(obj.day)) {
          uniqueObjects.push(obj);
          seenDays.add(obj.day);
      }
  }

  return uniqueObjects;
};

export const findRemovedItem = (arr1: Date[], arr2: Date[]) => {
  if (arr2.length >= arr1.length) return;
  return arr1.find(item => !arr2.includes(item));
};