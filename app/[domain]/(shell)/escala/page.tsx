'use client';

import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/index';
import { Schedule, Service, } from '@/utils/constants/types';
import { addDays, format, isSameDay, parseISO, startOfWeek, } from 'date-fns';
import { ptBR, } from 'date-fns/locale';
import React, { useCallback, useEffect, useMemo, useState, } from 'react';
import { toast, } from '@/components/ui/shard/use-toast';
import { ChevronLeft, ChevronRight, X, } from 'lucide-react';
import { getSchedules, getServices, getTeachersSchedule, removeTeacherSchedule, } from './actions';
import RemoveScheduleAlert from './components/remove-schedule-alert';
import { getToday, } from '@/lib/utils';
import AvailableTeachersList from './components/off-duty-teachers';

export const getFormattedName = ({ name, }: { name: string }) => {
  // Split the name into words
  // Bring the first word and only the first letter of the rest of the words
  const words = name.split(' ');
  const firstWord = words[0];
  const restOfWords = words.slice(1).map(word => word.charAt(0));
  return `${firstWord} ${restOfWords.join('')}.`;
};

export type TeacherScheduleItem = {
  profileName: string;
  profileId: string;
  scheduleHour: string;
  scheduleId: string;
  day: string;
};

const ScheduleTable = () => {
  const [services, setServices,] = useState<Service[] | null>(null);
  const [serviceId, setServiceId,] = useState<string | null>();
  const [serviceLimit, setServiceLimit,] = useState<number>(1);
  const [schedules, setSchedules,] = useState<Schedule[]>([]);
  const [teachersSchedule, setTeachersSchedule,] = useState<TeacherScheduleItem[]>([]);
  const [weekStart, setWeekStart,] = useState<Date>(startOfWeek(getToday(), { weekStartsOn: 1, }));
  const [showRemoveSchedule, setShowRemoveSchedule,] = useState(false);
  const [removedSchedule, setRemovedSchedule,] = useState<{ scheduleId: string, teacherId: string } | null>(null);

  const weekDates = useMemo(() => {
    return Array.from({ length: 7, }, (_, i) => addDays(weekStart, i));
  }, [weekStart,]);

  const navigateWeek = (direction: 'prev' | 'next') => {
    setWeekStart(prevStart => addDays(prevStart, direction === 'prev' ? -7 : 7));
  };

  const resetToCurrentWeek = () => {
    setWeekStart(startOfWeek(getToday(), { weekStartsOn: 1, }));
  };

  let uniqueHours: Schedule[] | null = schedules && schedules.reduce((accumulator, schedule) => {
    if (!accumulator.some(item => item.hour === schedule.hour)) {
      accumulator.push(schedule);
    }
    return accumulator;
  }, [] as Schedule[])
  .sort((a, b) => a.hour.localeCompare(b.hour));

  const onRemove = async () => {
    if (!removedSchedule) return;
    const response = await removeTeacherSchedule(removedSchedule);
    if (response.status === 200) {
      toast({
        title: 'Horário removido com sucesso!',
      });
      // Refresh the teachers schedule
      if (serviceId) {
        const responseTeachersSchedule = await getTeachersSchedule({ serviceId, weekStart, });
        if (responseTeachersSchedule.status === 200) {
          setTeachersSchedule(responseTeachersSchedule.data as TeacherScheduleItem[]);
        }
      }
    } else {
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
    setShowRemoveSchedule(false);
  };

  const updateTeachersSchedule = useCallback((updatedSchedule: TeacherScheduleItem[]) => {
    setTeachersSchedule(updatedSchedule);
  }, []);

  useEffect(() => {
    (async () => {
      const response = await getServices();
      if (response.status === 200) {
        if (response.data.length === 0) {
          toast({
            variant: 'destructive',
            description: 'Nenhum serviço encontrado.',
          });
          return;
        } else {
          setServiceId(response.data[0].id);
          setServiceLimit(response.data[0].limit || 1);
        }
        setServices(response.data);
      } else {
        toast({
          variant: 'destructive',
          description: response.message,
        });
      }
    })();
  }, []);

  useEffect(() => {
    if (!serviceId) return;
    (async () => {
      const response = await getSchedules({ serviceId, });
      if (response.status === 200) {
        setSchedules(response.data);
      } else {
        toast({
          variant: 'destructive',
          description: response.message,
        });
      }

      const responseTeachersSchedule = await getTeachersSchedule({ serviceId, weekStart, });
      if (responseTeachersSchedule.status === 200) {
        setTeachersSchedule(responseTeachersSchedule.data as TeacherScheduleItem[]);
      } else {
        toast({
          variant: 'destructive',
          description: responseTeachersSchedule.message,
        });
      }
    })();
  }, [serviceId, weekStart,]);

  useEffect(() => {
    if (removedSchedule !== null) {
      setShowRemoveSchedule(true);
    }
  }, [removedSchedule,]);

  return (
    <div className='w-full h-screen'>
      <Card>
        <CardHeader>
          <div className='space-y-2'>
            <Label htmlFor='service'>Serviço</Label>
            <Select onValueChange={setServiceId} required value={serviceId || undefined}>
              <SelectTrigger id='service'>
                <SelectValue placeholder='Selecione um serviço' />
              </SelectTrigger>
              <SelectContent>
                {services && services.map((service) => (<SelectItem key={service.id} value={service.id} className='cursor-pointer'>{service.title}</SelectItem>))}
              </SelectContent>
            </Select>
          </div>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='icon'
              onClick={() => navigateWeek('prev')}
              aria-label='Previous week'
            >
              <ChevronLeft className='h-4 w-4' />
            </Button>
            <Button
              variant='outline'
              size='sm'
              onClick={resetToCurrentWeek}
            >
              Hoje
            </Button>
            <Button
              variant='outline'
              size='icon'
              onClick={() => navigateWeek('next')}
              aria-label='Next week'
            >
              <ChevronRight className='h-4 w-4' />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className='w-full overflow-x-auto'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Horário</TableHead>
                  {weekDates.map(date => {
                    const schedulesOnDay = teachersSchedule.filter(sch => isSameDay(parseISO(sch.day), date));
                    return (
                      <TableHead key={date.toISOString()}>
                        <div className='flex flex-col items-center pb-2'>
                          <span className='text-center'>{ format(date, 'dd/MM', { locale: ptBR, }) }</span>
                          <span className='text-center'>{ format(date, 'EEE', { locale: ptBR, }) }</span>
                          <span className='text-center'>{schedulesOnDay.length * serviceLimit} vagas por horário</span>
                        </div>
                      </TableHead>
                    );
                  })}
                </TableRow>
              </TableHeader>
              <TableBody>
                {uniqueHours && uniqueHours.map(slot => (
                  <TableRow key={slot.id}>
                    <TableCell>{slot.hour}</TableCell>
                    {weekDates.map(async (date: Date) => {
                      const schedulesOnDay = teachersSchedule.filter(sch =>
                        isSameDay(parseISO(sch.day), date) && sch.scheduleHour === slot.hour
                      );

                      if (!schedulesOnDay.length) return <TableCell key={date.toISOString()}><div>Sem aulas</div></TableCell>;

                      return (
                        <TableCell key={date.toISOString()}>
                          <div className='flex flex-col gap-3'>
                            {schedulesOnDay
                              .sort((a, b) => a.profileName.localeCompare(b.profileName))
                              .map(s => (
                                <div key={s.scheduleId} className='flex items-center justify-between'>
                                  <p>{getFormattedName({ name: s.profileName, })}</p>
                                  <Button
                                    variant='ghost'
                                    size='icon'
                                    onClick={() => setRemovedSchedule({ teacherId: s.profileId, scheduleId: s.scheduleId, })}
                                    className='rounded-xl w-6 h-6 bg-red-400 hover:bg-red-300'
                                    >
                                    <X className='h-4 w-4' />
                                  </Button>
                                </div>
                              ))
                            }
                          </div>
                          <AvailableTeachersList
                            serviceId={serviceId}
                            scheduleId={slot.id}
                            hour={slot.hour}
                            day={date}
                            weekStart={weekStart}
                            onTeachersScheduleUpdate={updateTeachersSchedule}
                          />
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      <RemoveScheduleAlert
        open={showRemoveSchedule}
        setShowRemoveScheduleAlert={setShowRemoveSchedule}
        onConfirmSchedule={onRemove}
      />
    </div>
  );
};

export default ScheduleTable;