'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/index';
import { useState, } from 'react';

const RemoveScheduleAlert = ({ open, setShowRemoveScheduleAlert, onConfirmSchedule, }) => {
  const [label, setLabel,] = useState('Remover');

  const onConfirm = async () => {
    setLabel('Removendo');
    await onConfirmSchedule();
  };

  return (
    <div className="flex justify-between">
      <AlertDialog open={open}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Deseja remover hora da escala?</AlertDialogTitle>
            <span className='py-6 leading-tight'>Os agendamentos nesse horário do professor serão cancelados caso exista.</span>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowRemoveScheduleAlert(false)}>Manter a escala</AlertDialogCancel>
            <AlertDialogAction className='bg-red-500 hover:bg-red-400' onClick={onConfirm}>
              {label}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default RemoveScheduleAlert;
