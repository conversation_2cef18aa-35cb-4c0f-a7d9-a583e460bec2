'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/index';
import { Profile, Schedule, Service, TeacherSchedule, } from '@/utils/constants/types';
import { addDays, format, isSameDay, parseISO, startOfWeek, } from 'date-fns';
import { ptBR, } from 'date-fns/locale';
import React, { useEffect, useMemo, useState, } from 'react';
import { getSchedules, getServices, getTeachersSchedule, removeTeacherSchedule, } from '../actions';
import { toast, } from '@/components/ui/shard/use-toast';
import { ChevronLeft, ChevronRight, X, } from 'lucide-react';
import RemoveScheduleAlert from './remove-schedule-alert';

const ScheduleTable = () => {
  const [services, setServices,] = useState<Service[] | null>(null);
  const [serviceId, setServiceId,] = useState<string | null>();
  const [schedules, setSchedules,] = useState<Schedule[]>([]);
  const [teachersSchedule, setTeachersSchedule,] = useState<{ profileName: string, profileId: string, scheduleHour: string, scheduleId: string, day: string }[]>([]);
  const [weekStart, setWeekStart,] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1, }));
  const [showRemoveSchedule, setShowRemoveSchedule,] = useState(false);
  const [removedSchedule, setRemovedSchedule,] = useState<{ scheduleId: string, teacherId: string } | null>(null);

  const weekDates = useMemo(() => {
    return Array.from({ length: 7, }, (_, i) => addDays(weekStart, i));
  }, [weekStart,]);

  const navigateWeek = (direction: 'prev' | 'next') => {
    setWeekStart(prevStart => addDays(prevStart, direction === 'prev' ? -1 : 1));
  };

  const resetToCurrentWeek = () => {
    setWeekStart(startOfWeek(new Date(), { weekStartsOn: 1, }));
  };

  let uniqueHours: Schedule[] | null = schedules && schedules.reduce((accumulator, schedule) => {
    if (!accumulator.some(item => item.hour === schedule.hour)) {
      accumulator.push(schedule);
    }
    return accumulator;
  }, [] as Schedule[])
  .sort((a, b) => a.hour.localeCompare(b.hour));

  const onRemove = async () => {
    if (!removedSchedule) return;
    const response = await removeTeacherSchedule(removedSchedule);
    if (response.status === 200) {
      toast({
        title: 'Horário removido com sucesso!',
      });
    } else {
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
    setShowRemoveSchedule(false);
  };

  useEffect(() => {
    (async () => {
      const response = await getServices();
      if (response.status === 200) {
        setServices(response.data);
      } else {
        toast({
          variant: 'destructive',
          description: response.message,
        });
      }
    })();
  }, []);

  useEffect(() => {
    if (!serviceId) return;
    (async () => {
      const response = await getSchedules({ serviceId, });
      if (response.status === 200) {
        setSchedules(response.data);
      } else {
        toast({
          variant: 'destructive',
          description: response.message,
        });
      }

      const responseTeachersSchedule = await getTeachersSchedule({ serviceId, weekStart, });
      if (responseTeachersSchedule.status === 200) {
        setTeachersSchedule(responseTeachersSchedule.data);
      } else {
        toast({
          variant: 'destructive',
          description: responseTeachersSchedule.message,
        });
      }
    })();
  }, [serviceId, weekStart,]);

  useEffect(() => {
    if (removedSchedule !== null) {
      setShowRemoveSchedule(true);
    }
  }, [removedSchedule,]);

  return (
    <>
      <Card>
        <CardHeader>
          <div className="space-y-2">
            <Label htmlFor="service">Serviço</Label>
            <Select onValueChange={setServiceId} required>
              <SelectTrigger id="service">
                <SelectValue placeholder="Selecione um serviço" />
              </SelectTrigger>
              <SelectContent>
                {services && services.map((service) => (<SelectItem key={service.id} value={service.id} className="cursor-pointer">{service.title}</SelectItem>))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => navigateWeek('prev')}
              aria-label="Previous day"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={resetToCurrentWeek}
            >
              Hoje
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => navigateWeek('next')}
              aria-label="Next day"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Time</TableHead>
                  {weekDates.map(date => (
                    <TableHead key={date.toISOString()}>
                      {format(date,
                        'EEE dd/MM', { locale: ptBR, })}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {uniqueHours && uniqueHours.map(slot => (
                  <TableRow key={slot.id}>
                    <TableCell>{slot.hour}</TableCell>
                    {weekDates.map((date: Date) => {
                      const schedulesOnDay = teachersSchedule.filter(sch => isSameDay(addDays(sch.day, 1), date) && sch.scheduleHour === slot.hour);
                      if (!schedulesOnDay.length) return <TableCell key={date.toISOString()}><div>-</div></TableCell>;
                      return (
                        <TableCell key={date.toISOString()}>
                          {schedulesOnDay.map(s => (
                            <div key={s.scheduleId} className='flex items-center'>
                              <p>{s.profileName}</p>
                              <Button variant="ghost" size="icon" onClick={() => setRemovedSchedule({ teacherId: s.profileId, scheduleId: s.scheduleId, })}>
                                <X className="h-4 w-4" color='red'/>
                              </Button>
                            </div>
                          ))}
                          </TableCell>
                      );
                    })}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      <RemoveScheduleAlert
        open={showRemoveSchedule}
        setShowRemoveScheduleAlert={setShowRemoveSchedule}
        onConfirmSchedule={onRemove}
      />
    </>
  );
};

export default ScheduleTable;