import { Button, } from '@/components/index';
import { format, } from 'date-fns';
import { Plus, } from 'lucide-react';
import React, { useEffect, useState, } from 'react';
import { toast, } from '@/components/ui/shard/use-toast';
import { addTeacherSchedule, getTeachersByService, getTeachersSchedule, } from '../actions';
import { getFormattedName, TeacherScheduleItem, } from '../page';

interface Teacher {
  id: string;
  name: string;
}

interface AvailableTeachersListProps {
  serviceId: string;
  scheduleId: string;
  hour: string;
  day: Date;
  weekStart: Date;
  onTeachersScheduleUpdate: (teachersSchedule: TeacherScheduleItem[]) => void;
}
const AvailableTeachersList: React.FC<AvailableTeachersListProps> = ({
  serviceId,
  scheduleId,
  hour,
  day,
  weekStart,
  onTeachersScheduleUpdate,
}) => {
  const [teachersOffDuty, setTeachersOffDuty,] = useState<Teacher[]>([]);
  const [loading, setLoading,] = useState(true);
  const [addingTeacher, setAddingTeacher,] = useState<string | null>(null);

  // Quick add teacher function
  const quickAddTeacher = async ({ teacherId, }: { teacherId: string }) => {
    if (!serviceId) return;

    setAddingTeacher(teacherId);

    try {
      const response = await addTeacherSchedule({
        teacherId,
        day: format(day, 'yyyy-MM-dd'),
        scheduleId,
        serviceId,
      });

      if (response.status === 200) {
        toast({
          title: 'Professor adicionado com sucesso!',
        });

        // Refresh the teachersOffDuty schedule
        const responseTeachersSchedule = await getTeachersSchedule({
          serviceId,
          weekStart,
        });

        if (responseTeachersSchedule.status === 200) {
          // Update parent component with new schedule data
          onTeachersScheduleUpdate(responseTeachersSchedule.data as TeacherScheduleItem[]);
        }
        const teachersOffDutyResponse = await getTeachersByService({
          serviceId,
          day,
          hour,
        });

        if (teachersOffDutyResponse.status === 200) {
          setTeachersOffDuty(teachersOffDutyResponse.data as Teacher[]);
        }
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    } catch (error) {
      console.error('Error adding teacher:', error);
      toast({
        variant: 'destructive',
        title: 'Erro ao adicionar professor',
      });
    } finally {
      setAddingTeacher(null);
    }
  };

  useEffect(() => {
    const fetchTeachers = async () => {
      setLoading(true);
      try {
        const teachersOffDutyResponse = await getTeachersByService({
          serviceId,
          day,
          hour,
        });

        if (teachersOffDutyResponse.status === 200) {
          setTeachersOffDuty(teachersOffDutyResponse.data as Teacher[]);
        }
      } catch (error) {
        console.error('Error fetching available teachersOffDuty:', error);
        toast({
          variant: 'destructive',
          description: 'Erro ao buscar professores disponíveis',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchTeachers();
  }, [serviceId, day, hour,]);

  if (loading) {
    return (
      <div className='flex flex-col gap-1 mt-2'>
        <div className='text-sm text-gray-500'>
          Carregando professores...
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-col gap-1 mt-10 mb-4'>
      {teachersOffDuty.map(teacher => (
        <div key={teacher.id} className='flex items-center justify-between'>
          <span>{getFormattedName({ name: teacher.name, })}</span>
          <Button
            variant='ghost'
            size='icon'
            disabled={addingTeacher === teacher.id}
            onClick={() => quickAddTeacher({ teacherId: teacher.id, })}
            className='rounded-xl w-6 h-6 bg-green-400 hover:bg-green-300'
          >
            <Plus className='h-4 w-4' />
          </Button>
        </div>
      ))}
    </div>
  );
};

export default AvailableTeachersList;
