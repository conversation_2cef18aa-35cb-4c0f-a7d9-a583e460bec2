'use client';

import { useEffect, useState, } from 'react';
import { format, getDay, isAfter, isSameDay, parseISO, } from 'date-fns';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  Button,
  Calendar,
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  Checkbox,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/index';
import { Profile, Schedule, SchoolSettings, Service, TeacherSchedule, } from '@/utils/constants/types';
import { URLS, } from '@/utils/supabase/constants';
import { ptBR, } from 'date-fns/locale';
import { useToast, } from '@/components/ui/shard/use-toast';
import TeacherSelector from '@/components/ui/teacher-selector';
import { daysOfWeek, } from '@/utils/constants';
import { getSchedules, getTeachersByService, getTeacherSchedule, removeTeacherSchedule, } from '../actions';
import { getAllSchoolServices, getSchoolSettings, } from '@/app/actions';
import { getDaysInMonthExcludingWeekdays, removeDuplicateDates, removeDuplicates, } from '../utils';
import { DayMouseEventHandler, } from 'react-day-picker';
import RemoveScheduleAlert from './remove-schedule-alert';

const Register = () => {
  const [limit, setLimit,] = useState<number | null>(null);
  const [saveLabel, setSaveLabel,] = useState('Salvar');
  const [existingDates, setExistingDates,] = useState<Date[]>([]);
  const [selectedDates, setSelectedDates,] = useState<Date[]>([]);
  const [services, setServices,] = useState<Service[]>([]);
  const [schedulesByDate, setSchedulesByDate,] = useState<{ [key: string]: Schedule[] | null } | null>(null);
  const [serviceId, setServiceId,] = useState<string | null>();
  const [schedules, setSchedules,] = useState<Schedule[]>([]);
  const [schoolSettings, setSchoolSettings,] = useState<SchoolSettings | null>(null);
  const [existingSchedules, setExistingSchedules,] = useState<Schedule[]>([]);
  const [selectedSchedules, setSelectedSchedules,] = useState<Schedule[]>([]);
  const [teacherSchedules, setTeacherSchedules,] = useState<(TeacherSchedule & { schedule: Schedule } )[]>([]);
  const [teacherId, setTeacherId,] = useState<string | null>(null);
  const [teachers, setTeachers,] = useState<Profile[] | null>(null);
  const [selectedMonth, setSelectedMonth,] = useState(new Date());
  const [showCancelConfirmation, setShowCancelConfirmation,] = useState(false);
  const [showRemoveSchedule, setShowRemoveSchedule,] = useState(false);
  const [removedDay, setRemovedDay,] = useState<Date | null>(null);
  const [removedSchedule, setRemovedSchedule,] = useState<Schedule | null>(null);
  const [removeLabel, setRemoveLabel,] = useState('Remover');
  const { toast, } = useToast();

  const setSchedule = async () => {
    setSaveLabel('Salvando');
    const service = services.find(service => service.id === serviceId);
    if (!service || !limit || !selectedSchedules.length) {
      toast({
        variant: 'destructive',
        title: 'Selecione o serviço, limite, data e horários.',
      });
      setSaveLabel('Salvar');
      return;
    }

    const notOnTeacherSchedule = (date: Date) => teacherSchedules.find(sch => isSameDay(parseISO(sch.day), date)) === undefined;
    const notOnSchedule = selectedDates.filter(notOnTeacherSchedule);
    const response = await fetch(URLS.SCHEDULE_TEACHER, {
      method: 'POST',
      body: JSON.stringify({
        days: notOnSchedule.map(date => format(date, 'yyyy-MM-dd')),
        limit,
        serviceId: service.id,
        schedules: selectedSchedules,
        teacherId,
        teacherSchedules,
      }),
    });
    if (response.status === 200) {
      toast({
        title: 'Escala salva.',
      });
      setSaveLabel('Salvar');
    } else {
      const data = await response.json();
      setSaveLabel('Salvar');
      toast({
        variant: 'destructive',
        title: data.message,
      });
    }
  };

  const isClosed = (date: Date) => {
    if (!schoolSettings) return false;
    return (schoolSettings.closedWeekDays && schoolSettings.closedWeekDays.includes(getDay(date))) || (schoolSettings.closed && schoolSettings.closed.includes(format(date, 'yyyy-MM-dd')));
  };

  const isDateDisabled = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return !isAfter(date, today) || isClosed(date) ;
  };

  const checkMonth = () => {
    let days = getDaysInMonthExcludingWeekdays(selectedMonth, schoolSettings?.closedWeekDays || [], schoolSettings?.closed || []) || [];
    days = days.filter(day => !existingDates.find(date => isSameDay(day, date)));
    if (selectedDates) setSelectedDates(removeDuplicateDates([...days, ...selectedDates,]));
    else setSelectedDates(days);
  };

  const onConfirmRemoveDay = async () => {
    if (!teacherId || !removedDay) return null;
    setRemoveLabel('Removendo');
    const response = await removeTeacherSchedule({ teacherId, day: format(removedDay, 'yyyy-MM-dd'), });
    if (response.status === 200) {
      toast({
        title: 'Escala removida com sucesso.',
      });
      setExistingDates([...(existingDates || []).filter(date => !isSameDay(date,removedDay)),]);
      setRemoveLabel('Removido');
    }
    setShowCancelConfirmation(false);
  };

  const onConfirmSchedule = async () => {
    if (!teacherId || !removedSchedule) return null;
    const response = await removeTeacherSchedule({ teacherId, scheduleId: removedSchedule.id, });
    if (response.status === 200) {
      toast({
        title: 'Escala removida com sucesso.',
      });
      setExistingSchedules([...(existingSchedules || []).filter(schedule => schedule.id !== removedSchedule.id),]);
    }
    setShowRemoveSchedule(false);
  };

  const handleDayClick: DayMouseEventHandler = (day, modifiers) => {
    const existingDateIndex = existingDates.findIndex((d) => isSameDay(day, d));
    if (existingDateIndex !== -1) {
      setShowCancelConfirmation(true);
      setRemovedDay(day);
    } else {
      const newValue = [...selectedDates,];
      if (modifiers.selected) {
        const index = selectedDates.findIndex((d) => isSameDay(day, d));
        newValue.splice(index, 1);
      } else {
        newValue.push(day);
      }
      setSelectedDates(newValue);
    }
  };

  const renderDate = () => {
    if (!teacherId) return null;
    return (
      <div className='flex w-full'>
        <div>
          <Calendar
            mode="multiple"
            modifiers={{ selected: [...existingDates, ...selectedDates,], }}
            onDayClick={handleDayClick}
            initialFocus
            locale={ptBR}
            disabled={isDateDisabled}
            weekStartsOn={1}
            month={selectedMonth}
            onMonthChange={setSelectedMonth}
            showOutsideDays={false}
          />
          <Button className='w-full' onClick={checkMonth}>Marcar mês</Button>
        </div>
      </div>
    );
  };

  const checkHour = async (schedule: Schedule, checked: boolean) => {
    if (existingSchedules.find(sch => sch.id === schedule.id) && teacherId) {
      setRemovedSchedule(schedule);
      setShowRemoveSchedule(true);
    } else {
      if (checked) {
        setSelectedSchedules([...selectedSchedules, schedule,]);
      } else {
        setSelectedSchedules([...selectedSchedules.filter(sch => sch.id !== schedule.id),]);
      }
    }
  };

  const renderHours = () => {
    if (!schedulesByDate) return null;
    return (
      <div className='flex justify-between w-full h-full'>
        {
          Object.keys(schedulesByDate).map(weekday => {
            return (
              <div key={weekday}>
                <span>{weekday}</span>
                  {
                    schedulesByDate[weekday] && schedulesByDate[weekday].map(schedule => (
                      <div key={schedule.id}>
                        <Checkbox
                          id={schedule.id}
                          checked={!![...existingSchedules, ...selectedSchedules,]?.find(sch => sch.id === schedule.id)}
                          onCheckedChange={(checked: boolean) => checkHour(schedule, checked)}
                        />
                        <Label htmlFor={schedule.id} className='cursor-pointer ml-3'>{schedule.hour}</Label>
                      </div>
                    ))
                  }
              </div>
            );
          })
        }
      </div>
    );
  };

  // get school services
  useEffect(() => {
    (async () => {
      const servicesResponse = await getAllSchoolServices();
      if (servicesResponse.status === 200) {
        setServices(servicesResponse.data);
      } else {
        toast({
          variant: 'destructive',
          title: servicesResponse.message,
        });
      }
    })();
  }, []);

  // get school settings
  useEffect(() => {
    (async () => {
      const settingsResponse = await getSchoolSettings();
      if (settingsResponse.status === 200) {
        setSchoolSettings(settingsResponse.data);
      } else {
        toast({
          variant: 'destructive',
          title: settingsResponse.message,
        });
      }
    })();
  }, []);

  // get teachers by service
  useEffect(() => {
    (async () => {
      if (!serviceId) return null;
      const getTeachersResponse = await getTeachersByService({ serviceId, });
      if (getTeachersResponse.status === 200) {
        setTeachers(getTeachersResponse.data);
      } else {
        toast({
          variant: 'destructive',
          title: getTeachersResponse.message,
        });
      }
    })();
  }, [serviceId, ]);

  // get schedules by service
  useEffect(() => {
    (async () => {
      if (!serviceId) return null;
      const scheduleResponse = await getSchedules({ serviceId, });
      if (scheduleResponse.status === 200) {
        setSchedules(scheduleResponse.data);
      } else {
        toast({
          variant: 'destructive',
          title: scheduleResponse.data?.message,
        });
      }
    })();
  }, [serviceId,]);

  // filter schedules on selected date, and pass all schedule values to be marked as already selected
  useEffect(() => {
    const initialSchedules = existingDates.reduce((finalDays, date) => {
      const dayNumber = getDay(date);
      return {
        ...finalDays,
        [daysOfWeek[dayNumber]]: schedules.filter(schedule => schedule.number === dayNumber),
      };
    }, {});
    setSchedulesByDate(initialSchedules);
    setExistingSchedules(Object.keys(initialSchedules).map(key => initialSchedules[key]).flat());
  }, [existingDates,]);

  // filter schedules on selected date, and pass all schedule values to be marked as already selected
  useEffect(() => {
    const bothSchedules = [...existingDates, ...selectedDates,].reduce((finalDays, date) => {
      const dayNumber = getDay(date);
      return {
        ...finalDays,
        [daysOfWeek[dayNumber]]: schedules.filter(schedule => schedule.number === dayNumber),
      };
    }, {});
    const selectedSchedules = selectedDates.reduce((finalDays, date) => {
      const dayNumber = getDay(date);
      return {
        ...finalDays,
        [daysOfWeek[dayNumber]]: schedules.filter(schedule => schedule.number === dayNumber),
      };
    }, {});
    setSchedulesByDate(bothSchedules);
    setSelectedSchedules(Object.keys(selectedSchedules).map(key => selectedSchedules[key]).flat());
  }, [selectedDates,]);


  useEffect(() => {
    if (!teacherId || !serviceId) return;
    (async () => {
      const response = await getTeacherSchedule({ teacherId, serviceId, });
      if (response.status === 200) {
        const teacherSchedules = response.data;
        setTeacherSchedules(teacherSchedules);
        const teacherUniqueSchedule = removeDuplicates(teacherSchedules);
        if (teacherUniqueSchedule.length) setExistingDates(teacherUniqueSchedule.map(uniqueSchedule => parseISO(uniqueSchedule.day)));
      }
    })();
  }, [teacherId,]);

  return (
    <>
      <Card className="w-full mx-auto">
        <CardHeader></CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex flex-col space-y-4'>
            <Label htmlFor="service">Serviço</Label>
            <Select onValueChange={setServiceId} required>
              <SelectTrigger id="service">
                <SelectValue placeholder="Selecione um serviço" />
              </SelectTrigger>
              <SelectContent>
                {services && services.map((service) => (<SelectItem key={service.id} value={service.id} className="cursor-pointer">{service.title}</SelectItem>))}
              </SelectContent>
            </Select>
            {teachers && <TeacherSelector teachers={teachers} onSelect={(id) => typeof id === 'string' && setTeacherId(id)} />}
          </div>
          {renderDate()}
          {renderHours()}
          {[...existingSchedules, ...selectedSchedules,].length > 0 && (
            <div className='mt-4'>
              <Label htmlFor="service">Quantos alunos o professor consegue atender por aula?</Label>
              <Input onChange={e => setLimit(e.target.value)} value={limit} placeholder='Digite aqui o número de alunos que o professor consegue atender por aula.' />
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button onClick={setSchedule} className="w-full" disabled={!selectedSchedules.length}>{saveLabel}</Button>
        </CardFooter>
      </Card>
      <div className="flex justify-between">
        <AlertDialog open={showCancelConfirmation}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Deseja remover dia da escala?</AlertDialogTitle>
              <span className='py-6 leading-tight'>Os agendamentos desse dia do professor serão cancelados caso exista.</span>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setShowCancelConfirmation(false)}>Manter a escala</AlertDialogCancel>
              <AlertDialogAction className='bg-red-500 hover:bg-red-400' onClick={onConfirmRemoveDay}>
                {removeLabel}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
      <RemoveScheduleAlert open={showRemoveSchedule} setShowRemoveScheduleAlert={setShowRemoveSchedule} onConfirmSchedule={onConfirmSchedule} />
    </>
  );
};

export default Register;
