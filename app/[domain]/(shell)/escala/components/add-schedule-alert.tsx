'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/index';
import { useState, } from 'react';

interface AddScheduleAlertProps {
  open: boolean;
  setShowAddScheduleAlert: (show: boolean) => void;
  onConfirmAdd: () => Promise<void>;
  teacherName: string;
  scheduleHour: string;
  day: string;
}

const AddScheduleAlert = ({
  open,
  setShowAddScheduleAlert,
  onConfirmAdd,
  teacherName,
  scheduleHour,
  day,
}: AddScheduleAlertProps) => {
  const [label, setLabel,] = useState('Adicionar');

  const onConfirm = async () => {
    setLabel('Adicionando');
    await onConfirmAdd();
    setLabel('Adicionar');
  };

  return (
    <div className="flex justify-between">
      <AlertDialog open={open}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Deseja adicionar professor à escala?</AlertDialogTitle>
            <span className='py-6 leading-tight'>
              O professor {teacherName} será adicionado ao horário {scheduleHour} no dia {day}.
            </span>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowAddScheduleAlert(false)}>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={onConfirm}>
              {label}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AddScheduleAlert;