'use server';

import React from 'react';
import Link from 'next/link';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { CourseCard, } from '../cursos/components/card';
import { Button, } from '@/components/ui/shard/button';
import H3 from '@/components/ui/typography/h3';
import { Tables, } from '@/database.types';
import { Course, Progress, } from '@/utils/constants/types';
import { NothingFound, } from '@/components/index';
import { redirect, } from 'next/navigation';

type CustomProgress = Progress & { course: Course }

export default async function Courses() {
  const supabase = createClient();
  const supabaseAdmin = createClientAdmin();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user) redirect('/login');

  const { data: progress,} = await supabaseAdmin.from('progress').select('*, course(*)').match({ userId: user.id, }).returns<CustomProgress[]>();
  if (!progress?.length) {
    return (
      <NothingFound>
        <span>Nenhum curso encontrado.</span>
        <Link href="/cursos"><Button className="mt-4">Procurar curso</Button></Link>
      </NothingFound>
    );
  }

  const coursesInProgress = progress.reduce((acc, { course, }) => {
    if (!course) return acc;

    if (acc.length === 0) {
      acc.push(course);
      return acc;
    }
    const hasCourse = acc.filter(c => c.id = course?.id).length;
    if (!hasCourse) {
      acc.push(course);
    }
    return acc;
  }, [] as (Tables<'course'>)[]);

  return (
    <div>
      <H3 className='text-center my-4 sm:hidden'>Meus cursos</H3>
      <div className="grid grid-cols-auto-fill-300 h-full w-full">
        {coursesInProgress?.map((course) => {
          return <CourseCard key={course.id} course={course} />;
        })}
      </div>
    </div>
  );
}
