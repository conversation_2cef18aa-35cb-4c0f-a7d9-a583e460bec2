'use client';

import { Row, } from '@tanstack/react-table';
import { MoreHorizontal, } from 'lucide-react';

import { taskSchema, } from './schema';
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/index';
import { URLS, } from '@/utils/supabase/constants';
import { toast, } from '@/components/ui/shard/use-toast';
import { inviteUser, updateStudentRankWithUserId, } from './actions';

interface DataTableRowActionsProps<TData> {
  row: Row<TData>
}

export function DataTableRowActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const student = taskSchema.parse(row.original);
  const { id, status, rank, } = student;

  const changeStudentStatus = async (id: string, status: string) => {
    const response = await fetch(URLS.STUDENT_STATUS, {
      method: 'PATCH',
      body: JSON.stringify({ id, status, }),
    });

    if (response.status === 200) {
      toast({
        title: 'Status do estudante alterado com sucesso.',
      });
    } else {
      const data = await response.json();
      toast({
        variant: 'destructive',
        title: data.message,
      });
    }
  };

  const handleInvite = async () => {
    const response = await inviteUser({
      id,
    });

    if (response.status === 200) {
      toast({
        title: 'Convite enviado com sucesso.',
      });
    } else {
      toast({
        variant: 'destructive',
        title: 'Erro ao enviar convite',
        description: response.message,
      });
    }
  };

  const bumpRank = async (id: string) => {
    const response = await updateStudentRankWithUserId({ userId: id, });
    if (response.status === 200) {
      toast({
        title: 'Estudante graduado com sucesso.',
      });
    } else {
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
        >
          <MoreHorizontal />
          <span className="sr-only">Abrir menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        {status === 'active' && (
          <DropdownMenuItem
            onSelect={() => changeStudentStatus(id, 'inactive')}
            className="cursor-pointer"
          >
            Marcar como Inativo
          </DropdownMenuItem>
        )}
        {rank && (
          <DropdownMenuItem
            onSelect={() => bumpRank(id)}
            className="cursor-pointer"
          >
            Graduar aluno
          </DropdownMenuItem>
          )
        }
        {status === 'inactive' && (
          <DropdownMenuItem
            onSelect={() => changeStudentStatus(id, 'active')}
            className="cursor-pointer"
          >
            Reativar
          </DropdownMenuItem>
        )}
        {status === 'pending' && (
          <DropdownMenuItem
            onSelect={() => changeStudentStatus(id, 'active')}
            className="cursor-pointer"
          >
            Aprovar
          </DropdownMenuItem>
        )}
        {status === 'pending' && (
          <DropdownMenuItem
            onSelect={handleInvite}
            className="cursor-pointer"
          >
            Reenviar Convite
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}