'use client';

import { ColumnDef, } from '@tanstack/react-table';

import { statuses, } from './data';
import { Task, } from './schema';
import { DataTableColumnHeader, } from './data-table-column-header';
import { DataTableRowActions, } from './data-table-row-actions';
import { Badge, } from '@/components/ui/shard/badge';
import Link from 'next/link';
import { format, parseISO, } from 'date-fns';

export const columns: ColumnDef<Task>[] = [
    {
      accessorKey: 'name',
      header: ({ column, }) => (
        <DataTableColumnHeader column={column} title="Aluno" />
      ),
      cell: ({ row, }) => {
        const name = row.getValue('name') as string;
        const id = row.original.id;

        return (
          <div className="flex flex-col">
            <Link
              href={`/alunos/${id}`} className="max-w-[500px] truncate font-medium">
              {name || 'Sem nome'}
            </Link>
          </div>
        );
      },
    },
    {
      accessorKey: 'phone',
      header: ({ column, }) => (
        <DataTableColumnHeader column={column} title="Telefone" />
      ),
      cell: ({ row, }) => {
        const phone = row.getValue('phone') as string | null;
        return (
          <div className="flex space-x-2">
            <span>{phone || 'Sem telefone'}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'birthdate',
      header: ({ column, }) => (
        <DataTableColumnHeader column={column} title="Nascimento" />
      ),
      cell: ({ row, }) => {
        const birthdate = row.getValue('birthdate') as string | undefined;
        return (
          <div className="flex space-x-2">
            <span>{birthdate ? format(parseISO(birthdate), 'dd/MM/yyyy') : 'Sem data'}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'rank',
      header: ({ column, }) => (
        <DataTableColumnHeader column={column} title='Graduação' />
      ),
      cell: ({ row, }) => {
        return (
          <div className="flex w-[100px] items-center">
            <span>{row.getValue('rank')}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: ({ column, }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row, }) => {
        const status = statuses.find(
          (status) => status.value === row.getValue('status')
        );

        if (!status) {
          return null;
        }

        return (
          <div className="flex w-[100px] items-center">
            <Badge variant={status.color}>{status.label}</Badge>
          </div>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: 'actions',
      cell: ({ row, }) => <DataTableRowActions row={row} />,
    },
  ];