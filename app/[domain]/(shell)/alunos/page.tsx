import { Metadata, } from 'next';
import { z, } from 'zod';

import { columns, } from './columns';
import { DataTable, } from './data-table';
import { taskSchema, } from './schema';
import { redirect, } from 'next/navigation';
import { NothingFound, } from '@/components/index';
import { createClientAdmin, } from '@/utils/supabase/server';
import { userInfo, } from '@/supabase/verifications/user-info';
import { Profile, Rank, RankSchool, RankStudent, } from '@/utils/constants/types';
import { toPercentage, } from '@/lib/utils';
import { User, } from '@supabase/supabase-js';

export const metadata: Metadata = {
  title: 'Alunos',
  description: 'Visualização de todos os alunos da escola.',
};

const Students = async () => {
  const user: User | null = await userInfo();
  if (!user || user.user_metadata.role !== 'admin') redirect('/login');

  const supabase = createClientAdmin();

  let { data: profiles, } = await supabase
  .from('profile')
  .select()
  .match({
    schoolId: user.user_metadata.schoolId,
    type: 'student',
  })
  .order('name', { ascending: true, })
  .returns<Profile[] | null>();

  if (profiles === null) return (
    <NothingFound label="Erro ao buscar alunos." />
  );

  if (profiles.length === 0) return (
    <NothingFound label="Sem alunos pra exibir." />
  );

  let promises = profiles.map(async profile => {
    const { data: ranksStudent, } = await supabase
      .from('rank_student')
      .select('*, rank(*)')
      .match({ profileId: profile.id, })
      .order('createdAt', { ascending: false, })
      .limit(1)
      .returns<(RankStudent & { rank: Rank })[] | null>();

    if (ranksStudent && ranksStudent.length) {
      const rankStudent = ranksStudent[0];
      const { data: rankSchool, }: { data: RankSchool | null } = await supabase
        .from('rank_school')
        .select()
        .match({
          rankId: rankStudent.rank.nextRank,
          schoolId: profile.schoolId,
        })
        .maybeSingle();

      const { count, } = await supabase
        .from('attendance')
        .select('*', { count: 'exact', head: true, })
        .eq('userId', profile.id)
        .gte('createdAt', rankStudent.createdAt);

      return {
        ...profile,
        rank: toPercentage(count || 0, rankSchool?.classes || 0) || '0%',
      };
    }
    return {
      ...profile,
      rank: '0%',
    };
  });

  profiles = await Promise.all(promises);

  const students = z.array(taskSchema).parse(profiles);

  return (
    <DataTable data={students} columns={columns} />
  );
};

export default Students;
