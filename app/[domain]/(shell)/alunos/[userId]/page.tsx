'use server';

import ProfileDetails from './components/profile-details';
import Bookings from './components/bookings';
import Attendances from './components/attendances';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger, } from '@/components/index';
import SubscriptionDetails from '../../compras/components/subscription-details';
import { Pack, PackProfile, Payment, SaleRoot, } from '@/utils/constants/types';
import { createClientAdmin, } from '@/utils/supabase/server';
import PackDetails from '../../../../../components/ui/pack-details';
import { userInfo, } from '@/supabase/verifications/user-info';
import { redirect, } from 'next/navigation';
import { getSubscriptions, } from '../actions';
import { Suspense, } from 'react';
import StudenRanksSkeleton from './components/student-ranks-skeleton';
import StudentRanks from './components/student-ranks';
import Classes from './components/classes';

export default async function Students({ params: { domain, userId, }, }: { params: { domain: string, userId: string } }) {
  const user = await userInfo();
  if (!user || user.user_metadata.role !== 'admin') redirect('/login');

  const supabase = createClientAdmin();
  const subscriptionsResponse = await getSubscriptions({ profileId: user.id, });
  const subscriptions = subscriptionsResponse.status === 200 && subscriptionsResponse.data;

  const { data: packProfiles, } = await supabase
    .from('pack_profile')
    .select('*, pack(*), payment(*), sale_root(*)')
    .match({ profileId: userId, active: true, })
    .order('purchaseStatus', { ascending: false, })
    .order('createdAt', { ascending: false, })
    .returns<(PackProfile & { pack: Pack, payment: Payment, sale_root: SaleRoot })[] | null>();
    console.log('🚀 ~ Students ~ packProfiles:', packProfiles);

  return (
    <div className='container mx-auto space-y-6 p-4'>
      <ProfileDetails domain={domain} userId={userId} />
      <Suspense fallback={<StudenRanksSkeleton />}>
        <StudentRanks userId={userId} />
      </Suspense>
      <Tabs defaultValue='bookings' className='w-full'>
        <TabsList>
          {subscriptions && subscriptions.length > 0 && (
            <TabsTrigger value='subscription' className='text-xs sm:text-base'>Assinatura</TabsTrigger>
          )}
          {packProfiles && packProfiles.length > 0 && (
            <TabsTrigger value='packs' className='text-xs sm:text-base'>Pacotes</TabsTrigger>
          )}
          <TabsTrigger value='bookings' className='text-xs sm:text-base'>Agendamentos</TabsTrigger>
          <TabsTrigger value='attendances' className='text-xs sm:text-base'>Frequência</TabsTrigger>
          <TabsTrigger value='classes' className='text-xs sm:text-base'>Turmas</TabsTrigger>
        </TabsList>
        {subscriptions && subscriptions.length > 0 && (
          <TabsContent value='subscription' className='flex flex-col gap-8'>
            {subscriptions && subscriptions.map(subscription => (
              <SubscriptionDetails domain={domain} key={subscription.id} userId={userId} subscription={subscription} withLink={true} />
            ))}
          </TabsContent>
        )}
        {packProfiles && packProfiles.length > 0 && (
          <TabsContent value='packs' className='flex flex-col gap-8'>
            {packProfiles && packProfiles.map(packProfile => (
              <PackDetails
                key={packProfile.id}
                userId={userId}
                packProfile={packProfile}
                isAdmin={true}
              />
            ))}
          </TabsContent>
        )}
        <TabsContent value='bookings'>
          <Bookings userId={userId} />
        </TabsContent>
        <TabsContent value='attendances'>
          <Attendances userId={userId} />
        </TabsContent>
        <TabsContent value='classes'>
          <Classes userId={userId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}