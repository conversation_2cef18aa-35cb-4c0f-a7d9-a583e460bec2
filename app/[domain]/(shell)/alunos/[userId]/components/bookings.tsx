'use server';

import { format, } from 'date-fns';
import { parseISO, } from 'date-fns';
import { ptBR, } from 'date-fns/locale';
import { Card, CardContent, CardHeader, } from '@/components/ui/shard/card';
import { Badge, } from '@/components/ui/shard/badge';
import { createClientAdmin, } from '@/utils/supabase/server';
import { bookingStatuses, } from '@/utils/constants';
import { NothingFound, } from '@/components/index';

const Bookings = async  ({ userId, }: { userId: string }) => {
  const supabase = createClientAdmin();

  const currentYear = new Date();
  currentYear.setFullYear(new Date().getFullYear());
  currentYear.setDate(1);
  currentYear.setMonth(0);
  currentYear.setUTCHours(0, 0, 0, 0);

  const { data: bookings, } = await supabase
    .from('booking')
    .select('*, service(*), schedule(*)')
    .eq('userId', userId)
    .gte('createdAt', currentYear.toISOString().slice(0, 19).replace('T', ' '))
    .order('day', { ascending: false, });

  if (!bookings) {
    return (
      <NothingFound label='Não foi possível carregar os agendamentos do aluno.' />
    );
  }
  if (bookings.length === 0) {
    return (
      <NothingFound label='O aluno não tem agendamento' />
    );
  }


  const getReadableDate = (date, shape) => {
    const parsedDate = parseISO(date);
    const formattedDate = format(parsedDate, shape, { locale: ptBR, });
    return formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);
  };

  return (
    <Card>
      <CardHeader>
      </CardHeader>
      <CardContent>
      {
        bookings.map((booking) => {
          return (
            <div key={booking.id} className="mb-4 p-4 border rounded">
              <div className="flex justify-between items-center mb-1">
                <div className="flex items-center">
                  <span className="font-semibold capitalize">{booking.service.title}</span>
                </div>
                <Badge variant={bookingStatuses[booking.status].color}>{bookingStatuses[booking.status].label}</Badge>
              </div>
              <p className="text-sm text-muted-foreground">Data: {getReadableDate(booking.day, 'dd MMM yyyy')}</p>
              <p className="text-sm text-muted-foreground">Horário: {booking.schedule.hour}</p>
              <p className="text-sm text-muted-foreground">Código: {`${booking.code.slice(0, 3)}-${booking.code.slice(3,5)}`.toUpperCase()}</p>
            </div>
          );
        })
      }
      </CardContent>
    </Card>
  );
};

export default Bookings;