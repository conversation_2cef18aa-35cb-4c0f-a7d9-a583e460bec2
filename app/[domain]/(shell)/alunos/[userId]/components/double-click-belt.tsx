'use client';

import React, { useState, } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  Belt,
  Label,
  Textarea,
} from '@/components/index';
import { givePoints, updateStudentRank, } from '../../actions';
import { toast, } from '@/components/ui/shard/use-toast';
import { Rank, } from '@/utils/constants/types';
import { sendPushNotificationToUser, } from '@/app/actions';

type Props = {
  rank: Rank & { nextRank: Rank },
  schoolId: string,
  userId: string,
  sportId: string,
  classes: number,
  attendance: number
}

const DoubleClickBelt = ({ rank, userId, sportId, classes, attendance, }: Props) => {
  const [showAlert, setShowAlert,] = useState(false);
  const [message, setMessage,] = useState('Parabéns pela dedicação nos treinos.');
  const nextLevel = classes <= attendance;

  const pointStudent = async () => {
    const data = {
      title: '',
      body: '',
    };
    if (nextLevel) {
      const updateStudentRankResponse = await updateStudentRank({
        userId,
        sportId,
        nextRankId: rank?.nextRank.id,
      });
      if (updateStudentRankResponse.status === 200) {
        toast({
          title: 'Aluno graduado com sucesso.',
        });
        data.title = 'Parabéns, você graduou.';
        data.body = message || `Você agora é ${rank?.nextRank.name}!`;
      } else {
        toast({
          variant: 'destructive',
          title: updateStudentRankResponse.message,
        });
      }
    } else {
      const givePointsResponse = await givePoints({ userId, sportId, });
      if (givePointsResponse.status === 200) {
        toast({
          title: 'Ponto adicionado com sucesso.',
        });
        data.title = 'Parabéns, você tá evoluindo.';
        data.body = message || 'Seu mestre lhe deu um ponto de progresso.';
      } else {
        toast({
          variant: 'destructive',
          title: givePointsResponse.message,
        });
      }
    }
    await sendPushNotificationToUser(userId, data);
  };

  return (
    <div>
      <Label>Próxima</Label>
      <Belt
        rank={rank}
        onDoubleClick={() => setShowAlert(true)}
        required={classes}
        attendance={attendance}
      />
      <AlertDialog open={showAlert} onOpenChange={() => setShowAlert(false)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            {!nextLevel && (<AlertDialogTitle>Evoluir aluno</AlertDialogTitle>)}
            {nextLevel && (<AlertDialogTitle>Graduar aluno</AlertDialogTitle>)}
            <AlertDialogDescription>Mande parabéns, dicas ou elogios a esse aluno.</AlertDialogDescription>
          </AlertDialogHeader>
          <Textarea value={message} onChange={e => setMessage(e.target.value)} />
          <AlertDialogFooter className='flex flex-col gap-2'>
            <AlertDialogAction onClick={pointStudent}>
              {`Confirmar ${nextLevel ? 'graduação' : 'pontuação'}`}
            </AlertDialogAction>
            <AlertDialogCancel className='bg-red-500 hover:bg-red-400 text-white'>Cancelar</AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default DoubleClickBelt;