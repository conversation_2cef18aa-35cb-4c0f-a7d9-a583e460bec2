import { Card, CardContent, CardDescription, CardHeader, CardTitle, Skeleton, } from '@/components/index';
import React from 'react';
import { Label, } from '@/components/index';

const StudenRanksSkeleton = () => {
  return (
    <Card>
      <CardHeader className='py-4'>
        <CardTitle>Esporte</CardTitle>
        <CardDescription>Clique duas vezes na faixa pra evoluir o aluno.</CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
      <div>
        <div className="flex justify-between items-center">
          <Label>Faixa atual</Label>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-4 w-[40px]" />
            <Skeleton className="h-8 w-[60px]" />
          </div>
        </div>
        <Skeleton className="h-8 w-full" />
      </div>
      <div>
        <div className="flex justify-between items-center">
          <Label>Próxima</Label>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-4 w-[40px]" />
            <Skeleton className="h-8 w-[60px]" />
          </div>
        </div>
        <Skeleton className="h-8 w-full" />
      </div>
      </CardContent>
    </Card>
  );
};

export default StudenRanksSkeleton;