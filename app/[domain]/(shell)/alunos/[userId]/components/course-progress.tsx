'use server';

import { Card, CardContent, CardHeader, } from '@/components/ui/shard/card';
import { Badge, } from '@/components/ui/shard/badge';
import { createClientAdmin, } from '@/utils/supabase/server';
import { NothingFound, } from '@/components/index';
import { Progress, } from '@/components/ui/shard/progress';

const CourseProgress = async  ({ userId, }: { userId: string }) => {
  const supabase = createClientAdmin();
  const { data: progresses, } = await supabase
  .from('progress')
  .select('id, courseId, lessonId, status, lessonSequence, course(name)')
  .eq('userId', userId);

  if (!progresses) {
    return (
      <NothingFound label='Não foi possível carregar o progresso de cursos do aluno.' />
    );
  }
  if (progresses.length === 0) {
    return (
      <NothingFound label='O aluno não iniciou nenhum curso.' />
    );
  }

  return (
    <Card>
      <CardHeader>
      </CardHeader>
      <CardContent>
        {
          progresses.map((progress) => (
            <div key={progress.id} className="mb-4">
              <div className="flex justify-between items-center mb-2">
              <span className="font-semibold">Curso: {progress.course?.name || progress.courseId} - Aula {progress.lessonSequence}</span>
              <Badge variant={
              progress.status === 'complete' ? 'default' : progress.status === 'in_progress' ? 'secondary' : 'outline'}>
              {progress.status === 'complete' ? 'Completo' : progress.status === 'in_progress' ? 'Em progresso' : 'Não iniciado'}
            </Badge>
              </div>
              <Progress value={
                progress.status === 'complete' ? 100 :
                progress.status === 'in_progress' ? 50 :
                0
              } className="h-2" />
            </div>
          ))
        }
      </CardContent>
    </Card>
  );
};

export default CourseProgress;