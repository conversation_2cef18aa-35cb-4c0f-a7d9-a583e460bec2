'use server';

import { format, } from 'date-fns';
import { ptBR, } from 'date-fns/locale';
import { Card, CardContent, CardHeader, CardTitle, } from '@/components/ui/shard/card';
import { Badge, } from '@/components/ui/shard/badge';
import { CalendarDays, MapPin, Phone, User, } from 'lucide-react';
import { createClientAdmin, } from '@/utils/supabase/server';
import { NothingFound, } from '@/components/index';
import ProfileImage from '@/components/ui/profile-image';
import { studentStatuses, } from '@/utils/constants/index';
import { Rank, RankStudent, } from '@/utils/constants/types';
import Actions from './student-actions';

const ProfileDetails = async  ({ domain, userId, }: { domain: string, userId: string }) => {
  const supabase = createClientAdmin();
  const { data: profile, } = await supabase
    .from('profile')
    .select()
    .match({ id: userId, })
    .maybeSingle();

  const { data: ranks, } = await supabase
    .from('rank_student')
    .select('*, rank(*)')
    .match({ profileId: userId, })
    .order('createdAt', { ascending: false, })
    .limit(1)
    .returns<(RankStudent & { rank: Rank })[] | null>();

  if (!profile) {
    return <NothingFound label='Não foi possível carregar os dados do aluno.' />;
  }

  const status = studentStatuses[profile.status];

  return (
    <Card className="mb-8">
      <CardHeader className="flex flex-col sm:flex-row items-center sm:justify-start gap-4 mb-4">
        <ProfileImage
          type='face'
          domain={domain}
          folder='faces'
          email={profile.email}
          faceImage={profile.faceImage}
          profileImage={profile.profileImage}
        />
        <div className='self-start sm:self-center'>
          <CardTitle>{profile?.name}</CardTitle>
          <p className="text-sm text-muted-foreground">{profile?.email}</p>
          <Badge variant={status.color}>{status.label}</Badge>
        </div>
        <Actions profile={profile} />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4" />
            {profile?.birthdate && <span>Nascimento: {format(profile?.birthdate, 'dd MMM yyyy', { locale: ptBR, })}</span>}
          </div>
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            <span>{profile?.phone || 'Sem telefone'}</span>
          </div>
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            <span>{profile?.cpf || 'Sem cpf'}</span>
          </div>
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            <span>{`${profile?.street || ''}, ${profile?.neighborhood || ''}, ${profile?.city || ''}, ${profile?.state || ''}`}</span>
          </div>
          {ranks && ranks.length > 0 && (
            <div className="flex items-center gap-2">
              <CalendarDays className="h-4 w-4" />
              <span>Graduação: {ranks[0].rank.name}</span>
            </div>
          )}
          {profile && (
            <div className="flex items-center gap-2">
              <CalendarDays className="h-4 w-4" />
              <span>Membro desde: {format(profile?.createdAt, 'MMM yyyy', { locale: ptBR, })}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileDetails;
