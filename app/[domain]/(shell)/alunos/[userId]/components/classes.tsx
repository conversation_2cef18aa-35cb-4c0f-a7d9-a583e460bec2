'use server';

import { <PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, CardHeader, NothingFound, } from '@/components/index';
import { createClientAdmin, } from '@/utils/supabase/server';
import { daysOfWeek, } from '@/utils/constants/index';

const Classes = async  ({ userId, }: { userId: string }) => {
  const supabase = createClientAdmin();
  const { data: servicesTeacher, } = await supabase
    .from('service_student')
    .select('service(*, sport(*))')
    .match({ profileId: userId, });

  if (!servicesTeacher) {
    return <NothingFound label='Não foi possível carregar os dados das turmas.' />;
  }
  if (!servicesTeacher.length) {
    return <NothingFound label='Não foi possível carregar os dados das turmas.' />;
  }

  const services = servicesTeacher.map(({ service, }) => service);
  const servicesIds = services.map(service => service?.id);

  const { data: schedules, error, } = await supabase
    .from('schedule')
    .select('*, service(*)')
    .in('serviceId', [servicesIds,]);
    console.log('🚀 ~ Classes ~ schedules:', schedules);
    console.log('🚀 ~ Classes ~ error:', error);

  if (!schedules) {
    return <NothingFound label='Não foi possível carregar os dados das turmas.' />;
  }

  if (!schedules.length) {
    return <NothingFound label='Aluno não está em nenhuma turma.' />;
  }

  const calendars = services.reduce((acc, service) => {
    const serviceSchedules = schedules
    .filter((schedule) => schedule.serviceId === service.id);

    const dayAndHour = serviceSchedules.reduce((acc, serviceSchedule) => {
      const weekDay = (serviceSchedule.number).toString();
      if (acc[weekDay]) {
        acc[weekDay] = [...acc[weekDay], serviceSchedule.hour,].sort();
      } else {
        acc[weekDay] = [serviceSchedule.hour,];
      }
      return acc;
    }, {});

    return [...acc, { ...service, dayAndHour, },];
  }, []);
  console.log('🚀 ~ calendars ~ calendars:', calendars);

  return (
    <>
      {calendars.map(calendar => (
        <Card className='mb-8' key={calendar.id}>
          <CardHeader className='flex flex-col sm:flex-row items-center sm:justify-start gap-4 mb-4'>
            <span className='text-lg font-semibold'>{calendar.title}</span>
            <Badge>{calendar.sport.name}</Badge>
          </CardHeader>
          <CardContent>
            {Object.keys(calendar.dayAndHour).map(key => (
              <span key={key} className='flex flex-row gap-3'>
                <span>{daysOfWeek[key]}:</span>
                {calendar.dayAndHour[key].map(hour => (
                  <span key={hour}>{hour}</span>
                ))}
                <span></span>
              </span>
            ))}
          </CardContent>
        </Card>
      ))}
    </>
  );
};

export default Classes;
