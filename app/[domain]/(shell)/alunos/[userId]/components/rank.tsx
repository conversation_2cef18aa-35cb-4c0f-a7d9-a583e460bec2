import { Card, CardContent, CardDescription, CardHeader, CardTitle, } from '@/components/ui/shard/card';
import { getStudentRank, } from '../../actions'; // Assuming these are server-side actions
import { getAttendanceSinceLastRank, getPoints, getRequiredClassesForNextRank, } from '@/components/ui/belt/actions';
import { getProfile, } from '@/app/actions';
import DoubleClickBelt from './double-click-belt';

const StudentRank = async ({ userId, sportId, sportName, }: { userId: string; sportId: string; sportName: string }) => {
  const profileResponse = await getProfile(userId);

  if (profileResponse.status !== 200) return null;

  const profile = profileResponse.data;

  const studentRankResponse = await getStudentRank({ userId, sportId, });

  console.log('🚀 ~ StudentRank ~ studentRankResponse:', studentRankResponse);
  if (studentRankResponse.status !== 200) return null;

  const studentRank = studentRankResponse.data;

  if (!studentRank.rank || !studentRank.rank.nextRank) return null;

  const currentRank = studentRank.rank;
  const nextRank = currentRank.nextRank;

  const rankSchool = await getRequiredClassesForNextRank({ rankId: nextRank.id, schoolId: profile.schoolId, });

  let requiredClasses = rankSchool.status === 200 && rankSchool.data.classes;

  const attendanceResponse = await getAttendanceSinceLastRank({ profileId: userId, sportId, dateLastRank: studentRank.createdAt, });
  const attendance = ((attendanceResponse.status === 200 && attendanceResponse.data || []).length) || 0;
  const pointsResponse = await getPoints({ profileId: userId, sportId, dateLastRank: studentRank.createdAt, });
  const points = ((pointsResponse.status === 200 && pointsResponse.data || []).length) || 0;

  return (
    <Card>
      <CardHeader className='py-4'>
        <CardTitle>{sportName}</CardTitle>
        <CardDescription>Clique duas vezes na faixa pra evoluir o aluno.</CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div>
          <DoubleClickBelt
            rank={currentRank}
            schoolId={profile.schoolId}
            userId={userId}
            sportId={sportId}
            classes={requiredClasses || 0}
            attendance={attendance + points}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default StudentRank;