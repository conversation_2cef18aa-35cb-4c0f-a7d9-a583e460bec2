import { createClientAdmin, } from '@/utils/supabase/server';
import { userInfo, } from '@/supabase/verifications/user-info';
import { redirect, } from 'next/navigation';
import StudentRank from './rank';

export const revalidate = 60;

const StudentRanks = async ({ userId, }: { userId: string }) => {
  const user = await userInfo();
  if (!user || user.user_metadata.role !== 'admin') redirect('/login');

  const supabase = createClientAdmin();

  const { data: sportsProfile, } = await supabase
    .from('sport_profile')
    .select('*, sport(*)')
    .match({ profileId: userId, });
    console.log('🚀 ~ StudentRanks ~ sportsProfile:', sportsProfile);

  if (!sportsProfile) return <span>Erro ao buscar esportes.</span>;
  if (sportsProfile.length === 0) return null;

  return (
    <>
      {sportsProfile.map(sportProfile => (
        <StudentRank
          key={sportProfile.id}
          sportId={sportProfile.sportId}
          sportName={sportProfile.sport.name}
          userId={userId}
        />
      ))}
    </>
  );
};

export default StudentRanks;