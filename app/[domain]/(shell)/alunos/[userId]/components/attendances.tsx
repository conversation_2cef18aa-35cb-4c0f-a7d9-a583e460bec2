'use server';

import { format, } from 'date-fns';
import { parseISO, } from 'date-fns';
import { ptBR, } from 'date-fns/locale';
import { Card, CardContent, } from '@/components/ui/shard/card';
import { createClientAdmin, } from '@/utils/supabase/server';
import { NothingFound, } from '@/components/index';

const Students = async  ({ userId, }: { userId: string }) => {
  const supabase = createClientAdmin();

  const { data: attendances, } = await supabase
    .from('attendance')
    .select('id, date, schedule(*), service(*)')
    .eq('userId', userId)
    .order('date', { ascending: false, })
    .limit(15);

  if (!attendances) {
    return (
      <NothingFound label='Não foi possível carregar as presenças do aluno.' />
    );
  }
  if (attendances.length === 0) {
    return (
      <NothingFound label='Aluno não tem presenças.' />
    );
  }

  const getReadableDate = (date, shape) => {
    const parsedDate = parseISO(date);
    const formattedDate = format(parsedDate, shape, { locale: ptBR, });
    return formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);
  };

  return (
    <Card>
      <CardContent className='flex flex-col gap-4 mt-4'>
      {
        attendances.map((attendance) => (
          <div key={attendance.id} className='rounded border p-2'>
            <span className='text-lg font-semibold'>{attendance.service.title}</span>
            <span> - {getReadableDate(attendance.date, 'dd MMM yyyy')}</span>
            <span> - {attendance.schedule.hour}</span>
          </div>
        ))
      }
    </CardContent>
    </Card>
  );
};

export default Students;