'use server';

import React from 'react';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { CustomSubscription, Profile, } from '@/utils/constants/types';
import { NothingFound, } from '@/components/index';
import { redirect, } from 'next/navigation';
import SubscriptionDetails from '../../../../compras/components/subscription-details';
import Payments from '../../../../compras/components/payments';

const Subscription = async ({ params: { domain, userId, id, }, }: { params: { domain: string, userId: string, id: string } }) => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user || user.user_metadata.role !== 'admin') redirect('/login');

  const supabaseAdmin = createClientAdmin();

  const { data: subscription, }: { data: CustomSubscription | null } = await supabaseAdmin
  .from('subscription')
  .select('*, plan(*)')
  .match({
    id,
    profileId: userId,
  })
  .maybeSingle();

  if (!subscription || !subscription.plan) {
    return <NothingFound label='Não encontramos a assinatura.' />;
  }

  const { data: profile, }: { data: Profile | null } = await supabaseAdmin
  .from('profile')
  .select()
  .match({ id: subscription.profileId, })
  .maybeSingle();

  if (!profile) redirect('/login');

  return (
    <div className='flex flex-col w-full gap-10'>
      <SubscriptionDetails domain={domain} subscription={subscription} />
      <Payments subscriptionId={subscription.id} />
    </div>
  );
};

export default Subscription;