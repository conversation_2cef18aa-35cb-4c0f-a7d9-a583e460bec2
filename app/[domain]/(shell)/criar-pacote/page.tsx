'use client';

import {
  Button,
  Checkbox,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import { ChangeEvent, useEffect, useState, } from 'react';
import { useForm, } from 'react-hook-form';
import { z, } from 'zod';
import { zodResolver, } from '@hookform/resolvers/zod';
import { useRouter, } from 'next/navigation';
import { Pack, PackService, Service, } from '@/utils/constants/types';
import { formatToBrazilianReal, } from '@/lib/utils';
import { savePack, updatePack, } from './actions';
import { getAllSchoolServices, } from '@/app/actions';

const requiredMessage = 'Campo obrigatório.';
export const formSchema = z.object({
  expires: z.string({ required_error: requiredMessage, }),
  name: z.string({ required_error: requiredMessage, })
    .max(40, { message: 'Nome muito longo, máximo 40 letras.', }),
  price: z.string({ required_error: requiredMessage, })
    .min(3, { message: 'Preço não permitido.', })
    .max(11, { message: 'Preço não permitido.', }),
  services: z.array(z.object({
    id: z.string(),
  })),
  use: z.string({ required_error: requiredMessage, }),
  installments: z.coerce.string().optional(),
});

export type PackFormData = z.infer<typeof formSchema>;

const CreateProductForm = ({ product, }: { product: Pack & { services: PackService[] } }) => {
  const [isLoading, setIsLoading,] = useState(false);
  const [services, setServices,] = useState<Service[] | null>(null);
  const router = useRouter();

  const defaultValues = product && {
    expires: product?.expires?.toString(),
    name: product.name,
    price: formatToBrazilianReal(product.price),
    use: product.use?.toString(),
    services: product.services.map(s => ({ id: s.serviceId, })),
    installments: product?.installments?.toString(),
  };

  let form = useForm<PackFormData>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  const onSubmit = async (formData: z.infer<typeof formSchema>) => {
    setIsLoading(true);

    if (!product) {
      const response = await savePack(formData);
      if (response.status === 201) {
        toast({
          description: 'Serviço criado com sucesso.',
        });
        router.push('/pacotes');
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    } else {
      const response = await updatePack({ formData, id: product.id, });
      if (response.status === 201) {
        toast({
          description: 'Serviço atualizado com sucesso.',
        });
        router.push('/pacotes');
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    }
    setIsLoading(false);
  };

  const formatPrice = (value: string) => {
    const digits = value.replace(/\D/g, '');

    const formatter = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    const numericValue = parseInt(digits, 10) / 100;
    return formatter.format(numericValue);
  };

  const handlePriceChange = (event: ChangeEvent<HTMLInputElement>) => {
    const rawValue = event.target.value.replace(/\D/g, '');
    const formattedValue = formatPrice(rawValue);
    form.setValue('price', formattedValue, { shouldValidate: true, });
  };

  useEffect(() => {
    (async () => {
      const response = await getAllSchoolServices();
      if (response.status === 200) {
        setServices(response.data.map((service: Service) => ({ ...service, selected: product ? !!product.services.find(sv => sv.serviceId === service.id) : true, })));
        if (!product) form.setValue('services', response.data?.map((service: Service) => ({ id: service.id, })));
      } else {
        toast({
          variant: 'destructive',
          description: response.message,
        });
      }
    })();
  }, []);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8 w-full">
        <FormField
          control={form.control}
          name='name'
          render={({ field, }) => (
            <FormItem>
              <FormLabel>Nome</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Algum texto que mostre ao usuário o que ele está comprando."
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='price'
          render={({ field, }) => (
            <FormItem>
              <FormLabel>Preço</FormLabel>
              <FormControl>
                <Input
                  id="price"
                  {...field}
                  name="price"
                  onChange={handlePriceChange}
                  placeholder="R$ 0,00"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
          <div>
            <FormLabel>Serviços inclusos</FormLabel>
            <div className='flex gap-8 mt-3'>
              {services && services.map((service) => (
                <FormField
                  key={service.id}
                  control={form.control}
                  name='services'
                  render={({ field, }) => (
                    <FormItem>
                      <FormControl>
                        <div>
                          <Checkbox
                            id={service.id}
                            checked={!!field.value.find((f: { id: string }) => f?.id === service.id)}
                            onCheckedChange={(checked) => {
                              const isChecked = checked === true;
                              if (isChecked) {
                                field.onChange([...field.value, { id: service.id, },]);
                              } else {
                                field.onChange(field.value.filter((i: { id: string }) => i.id !== service.id));
                              }
                              setServices(services.map(e =>
                                e.id === service.id ? { ...e, selected: isChecked, } : e
                              ));
                            }}
                          />
                          <Label htmlFor={service.id} className='cursor-pointer ml-3'>{service.title}</Label>
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              ))}
            </div>
          </div>
        <FormField
          control={form.control}
          name='expires'
          render={({ field, }) => (
            <FormItem>
              <FormLabel>Expira em (dias)</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder='Quantidade de dias que o pacote é válido.'
                  type='number'
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='use'
          render={({ field, }) => (
            <FormItem>
              <FormLabel>Quantidade de uso</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder='Quantas vezes o usuário pode usar o serviço.'
                  type='number'
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='installments'
          render={({ field, }) => (
            <FormItem>
              <FormLabel>Parcelas</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={form.formState.defaultValues?.installments}>
                  <SelectTrigger id="installments">
                    <SelectValue placeholder="Parcelas" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 13, }, (_, i) => i).map((_: any, index: number) => {
                      if (index === 0) return;
                      return (
                        <SelectItem key={index} value={index.toString()}>
                          {index}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? (product ? 'Atualizando...' : 'Criando...') : (product ? 'Atualizar pacote' : 'Criar pacote' )}
        </Button>
      </form>
    </Form>
  );
};

export default CreateProductForm;