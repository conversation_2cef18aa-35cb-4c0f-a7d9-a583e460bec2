'use server';

import { Pack, PackService, } from '@/utils/constants/types';
import { createClientAdmin, } from '@/utils/supabase/server';
import { PackFormData, } from './page';
import { userInfo, } from '@/supabase/verifications/user-info';
import { UNAUTHORIZED, } from '@/utils/constants';
import { PostgrestMaybeSingleResponse, PostgrestResponse, } from '@supabase/supabase-js';

const parseBrazilianReal = (realValue: string) => {
  const cleanValue = realValue.replace(/[R$.,\s]/g, '');
  return cleanValue.replace(/(\d{2})$/, '.$1');
};

type SavePackReturn = {
  data: Pack,
  status: 201
} | { message: string, status: 400 }
const savePackItself = async (formData: PackFormData, schoolId: string): Promise<SavePackReturn> => {
  const { services, ...packInfo } = formData;
  const supabaseAdmin = createClientAdmin();
  const { data, error }: PostgrestMaybeSingleResponse<Pack> = await supabaseAdmin
  .from('pack')
  .insert({
    ...packInfo,
    price: parseBrazilianReal(formData.price),
    schoolId,
  })
  .select()
  .maybeSingle();

  console.log("🚀 ~ savePackItself ~ data:", data)
  console.log("🚀 ~ savePackItself ~ error:", error)
  if (data === null) return {
    message: 'Não foi possível salvar pacote.',
    status: 400,
  };

  return { data, status: 201, };
};

type SavePackServiceReturn = {
  data: PackService[],
  status: 201
} | { message: string, status: 400 }
type SavePackServiceParams = { services: { id: string }[], packId: string }
const saveServicesPack = async ({ services, packId, }: SavePackServiceParams): Promise<SavePackServiceReturn> => {
  const supabaseAdmin = createClientAdmin();
  const servicesPack = services.map((service) => {
    return {
      serviceId: service.id,
      packId,
    };
  });
  console.log("🚀 ~ servicesPack ~ servicesPack:", servicesPack)
  const { data, }: PostgrestResponse<PackService> = await supabaseAdmin
  .from('service_pack')
  .insert(servicesPack)
  .select();

  if (!data) return {
    message: 'Não foi possível ligar pacote ao serviço.',
    status: 400,
  };

  return { data, status: 201, };
};

export async function savePack(formData: PackFormData) {
  const user = await userInfo();
  if (!user || user.user_metadata.role !== 'admin') return { message: UNAUTHORIZED, status: 401, };

  const savePackResponse = await savePackItself(formData, user.user_metadata.schoolId);

  if (savePackResponse.status === 400) return savePackResponse;
  const pack = savePackResponse.data;
  console.log('🚀 ~ savePack ~ pack:', pack);

  return await saveServicesPack({
    services: formData.services,
    packId: pack.id,
  });
}

const updatePackItself = async ({ formData, id, schoolId, }: { formData: PackFormData, id: string, schoolId: string }): Promise<SavePackReturn> => {
  const { services, ...packInfo } = formData;
  const supabaseAdmin = createClientAdmin();
  const { data, error, }: PostgrestMaybeSingleResponse<Pack> = await supabaseAdmin
  .from('pack')
  .update({
    ...packInfo,
    price: parseBrazilianReal(formData.price),
    schoolId,
  })
  .match({ id, })
  .select()
  .maybeSingle();

  console.log('🚀 ~ updatePackItself ~ data:', data);
  console.log('🚀 ~ updatePackItself ~ error:', error);
  if (data === null) return {
    message: 'Não foi possível salvar pacote.',
    status: 400,
  };

  return { data, status: 201, };
};

export async function updatePack({ formData, id, }: { formData: PackFormData, id: string }) {
  const user = await userInfo();
  if (!user || user.user_metadata.role !== 'admin') return { message: UNAUTHORIZED, status: 401, };

  const savePackResponse = await updatePackItself({ formData, id, schoolId: user.user_metadata.schoolId, });

  if (savePackResponse.status === 400) return savePackResponse;
  const pack = savePackResponse.data;
  console.log('🚀 ~ savePack ~ pack:', pack);

  const supabaseAdmin = createClientAdmin();
  await supabaseAdmin
    .from('service_pack')
    .delete()
    .match({
      packId: pack.id,
    });

  return await saveServicesPack({
    services: formData.services,
    packId: pack.id,
  });
}