'use server';

import React from 'react';
import { redirect, } from 'next/navigation';
import PushNotificationManager from './provider';
import CheckIn from './components/checkin';
import { ThemeProvider, } from '@/lib/ThemeProvider';
import { getProfile, getSchoolByDomain, getSchoolSettings, } from '@/app/actions';
import { SidebarInset, SidebarProvider, } from '@/components/ui/shard/sidebar';
import { AppSidebar, } from './app-sidebar';
import { SiteHeader, } from './site-header';

export default async function Layout({ children, params: { domain, }, }: { children: React.ReactNode, params: { domain: string } }) {
  const response = await getProfile();
  if (response.status !== 200) redirect('/login');
  const user = response?.data;

  const isStudent = user.type === 'student';

  const schoolResponse = await getSchoolByDomain({ domain, });
  const schoolSettingsResponse = await getSchoolSettings();

  return (
    <ThemeProvider
      defaultTheme={schoolSettingsResponse?.data?.theme}
      enableColorScheme
      themes={['yellow', 'red', 'blue', 'green',]}
    >
      <SidebarProvider>
        <AppSidebar variant="inset" user={user} schoolName={schoolResponse?.data?.name} />
        <SidebarInset>
          <SiteHeader />
          <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                <div className="px-4 lg:px-6">
                  {children}
                  <PushNotificationManager />
                </div>
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </ThemeProvider>
  );
}
