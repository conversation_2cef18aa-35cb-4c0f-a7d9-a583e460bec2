'use client';

import {
  Command,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/index';
import { Profile, } from '@/utils/constants/types';
import { URLS, } from '@/utils/supabase/constants';
import { useEffect, useState, } from 'react';
import Loading from '../loading';
import { getLeads, } from './actions';

const SearchUser = ({ onSelect, }: { onSelect: (user: Profile) => void }) => { // eslint-disable-line no-unused-vars
  const [users, setUsers,] = useState<Profile[]>([]);
  const [loading, setLoading,] = useState(true);
  const [search, setSearch,] = useState('');

  useEffect(() => {
    (async () => {
      let newUsers = [];
      const response = await fetch(URLS.STUDENTS);
      if (response.status === 200) {
        const data = await response.json();
        newUsers = data;
      }
      const getProspectsResponse = await getLeads();
      if (getProspectsResponse.status === 200) {
        newUsers = [...newUsers, ...getProspectsResponse.data,];
      }
      setUsers(newUsers);
      setLoading(false);
    })();
  }, []);

  const onSelectUser = (name: string) => {
    const selectedUser =  users.find(user => user.name === name);
    if (selectedUser) onSelect(selectedUser);
    setSearch('');
  };

  if (loading) return <Loading />;

  if (!users || !users.length) return null;

  const filteredUsers = search === '' ? [] : users.filter(user => `${user.name}`.toLowerCase().startsWith(search.toLowerCase()));
  return (
    <Command
      className="rounded-lg border w-full h-fit mt-6"
      >
      <CommandInput placeholder="Procurar aluno" value={search}
      onValueChange={setSearch} />
      <CommandList className={`${!filteredUsers.length ? 'hidden' : 'block'}`}>
        <CommandGroup>
          {filteredUsers.map(user => (
            <CommandItem
              className='cursor-pointer'
              key={user.id}
              onSelect={() => onSelectUser(user.name)}
            >
              <span>{user.name} - {user.phone}</span>
            </CommandItem>
          ))}
        </CommandGroup>
      </CommandList>
    </Command>
  );
};

export default SearchUser;