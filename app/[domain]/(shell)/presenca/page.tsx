'use client';

import { useEffect, useState, } from 'react';
import { <PERSON>ton, Calendar, Card, CardContent, CardHeader, Input, Label, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Table, TableBody, TableCell, TableHead, TableHeader, TableRow, } from '@/components/index';
import { Upload, X, Loader2, } from 'lucide-react';
import ImageComponent from 'next/image';
import * as faceapi from 'face-api.js';
import { ptBR, } from 'date-fns/locale';
import { Face, Profile, Schedule, Service, } from '@/utils/constants/types';
import { URLS, weekDaysDictionaryExtense, } from '@/utils/supabase/constants';
import SearchUser from './search-user';
import { format, } from 'date-fns';
import { getAllSchoolServices, } from '@/app/actions';
import { toast, } from '@/components/ui/shard/use-toast';

const Attendance = () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const [users, setUsers,] = useState<Profile[]>([]);
  const [saveButton, setSaveButton,] = useState('Salvar');
  const [selectedDate, setSelectedDate,] = useState<Date | undefined>(today);
  const [selectedImage, setSelectedImage,] = useState<string | null>(null);
  const [faces, setFaces,] = useState<Face[] | null>(null);
  const [loadingImg, setLoadingImg,] = useState(false);
  const [loadingModels, setLoadingModels,] = useState(true);
  const [services, setServices,] = useState<Service[] | []>([]);
  const [schedules, setSchedules,] = useState<Schedule[] | []>([]);
  const [selectedScheduleId, setSelectedScheduleId,] = useState<Schedule | null>(null);
  const [selectedServiceId, setSelectedServiceId,] = useState<Service | null>(null);

  const removeUser = async (studentId: string) => {
    setSaveButton('Salvando');
    setUsers(prevUsers => prevUsers.filter(user => user.id !== studentId));
    await fetch(URLS.ATTENDANCE_REMOVE, {
      method: 'POST',
      body: JSON.stringify({
        date: selectedDate,
        studentId,
      }),
    });
  };

  const saveAttendance = async () => {
    setSaveButton('Salvando');
    const response = await fetch(URLS.ATTENDANCE, {
      method: 'POST',
      body: JSON.stringify({
        date: selectedDate,
        students: users,
        serviceId: selectedServiceId,
        scheduleId: selectedScheduleId,
      }),
    });
    if (response.status === 200) {
      setTimeout(() => setSaveButton('Presença salva'), 2000);
      toast({
        title: 'Presenças salvas.',
      });
    } else {
      const data = await response.json();
      setSaveButton('Salvar');
      toast({
        variant: 'destructive',
        title: data.message,
      });
    }
  };

  const loadFaces = async () => {
    const response = await fetch('/api/face');
    let data = await response.json() as Face[] | null;
    if (data) {
      data = data.reduce((facesWithFloat, face, ) => {
        const images = face.description.map(description => {
          const binaryData = atob(description);
          const uint8Array = new Uint8Array(binaryData.length);
          for (let i = 0; i < binaryData.length; i++) {
            uint8Array[i] = binaryData.charCodeAt(i);
          }
          return new Float32Array(uint8Array.buffer);
        });
        const faceWithFloatDescriptor = new faceapi.LabeledFaceDescriptors(face.email, images);
        facesWithFloat.push(faceWithFloatDescriptor);
        return facesWithFloat;
      }, []);
      setFaces(data);
    }
  };

  const loadTodaysAttendance = async () => {
    const response = await fetch(`${URLS.ATTENDANCE_TODAY}?date=${selectedDate}&scheduleId=${selectedScheduleId}&serviceId=${selectedServiceId}`);
    const data = await response.json();
    if (response.status === 200) {
      const todayUsers = data.map(d => d.profile);
      setUsers(todayUsers);
    } else {
      toast({
        variant: 'destructive',
        title: data.message,
      });
    }
  };

  useEffect(() => {
    (async () => {
      await Promise.all([
        faceapi.loadSsdMobilenetv1Model('https://justadudewhohacks.github.io/face-api.js/models/'),
        faceapi.loadFaceLandmarkModel('https://justadudewhohacks.github.io/face-api.js/models/'),
        faceapi.loadFaceRecognitionModel('https://justadudewhohacks.github.io/face-api.js/models/'),]
      );
      setLoadingModels(false);
      await loadFaces();
      const response = await getAllSchoolServices();
      if (response.status === 200) {
        setServices(response.data);
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })();
  }, []);

  useEffect(() => {
    if (selectedServiceId && selectedDate) {
      (async () => {
        const d = format(new Date(selectedDate), 'EEEE', { locale: ptBR, });
        const dayOfWeek = weekDaysDictionaryExtense[d];
        const scheduleResponse = await fetch(`${URLS.SCHEDULE}?serviceId=${selectedServiceId}&dayOfWeek=${dayOfWeek}`);
        if (scheduleResponse.status === 200) {
          console.log('🚀 ~ scheduleResponse:', scheduleResponse);
          const data = await scheduleResponse.json();
          setSchedules(data);
        }
      })();
    }
  }, [selectedDate, selectedServiceId,]);

  useEffect(() => {
    (async () => {
      if (!selectedDate || !selectedServiceId || !selectedScheduleId) return null;
      await loadTodaysAttendance();
    })();
  }, [selectedDate, selectedServiceId, selectedScheduleId,]);

  useEffect(() => {
    if (selectedImage) {
      const img = new Image(); // Create a new HTMLImageElement

      img.onload = function() {
        (( async () => {
          try {
            let fullFaceDescriptions = await faceapi.detectAllFaces('studentsPicture').withFaceLandmarks().withFaceDescriptors();
            fullFaceDescriptions = faceapi.resizeResults(fullFaceDescriptions, { width: img.width, height: img.height, });
            const faceMatcher = new faceapi.FaceMatcher(faces, 0.6);
            const results = fullFaceDescriptions.map((d) => faceMatcher.findBestMatch(d.descriptor));
            const userEmails = new Set();
            results.map(match => {
              if (match._label !== 'unknown') userEmails.add(match.label);
            });
            const arrayEmails = Array.from(userEmails);
            const serializedEmails = encodeURIComponent(JSON.stringify(arrayEmails));
            const response = await fetch(`${URLS.USER.EMAIL}?userEmails=${serializedEmails}`);
            const usersDetected = await response.json();

            setUsers(usersDetected);
          } catch (error) {
            toast({
              variant: 'destructive',
              title: 'Erro na deteccao de rostos.',
            });
          } finally {
            setLoadingImg(false);
          }
        })());
      };

      img.onerror = function() {
        toast({
          variant: 'destructive',
          title: 'Erro carregando a imagem.',
        });
      };

      img.src = URL.createObjectURL(selectedImage);
    }
  }, [selectedImage,]);

  return (
    <div className="container mx-auto p-4">
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="md:col-span-2">
          <CardHeader>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="image-upload" className="sr-only">Upload Image</Label>
              <div className="flex items-center justify-center relative">
                {loadingImg && (
                  <div className="absolute inset-0 flex items-center justify-center bg-background/10 backdrop-blur-sm z-50">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                )}
                <Input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={(event) => { setLoadingImg(true); setSelectedImage(event.target.files[0]); }}
                  className="hidden"
                />
                <Label
                  htmlFor="image-upload"
                  className="cursor-pointer flex items-center justify-center w-full aspect-video border-2 border-dashed rounded-md hover:border-primary"
                >
                  {selectedImage ? (
                    <div className="relative w-full h-full">
                      <ImageComponent
                        src={URL.createObjectURL(selectedImage)}
                        alt="Uploaded class image"
                        layout="fill"
                        objectFit="contain"
                        className="rounded-md"
                        id='studentsPicture'
                      />
                      <Button
                        variant="destructive"
                        size="icon"
                        className="absolute top-2 right-2"
                        onClick={() => { setSelectedImage(null); }}
                      >
                        <X className="h-4 w-4" onClick={() => setSaveButton('Salvar')} />
                      </Button>
                    </div>
                  ) : !loadingModels ? (
                    <div className="text-center">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-2 text-sm text-gray-500">Foto usada no reconhecimento facial.</p>
                    </div>
                  ) : (
                    <div className="animate-in fade-in duration-300 flex flex-col w-full h-full items-center justify-center gap-2">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  )}
                </Label>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className='grid place-items-center'>
          <CardContent>
            <form className="space-y-4">
              <div className="space-y-2">
                <Calendar
                  disabled={{ after: today, }}
                  weekStartsOn={1}
                  locale={ptBR}
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  className="rounded-md border"
                />
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
      <div className='grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mt-6'>
        <Select onValueChange={setSelectedServiceId} >
          <SelectTrigger className="w-full">
            <SelectValue placeholder='Serviços' />
          </SelectTrigger>
          <SelectContent>
            {services.map(service => (<SelectItem key={service.id} value={service.id}>{service.title}</SelectItem>))}
          </SelectContent>
        </Select>
        <Select onValueChange={setSelectedScheduleId} >
          <SelectTrigger className="w-full">
            <SelectValue placeholder='Período' />
          </SelectTrigger>
          <SelectContent>
            {schedules && schedules.map(schedule => (<SelectItem key={schedule.id} value={schedule.id}>{schedule.hour}</SelectItem>))}
          </SelectContent>
        </Select>
      </div>
      <SearchUser onSelect={(user: Profile) => setUsers(prev => [...prev, user,])} />
      <Card className="mt-6">
        <CardHeader>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead/>
                <TableHead>Nome</TableHead>
                <TableHead className="text-right">Presença</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users && users.map((user, index) => (
                <TableRow key={user.id}>
                  <TableCell className='w-16'>{index + 1}</TableCell>
                  <TableCell>{user.name}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="destructive"
                      size="icon"
                      className=""
                      onClick={() => { removeUser(user.id); }}
                    >
                      <X className="h-4 w-4"/>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      <div className="mt-6 flex justify-end">
        <Button onClick={() => saveAttendance()}>{saveButton}</Button>
      </div>
    </div>
  );
};

export default Attendance;