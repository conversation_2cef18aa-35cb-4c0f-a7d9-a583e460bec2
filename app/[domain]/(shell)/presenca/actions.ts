'use server';

import { getToday, } from '@/lib/utils';
import { userInfo, } from '@/supabase/verifications/user-info';
import { UNAUTHORIZED, } from '@/utils/constants';
import { Lead, Service, } from '@/utils/constants/types';
import { createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestResponse, } from '@supabase/supabase-js';
import { format, getDay, isFuture, } from 'date-fns';

export const saveStudentAttendance = async ({ serviceId, scheduleHour, }: { serviceId: string, scheduleHour: string }) => {
  const user = await userInfo();
  if (!user) return {
    message: UNAUTHORIZED,
    status: 401,
  };

  const today = getToday();
  const supabaseAdmin = createClientAdmin();
  const { data: schedule, } = await supabaseAdmin
    .from('schedule')
    .select()
    .match({ serviceId, hour: scheduleHour, number: getDay(today), })
    .maybeSingle();
    console.log('🚀 ~ saveStudentAttendance ~ schedule:', schedule);

  if (!schedule) return {
    message: 'Erro ao buscar horários.',
    status: 400,
  };

  let scheduleDateAndHour = new Date();
  scheduleDateAndHour.setUTCHours(scheduleDateAndHour.getUTCHours() - 3);
  scheduleDateAndHour.setUTCHours(parseInt(schedule.hour.split(':')[0]));
  scheduleDateAndHour.setMinutes(parseInt(schedule.hour.split(':')[1]));

  if (isFuture(scheduleDateAndHour)) return {
    message: 'Aula ainda não aconteceu.',
    status: 400,
  };

  const formatToday = format(today, 'yyyy-MM-dd');
  const { data, } = await supabaseAdmin
    .from('attendance')
    .select()
    .match({
      userId: user.id,
      serviceId,
      scheduleId: schedule.id,
      date: formatToday,
    })
    .maybeSingle();

  if (!data) {
    const { error, } = await supabaseAdmin
      .from('attendance')
      .insert({
        userId: user.id,
        serviceId,
        scheduleId: schedule.id,
        date: formatToday,
        confirmed: true,
      })
      .select();

    if (error) return {
      message: 'Erro ao fazer check in.',
      status: 400,
    };
    return { status: 200, };
  }

  if (data?.confirmed === false) {
    const { error, } = await supabaseAdmin
      .from('attendance')
      .update({
        confirmed: true,
      })
      .match({ id: data.id, })
      .select();

    if (error) return {
      message: 'Erro ao fazer check in.',
      status: 400,
    };
    return { status: 200, };
  }

  if (data.confirmed === true) return {
    status: 200,
  };
};

export const saveFriendAttendance = async ({ serviceId, friendId, scheduleHour, }: { serviceId: string, friendId: string, scheduleHour: string }) => {

  const supabaseAdmin = createClientAdmin();


  const today = getToday();
  console.log('🚀 ~ saveFriendAttendance ~ today:', today);
  console.log('🚀 ~ saveFriendAttendance ~ getDay(today):', getDay(today));
  const { data: schedule, } = await supabaseAdmin
    .from('schedule')
    .select()
    .match({ serviceId, hour: scheduleHour, number: getDay(today), })
    .maybeSingle();

  const formatToday = format(today, 'yyyy-MM-dd');
  const { data: todayAttendance, error, } = await supabaseAdmin
    .from('attendance')
    .select()
    .match({
      userId: friendId,
      serviceId,
      scheduleId: schedule.id,
      date: formatToday,
    })
    .maybeSingle();

    console.log('🚀 ~ saveFriendAttendance ~ error:', error);
    console.log('🚀 ~ saveFriendAttendance ~ todayAttendance:', todayAttendance);
  if (!todayAttendance && !error) {
    const { error, } = await supabaseAdmin
      .from('attendance')
      .insert({
        userId: friendId,
        serviceId,
        scheduleId: schedule.id,
        date: formatToday,
        confirmed: false,
      });
      console.log('🚀 ~ saveFriendAttendance ~ error:', error);
    if (!error) return { status: 200, };
  }

  if (todayAttendance.confirmed === true) return {
    status: 200,
  };

  if (todayAttendance.confirmed === false) {
    const user = await userInfo();
    const { error, } = await supabaseAdmin
      .from('attendance')
      .update({
        userId: friendId,
        serviceId,
        scheduleId: schedule.id,
        date: formatToday,
        confirmed: true,
        by: user?.id,
      })
      .match({ userId: friendId, serviceId, scheduleId: schedule.id, date: formatToday, });
      console.log('🚀 ~ saveFriendAttendance ~ error:', error);
    if (!error) return { status: 200, };
  }


  console.log('🚀 ~ saveStudentAttendance ~ error:', error);

  if (error) return {
    message: 'Erro ao fazer check in.',
    status: 400,
  };

  return { status: 200, };
};

export const getLeads = async (): Promise<{ data: Lead[], status: 200 } | { message: string, status: 400 }> => {
  const user = await userInfo();
  const supabaseAdmin = createClientAdmin();
  const { data, }: PostgrestResponse<Lead> = await supabaseAdmin
    .from('lead')
    .select()
    .match({ schoolId: user?.user_metadata.schoolId, });

  if (!data) return {
    message: 'Erro ao buscar prospectos.',
    status: 400,
  };

  if (!data.length) return {
    message: 'Não existem prospectos.',
    status: 400,
  };

  return { data, status: 200, };
};

export const getFriendsAttendance = async ({ serviceId, scheduleHour, }) => {
  const user = await userInfo();
  const supabaseAdmin = createClientAdmin();
  const today = getToday();
  const { data: schedule, } = await supabaseAdmin
    .from('schedule')
    .select()
    .match({ serviceId, hour: scheduleHour, number: getDay(today), })
    .maybeSingle();
  const { data: attendance, error, } = await supabaseAdmin
    .rpc('friends_attendance', { service: serviceId, schedule: schedule.id, exclude: user.id, day: getToday(), });
  console.log('🚀 ~ getFriendsAttendance ~ error:', error);
  console.log('🚀 ~ getFriendsAttendance ~ attendance:', attendance);
  if (!error) return {
    data: attendance,
    status: 200,
  };
  return {
    message: 'Erro ao buscar presenças.',
    status: 400,
  };
};

export const getAttendanceRank = async ({ month, serviceId, limit = 10, }: { month: number, serviceId: string, limit: number, }): Promise<{ data: { userId: string, name: string, profileImage: string, total: number }, status: 200 } | { message: string, status: 400 } > => {
  const supabaseAdmin = createClientAdmin();
  const currentYear = new Date().getFullYear();

  if (month) {
    const { data: monthlyAttendances, } = await supabaseAdmin
      .from('attendance_monthly')
      .select()
      .match({ year: currentYear, month, serviceId, })
      .limit(limit);
    if (!monthlyAttendances) return { message: 'Erro ao buscar frequência.', status: 400, };
    return { data: monthlyAttendances, status: 200, };
  }
  const { data: yearlyAttendances, } = await supabaseAdmin
    .from('attendance_yearly')
    .select()
    .match({ year: currentYear, serviceId, })
    .limit(limit);
  if (!yearlyAttendances) return { message: 'Erro ao buscar frequência.', status: 400, };
  return { data: yearlyAttendances, status: 200, };
};