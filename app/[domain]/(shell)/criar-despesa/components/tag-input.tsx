'use client';

import React, { useState, useRef, KeyboardEvent, } from 'react';
import { X, } from 'lucide-react';
import { Button, Input, Label, } from '@/components/index';
import { Category, } from '@/utils/constants/types';

interface TagInputProps {
  suggestions?: Category[]
  onTagsChange: (tags: Category[]) => Promise<void>
}

export default function TagInput({ suggestions = [], onTagsChange, }: TagInputProps) {
  const [inputValue, setInputValue,] = useState('');
  const [tags, setTags,] = useState<Category[]>([]);
  const [filteredSuggestions, setFilteredSuggestions,] = useState<Category[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  const addTag = async (tag: Category) => {
    if (tag && !tags.find(t => t.name === tag.name)) {
      const newTags = [...tags, tag,];
      setTags(newTags);
      setInputValue('');
      setFilteredSuggestions([]);
      await onTagsChange(newTags);
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = tags.filter((tag: Category) => tag.id !== tagToRemove);
    setTags(newTags);
    onTagsChange(newTags);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    if (value) {
      const filtered = suggestions.filter(suggestion =>
        suggestion.name.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredSuggestions(filtered);
    } else {
      setFilteredSuggestions([]);
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue) {
      e.preventDefault();
      addTag({ id: null, name: inputValue, });
    }
  };

  return (
    <div className="space-y-2">
      <div className="relative">
        {filteredSuggestions.length > 0 && (
          <ul className="absolute z-10 w-full bg-background border border-input rounded-md mt-1 max-h-60 overflow-auto">
            {filteredSuggestions.map(suggestion => (
              <li
                key={suggestion.id}
                className="px-3 py-2 hover:bg-accent hover:text-accent-foreground cursor-pointer"
                onClick={() => addTag(suggestion)}
              >
                {suggestion.name}
              </li>
            ))}
          </ul>
        )}
        <Label>
          Categoria
        </Label>
        <Input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder="Digite pra adicionar categorias. Exemplo: agua, luz, telefone."
          className="w-full"
        />
      </div>
      <div className="flex flex-wrap gap-2">
        {tags.map((tag: Category) => (
          <span key={tag.id} className="bg-primary text-primary-foreground px-2 py-1 rounded-full text-sm flex items-center">
            {tag.name}
            <Button
              variant="ghost"
              size="sm"
              className="ml-1 h-4 w-4 p-0 hover:bg-primary-foreground hover:text-primary"
              onClick={() => removeTag(tag.id)}
            >
              <X className="h-3 w-3" />
            </Button>
          </span>
        ))}
      </div>
    </div>
  );
}

