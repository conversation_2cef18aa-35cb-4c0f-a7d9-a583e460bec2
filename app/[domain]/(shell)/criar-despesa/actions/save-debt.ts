'use server';

import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { DebtFormData, } from '../page';
import { Category, DebtRoot, } from '@/utils/constants/types';
import { PostgrestMaybeSingleResponse, } from '@supabase/supabase-js';
import { userInfo, } from '@/supabase/verifications/user-info';
import { format, getDate, parse, parseISO, } from 'date-fns';
import { formatFromBrDate } from '@/lib/utils';

export const parseBrazilianReal = (realValue: string) => {
  const cleanValue = realValue.replace(/[R$.,\s]/g, '');
  return cleanValue.replace(/(\d{2})$/, '.$1');
};

export const getSuggestions = async () => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  const supabaseAdmin = createClientAdmin();
  const { data: debtCategory, } = await supabaseAdmin
    .from('category')
    .select()
    .match({ schoolId: user?.user_metadata.schoolId, });
  if (!debtCategory) return [];
  return debtCategory;
};

export const saveCategory = async (category: Category): Promise<{ data: Category, status: 201 } | { message: string, status: 400 }> => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  const supabaseAdmin = createClientAdmin();
  const { data, } = await supabaseAdmin
    .from('category')
    .insert({
      schoolId: user?.user_metadata.schoolId,
      name: category.name,
    })
    .select()
    .returns<Category | null>();
  if (data) return { data, status: 201, };
  return { message: 'Erro ao criar categoria.', status: 400, };
};

export const saveDebt = async ({
  categories,
  formData,
}: { formData: DebtFormData, categories: Category[] }) => {

  const user = await userInfo();
  const { repeats, ...data } = formData;
  const supabaseAdmin = createClientAdmin();
  const { data: debtRoot, error, }: PostgrestMaybeSingleResponse<DebtRoot> = await supabaseAdmin
    .from('debt_root')
    .insert({
      ...data,
      value: await parseBrazilianReal(formData.value),
      billingDate: formatFromBrDate(formData.billingDate),
      billingDay: getDate(parseISO(formatFromBrDate(formData.billingDate))),
      schoolId: user?.user_metadata.schoolId,
      fixed: repeats === 'fixed',
      ...(repeats !== 'fixed' && repeats !== 'once' && { repetitions: repeats, }),
    })
    .select()
    .maybeSingle();
  console.log('🚀 ~ debt:', debtRoot);
  console.log('🚀 ~ error:', error);

  if (debtRoot) {
    categories.map(async category => {
      if (!category.id) {
        const { data, }: { data: Category | null } = await supabaseAdmin
          .from('category')
          .select()
          .match({ name: category.name, schoolId: user?.user_metadata.schoolId, })
          .maybeSingle();
        if (data) category.id = data?.id;
      }

      const { error, } = await supabaseAdmin
        .from('debt_category')
        .insert({
          schoolId: user?.user_metadata.schoolId,
          debtRootId: debtRoot.id,
          categoryId: category.id,
        }).select();
        console.log('🚀 ~ debt_category:', error);
      });
    return { data: debtRoot, status: 201, };
  }
  return { message: 'Erro ao salvar despesa.', status: 400, };
};