'use client';

import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
} from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import { ChangeEvent, useEffect, useState, } from 'react';
import { useForm, } from 'react-hook-form';
import { z, } from 'zod';
import { zodResolver, } from '@hookform/resolvers/zod';
import TagInput from './components/tag-input';
import { getSuggestions, saveCategory, saveDebt, } from './actions/save-debt';
import { Category, } from '@/utils/constants/types';
import { formatDate, } from '@/lib/utils';
import { format, } from 'date-fns';

const requiredMessage = 'Campo obrigatório.';
const formSchema = z.object({
  billingDate: z.string({ required_error: 'Campo obrigatório.', }).regex(/^\d{2}\/\d{2}\/\d{4}$/, 'Formato inválido.'),
  category: z.string({ required_error: requiredMessage, })
    .max(25, { message: 'Nome muito longo, máximo 25 letras.', }).optional(),
  description: z.string({ required_error: requiredMessage, })
    .min(10, { message: 'Descrição muito curta, mínimo 10 letras.', })
    .max(500, { message: 'Descrição muito longa, máximo 40 letras.', }).optional(),
  fixed: z.boolean().default(false),
  name: z.string({ required_error: requiredMessage, })
    .max(40, { message: 'Nome muito longo, máximo 40 letras.', }),
  method: z.string().optional(),
  paid: z.boolean().default(false),
  paidDate: z.string().optional(),
  repetitions: z.string().optional(),
  value: z.string({ required_error: requiredMessage, })
    .min(3, { message: 'Preço não permitido.', })
    .max(11, { message: 'Preço não permitido.', }),
  repeats: z.string({ required_error: requiredMessage, }),
});

export type DebtFormData = z.infer<typeof formSchema>;

const CreateDebt = () => {
  const [isLoading, setIsLoading,] = useState(false);
  const [suggestions, setSuggestions,] = useState<Category[]>([]);
  const [categories, setCategories,] = useState<Category[]>([]);

  const form = useForm<DebtFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      repeats: 'once',
      billingDate: format(new Date(), 'dd/MM/yyyy'),
    },
  });

  const onSubmit = async (formData: z.infer<typeof formSchema>) => {
    setIsLoading(true);
    const response = await saveDebt({ categories, formData, });
    if (response.status === 201) {
      toast({
        description: 'Despesa salva com sucesso.',
      });
    } else {
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
    setIsLoading(false);
  };

  const formatPrice = (value: string) => {
    const digits = value.replace(/\D/g, '');

    const formatter = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    const numericValue = parseInt(digits, 10) / 100;
    return formatter.format(numericValue);
  };

  const handlePriceChange = (event: ChangeEvent<HTMLInputElement>) => {
    const rawValue = event.target.value.replace(/\D/g, '');
    const formattedValue = formatPrice(rawValue);
    form.setValue('value', formattedValue, { shouldValidate: true, });
  };

  const handleTagsChange = async (tags: Category[]) => {
    const addedTag = tags[tags.length - 1];
    if (addedTag?.id === null) {
      const saveCategoryResponse = await saveCategory(addedTag);
      if (saveCategoryResponse.status === 201) {
        const category = saveCategoryResponse.data;
        setCategories([...categories, category,]);
      }
    }
    setCategories(tags);
  };

  const handleBillingDateChange = (e: ChangeEvent<HTMLInputElement>) => {
    const formatted = formatDate(e.target.value);
    form.setValue('billingDate', formatted, { shouldValidate: true, });
  };

  useEffect(() => {
    (async () => {
      const suggestion = await getSuggestions();
      setSuggestions(suggestion);
    })();
  }, []);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 w-full">
        <FormField
          control={form.control}
          name='repeats'
          render={({ field, }) => (
            <FormItem>
              <FormLabel>Repetições</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value='once'>Cobrança única</SelectItem>
                  <SelectItem value='fixed'>Indefinido</SelectItem>
                  {Array.from({ length: 23, }, (_, i) => i + 2).map((num) => (
                    <SelectItem key={num} value={num.toString()}>
                      {num} vezes
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='name'
          render={({ field, }) => (
            <FormItem>
              <FormLabel>Nome</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder='Nome da despesa'
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='description'
          render={({ field, }) => (
            <FormItem>
              <FormLabel className=''>
                Descrição
              </FormLabel>
              <Textarea {...field} placeholder='Detalhes sobre a despesa' />
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='value'
          render={({ field, }) => (
            <FormItem>
              <FormLabel htmlFor="value">Valor</FormLabel>
              <FormControl>
                <Input
                  id="value"
                  {...field}
                  onChange={handlePriceChange}
                  placeholder="R$ 0,00"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='billingDate'
          render={({ field, }) => (
            <FormItem>
              <FormLabel htmlFor="billingDate">Data da fatura</FormLabel>
              <FormControl>
                <Input
                  id="billingDate"
                  {...field}
                  placeholder='01/01/2025'
                  onChange={handleBillingDateChange}
                  maxLength={10}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <TagInput suggestions={suggestions} onTagsChange={handleTagsChange} />
        <FormField
          control={form.control}
          name='method'
          render={({ field, }) => (
            <FormItem>
              <FormControl>
                <Select onValueChange={field.onChange} >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder='Método de pagamento' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='CREDIT_CARD'>Cartão de crédito</SelectItem>
                      <SelectItem value='PIX'>Pix</SelectItem>
                      <SelectItem value='BOLETO'>Boleto</SelectItem>
                    </SelectContent>
                  </Select>
              </FormControl>
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full">
          {isLoading ? 'Salvando...' : 'Salvar despesa'}
        </Button>
      </form>
    </Form>
  );
};

export default CreateDebt;