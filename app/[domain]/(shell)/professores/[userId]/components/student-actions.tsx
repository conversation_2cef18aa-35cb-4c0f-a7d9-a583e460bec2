'use client';

import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger, Button, DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, } from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import { URLS, } from '@/utils/supabase/constants';
import { MoreHorizontal, } from 'lucide-react';
import React, { useState, } from 'react';
import { Profile, } from '@/utils/constants/types';
import { deleteUser, inviteUser, } from '../../actions';

const Actions = ({ profile, }: { profile: Profile }) => {
  const [showAlert, setShowAlert,] = useState(false);
  const [deleteLabel, setDeleteLabel,] = useState('Deletar');

  const changeStudentStatus = async (status: string) => {
    const response = await fetch(URLS.STUDENT_STATUS, {
      method: 'PATCH',
      body: JSON.stringify({ id: profile.id, status, }),
    });

    if (response.status === 200) {
      toast({
        title: 'Status do estudante alterado com sucesso.',
      });
    } else {
      const data = await response.json();
      toast({
        variant: 'destructive',
        title: data.message,
      });
    }
  };

  const handleInvite = async () => {
    const response = await inviteUser({
      id: profile.id,
    });

    if (response.status === 200) {
      toast({
        title: 'Convite enviado com sucesso.',
      });
    } else {
      toast({
        variant: 'destructive',
        title: 'Erro ao enviar convite',
        description: response.message,
      });
    }
  };

  const handleDeleteUser = async () => {
    setDeleteLabel('Deletando');
    const response = await deleteUser({
      profileId: profile.id,
    });
    setShowAlert(false);
    if (response.status === 200) {
      toast({
        title: 'Usuário deletado com sucesso.',
      });
    } else {
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
  };

  return (
    <>
      <AlertDialog open={showAlert} onOpenChange={setShowAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Tem certeza?</AlertDialogTitle>
            <AlertDialogDescription>
              Ao remover aluno, você vai perder todos os dados relacionados a esse aluno como por exemplo compras, presenças, progresso, etc.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteUser} disabled={deleteLabel === 'Deletando'}>
              {deleteLabel}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <div className='bg-slate-200 rounded-xl w-8 h-6 flex items-center'>
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
            >
              <MoreHorizontal />
              <span className="sr-only">Abrir menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[200px]">
            {profile.status === 'active' && (
              <DropdownMenuItem
                onSelect={() => changeStudentStatus('inactive')}
                className="cursor-pointer"
              >
                Marcar como Inativo
              </DropdownMenuItem>
            )}
            {profile.status === 'inactive' && (
              <DropdownMenuItem
                onSelect={() => changeStudentStatus('active')}
                className="cursor-pointer"
              >
                Reativar
              </DropdownMenuItem>
            )}
            {profile.status === 'pending' && (
              <DropdownMenuItem
                onSelect={() => changeStudentStatus('active')}
                className="cursor-pointer"
              >
                Aprovar
              </DropdownMenuItem>
            )}
            {profile.status === 'pending' && (
              <DropdownMenuItem
                onSelect={handleInvite}
                className="cursor-pointer"
              >
                Reenviar Convite
              </DropdownMenuItem>
            )}
            <DropdownMenuItem
              onSelect={() => setShowAlert(true)}
              className="cursor-pointer"
            >
              Deletar usuário
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </>
  );
};

export default Actions;