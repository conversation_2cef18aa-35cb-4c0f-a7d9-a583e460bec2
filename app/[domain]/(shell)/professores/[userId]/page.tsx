'use server';

import ProfileDetails from './components/profile-details';
import { userInfo, } from '@/supabase/verifications/user-info';
import { redirect, } from 'next/navigation';
import Classes from './components/classes';

export default async function Students({ params: { domain, userId, }, }: { params: { domain: string, userId: string } }) {
  const user = await userInfo();
  if (!user || user.user_metadata.role !== 'admin') redirect('/login');

  return (
    <div className='container mx-auto space-y-6 p-4'>
      <ProfileDetails domain={domain} userId={userId} />
      <Classes userId={userId} />
    </div>
  );
}