'use server';

import { getProfile, } from '@/app/actions';
import { Plan, Rank, RankStudent, SportProfile, Subscription, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { User, } from '@supabase/auth-js';
import { PostgrestMaybeSingleResponse, PostgrestResponse, PostgrestSingleResponse, } from '@supabase/supabase-js';
import { revalidatePath, } from 'next/cache';

type FormData = {
  id: string;
};

type RanksResponse = RankStudent & { rank: Rank  & { nextRank: Rank } }

type RanksReturn = {
  data: RanksResponse,
  status: 200,
} | {
  message: string,
  status: 400
}

export const inviteUser = async (formData: FormData) => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user) {
    return { status: 400, message: '<PERSON>u<PERSON><PERSON> não autenticado.', };
  }

  const supabaseAdmin = createClientAdmin();
  const { data: profile, } = await supabaseAdmin.from('profile').select().eq('id', user.id).maybeSingle();

  if (profile?.type !== 'admin' && profile?.type !== 'teacher') {
    return { status: 400, message: 'Sem permissão para enviar convite.', };
  }

  const { data: studentData, } = await supabaseAdmin
    .from('profile').select('email, schoolId').eq('id', formData.id).single();

  if (!studentData?.email || !studentData?.schoolId) {
    return { status: 400, message: 'Dados do aluno incompletos.', };
  }

  const { data: schoolData, } = await supabaseAdmin.from('school').select('domain').eq('id', studentData.schoolId).maybeSingle();

  if (!schoolData?.domain) {
    return { status: 400, message: 'Domínio da escola não encontrado.', };
  }

  const domain = schoolData.domain;
  const { error, } = await supabase.auth.resend({
    type: 'signup',
    email: studentData.email,
    options: {
      emailRedirectTo: `https://${domain}.meumestre.com.br`,
    },
  });

  if (error) {
    return { status: 400, message: `Erro: ${error.message}`,};
  }

  return { status: 200, };
};

export const deleteUser = async ({ profileId, }: { profileId: string }): Promise<{ data: User, status: 200 } | { message: string, status: 400 }> => {
  const supabase = createClientAdmin();
  const { data, } = await supabase.auth.admin.deleteUser(profileId);
  if (data && data.user) return { data: data.user, status: 200, };
  return { message: 'Não foi possível deletar o usuário.', status: 400, };
};

export const getStudentRank = async ({ userId, sportId, }: { userId: string, sportId: string }): Promise<RanksReturn> => {
  const supabaseAdmin = createClientAdmin();
  const { data, }: PostgrestResponse<RanksResponse> = await supabaseAdmin
  .from('rank_student')
  .select('*, rank(*, nextRank(*))')
  .match({ profileId: userId, sportId, })
  .order('createdAt', { ascending: false, });

  if (!data) return {
    message: 'Não foi possível buscar as graduações.',
    status: 400,
  };

  return { data: data[0], status: 200, };
};

export const sportHasRank = async ({ sportId, }: { sportId: string }) => {
  console.log('🚀 ~ sportHasRank ~ sportId:', sportId);
  const supabaseAdmin = createClientAdmin();
  const { count, } = await supabaseAdmin
    .from('rank')
    .select('*', { count: 'exact', head: true, })
    .match({ sportId, });
  console.log('🚀 ~ sportHasRank ~ count:', count);

  if (count === null) return {
    message: 'Erro ao buscar graduações do esporte.',
    status: 400,
  };
  return {
    data: count > 0,
    status: 200,
  };
};

export const getSportRanks = async ({ sportId, }: { sportId: string }) => {
  const supabaseAdmin = createClientAdmin();
  const { data: ranks, } = await supabaseAdmin
    .from('rank')
    .select()
    .match({ sportId, });

  if (!ranks) return {
    message: 'Erro ao buscar graduações do esporte.',
    status: 400,
  };
  return {
    data: ranks,
    status: 200,
  };
};