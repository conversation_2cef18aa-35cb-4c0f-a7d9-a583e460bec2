'use server';

import { getToday, } from '@/lib/utils';
import { userInfo, } from '@/supabase/verifications/user-info';
import { UNAUTHORIZED, } from '@/utils/constants';
import { Schedule, Service, } from '@/utils/constants/types';
import { createClientAdmin, } from '@/utils/supabase/server';
import { addMinutes, getDay, isWithinInterval, subMinutes, } from 'date-fns';

const inTimeRange = ({ time, duration = 60, }: { time: string, duration: number }) => {
  const now = getToday();

  const [scheduleHour, scheduleMinute,] = time.split(':').map(Number);
  const scheduleTime = getToday();
  scheduleTime.setUTCHours(scheduleHour, scheduleMinute, 0, 0);

  const lowerBound = subMinutes(scheduleTime, 30);
  const upperBound = addMinutes(scheduleTime, 30 + duration);

  return isWithinInterval(now, {
      start: lowerBound,
      end: upperBound,
  });
};

export const getCurrentService = async (): Promise<{ data: (Schedule & { service: Service })[], status: 200 } | { message: string, status: 400 | 401 }> => {
  const user = await userInfo();

  if (!user) return {
    message: UNAUTHORIZED,
    status: 401,
  };

  const supabaseAdmin = createClientAdmin();
  const { data, } = await supabaseAdmin
    .from('service_student')
    .select('service(*)')
    .match({ profileId: user.id, })
    .returns<{ service: Service }[] | null>();

  if (!data) return {
    message: 'Nenhum serviço encontrado.',
    status: 400,
  };

  let services: Service[] | null = [];

  if (data.length === 0) {
    const { data, } = await supabaseAdmin
      .from('service')
      .select()
      .match({ schoolId: user.user_metadata.schoolId, })
      .returns<Service[]>();

    services = data;

    if (!services || services.length === 0) return {
      message: 'Não encontramos serviços cadastrados.',
      status: 400,
    };
  } else {
    services = data.map(serviceStudent => serviceStudent.service);
  }

  const today = getToday();
  const { data: schedules, } = await supabaseAdmin
    .from('schedule')
    .select('*, service(*)')
    .match({ number: getDay(today), })
    .in('serviceId', services?.map(service => service.id) || [])
    .returns<(Schedule & { service: Service })[] | null>();

    if (!schedules) return {
      message: 'Nenhum horário encontrado.',
      status: 400,
    };

  const inTimeRangeSchedule = schedules.filter(schedule => inTimeRange({
    time: schedule.hour,
    duration: schedule.service.duration,
  }));

  return { data: inTimeRangeSchedule, status: 200, };
};