'use client';

import React, { useEffect, } from 'react';
import { getCurrentService, } from './actions';
import { toast } from '@/components/ui/shard/use-toast';
import { saveAttendance } from '../perfil/actions';
import { getToday } from '@/lib/utils';
import { useRouter } from 'next/navigation';

const QrCode = () => {
  const router = useRouter();

  useEffect(() => {
    (async () => {
      const response = await getCurrentService();
      if (response.status === 200) {
        const { data: schedules, } = response;
        if (schedules.length > 0) {
          const schedule = schedules[0];
          const attendanceResponse = await saveAttendance({
            confirmed: true,
            scheduleId: schedule.id,
            serviceId: schedule.service.id, date: getToday(),
          });
          if (attendanceResponse.status === 200) {
            toast({
              title: 'Presença confirmada!',
            });
          } else {
            toast({
              variant: 'destructive',
              title: attendanceResponse.message,
            });
          }
        } else {
          toast({
            variant: 'destructive',
            title: 'Nenhum hor<PERSON>rio encontrado.',
          });
        }
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    router.push('/perfil');
    })();
  }, []);

  return (
    <div className='flex w-full h-full justify-center items-center'>
      <span>Salvando presença...</span>
    </div>
  );
};

export default QrCode;