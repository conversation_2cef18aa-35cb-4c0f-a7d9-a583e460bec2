'use client';

import { type LucideIcon, } from 'lucide-react';

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/index';
import { usePathname, } from 'next/navigation';
import Link from 'next/link';
import { cn, } from '@/lib/utils';

const LinkComponent = ({ Icon, label, href, }: { Icon: LucideIcon, label: string, href: string }) => {
  const pathname = usePathname();
  return (
    <Link
      href={href}
      className={cn('flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary', pathname.indexOf(href) !== -1 && 'bg-slate-200 text-slate-900')}
    >
      <Icon className='h-4' />
      {label}
    </Link>
  );
};

export function NavMain({
  items,
}: {
  items: {
    label: string
    href: string
    icon: LucideIcon
  }[]
}) {
  return (
    <SidebarGroup>
      <SidebarGroupContent className='flex flex-col gap-2'>
        <SidebarMenu>
          {items.map((item) => (
            <SidebarMenuItem key={item.label}>
              <SidebarMenuButton tooltip={item.label}>
              <LinkComponent key={item.href} Icon={item.icon} href={item.href} label={item.label} />
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
