import { formatToBrazilianReal, } from '@/lib/utils';
import React from 'react';

interface ContractProps {
  schoolName: string | null;
  plan: string | null;
  price: string | null;
  start: string;
  end: string | null;
  studentName: string;
  birthDate: string;
  cnpj: string | null;
  cpf: string;
  email: string;
  hours: string;
  priceUpdates: string;
  reimbursement: string;
  cancel: string;
  recurrency: string;
}

const Contract: React.FC<ContractProps> = ({
  schoolName,
  plan,
  price,
  start,
  end,
  studentName,
  birthDate,
  cpf,
  cnpj,
  email,
  hours,
  priceUpdates,
  reimbursement,
  cancel,
  recurrency,
}) => {
  return (
    <div className="w-full mx-auto p-6 bg-white shadow-lg rounded-lg">
      <h1 className="text-2xl font-bold mb-6 text-center">CONTRATO {schoolName}</h1>

      <div className="mb-6">
        <p><strong>Plano contratado:</strong> {plan}</p>
        {price && <p><strong>Preço:</strong> {formatToBrazilianReal(price)}</p>}
        <p><strong>Inicio:</strong> {start}</p>
        <p><strong>Fim:</strong> {end || 'Indefinido'}</p>
        <p><strong>Recorrência:</strong> {recurrency}</p>
      </div>

      <div className="mb-6">
        <p>Contrato firmado entre <strong>{studentName}</strong>, nascido em <strong>{birthDate}</strong>, registrado sob CPF de número <strong>{cpf}</strong>, com correspondência eletrônica <strong>{email}</strong> e {schoolName}, registrado sob CNPJ {cnpj}.</p>
      </div>

      <div className="mb-6">
        <p>Programa de atividades físicas realizadas em espaços previamente definidos, com horários pré-estabelecidos e divulgados, sujeitos a alterações sem prévio aviso e sem restituição financeira ao cliente em decorrência de tais alterações. Inclusão ou exclusão de taxas adicionais será realizada mediante emenda contratual.</p>
      </div>

      <div className="mb-6">
        <p>Ao assinar esse contrato você também aceita os Termos de Uso e Política de Privacidade do sistema Meu Mestre relacionado a escola caso o utilize.</p>
        <p>
          https://meumestre.com.br/termos-de-uso<br/>
          https://meumestre.com.br/politica-de-privacidade
        </p>
      </div>

      <ol className="list-decimal pl-6 space-y-4">
        {hours && <li><strong>Horário:</strong> {hours}</li>}

        <li>
          <strong>Conduta do cliente dentro das instalações da {schoolName}:</strong>
          A academia tem um regulamento interno e normas especificas a serem seguidas em suas atividades.
          A academia se reserva o direito de impedir a qualquer momento o acesso do cliente as suas instalações,
          caso o mesmo infrinja o regulamento interno, apresente comportamento ou conduta inadequados ou faça má
          utilização dos equipamentos e instalações da academia. Este também se responsabilizará e pagará pelos
          danos feitos dentro do estabelecimento. E qualquer dessas hipóteses o cliente não fara jus à restituição
          de valores pelo período não utilizado.
        </li>

        <li>
          <strong>Responsabilidade:</strong>
          A {schoolName} deixa claro que os alunos recebem treinamentos pra executar as atividades físicas
          sendo de total responsabilidade do alunos quaisquer lesões que venha a ocorrer durante as aulas
          seja por má-execução, impossibilidade física para execução e/ou infortúnios. Declaro que gozo
          de boa saúde, estando apto(a) para a prática esportiva, responsabilizando-me por qualquer dano
          físico que possa vir acontecer durante ou após as aulas.
        </li>

        <li>
          <strong>Guarda de Objetos:</strong>
          A {schoolName} não se responsabiliza pela guarda ou conservação de quaisquer bens de propriedade
          dos clientes depositados, guardados ou esquecidos em qualquer ambiente da academia, inclusive
          vestiários e armários.
        </li>

        <li>
          <strong>Vestimentas:</strong>
          É terminantemente proibido os alunos treinarem de chinelo, sandália, croc, papete, calça jeans,
          bermuda jeans, e ou similares. É proibido o aluno iniciar a aula de artes marciais sem o
          uniforme adequado a cada tipo de luta.
        </li>

        <li>
          <strong>Menores de 18 anos:</strong> Menores de 18 anos poderão frequentar a {schoolName} mediante
          autorização do pai, mãe ou responsável legal, que, ao assinar este termo, responsabilizar-se-á
          pelo menor durante a sua permanência na academia e responderá solidariamente pelos seus atos e
          omissões.
        </li>

        <li>
          <strong>Frequência:</strong> O cliente somente poderá frequentar a {schoolName} enquanto estiver
          em dia com os pagamentos, sendo que estes deverão ser feitos independentemente da frequência
          posto que os serviços, equipamentos e instalações da escola ficarão disponíveis para utilização
          por todo o período contratado.
        </li>

        <li>
          <strong>Bloqueio e Transferência:</strong> O plano recorrente contratado não permite bloqueio e
          transferência do crédito respectivo.
        </li>

        {priceUpdates && <li><strong>Reajuste de preço:</strong> {priceUpdates}</li>}

        {reimbursement && <li><strong>Reembolso:</strong> {reimbursement}</li>}

        {cancel && <li><strong>Cancelamento do plano:</strong> {cancel}</li>}
      </ol>
    </div>
  );
};

export default Contract;

