'use server';

import { formatFromBrDate, } from '@/lib/utils';
import { userInfo, } from '@/supabase/verifications/user-info';
import { UNAUTHORIZED, } from '@/utils/constants';
import { Plan, Profile, } from '@/utils/constants/types';
import { createClientAdmin, } from '@/utils/supabase/server';
import { User, } from '@supabase/supabase-js';

export async function createContract({ data, planId, start, end, }: { data: any, planId?: string, start: string, end?: string }) {
  const user: User | null = await userInfo();

  if (user?.user_metadata.role !== 'admin') {
    return {
      message: UNAUTHORIZED,
      status: 401,
    };
  }

  const token = process.env.AUTENTIQUE_TOKEN;

  try {
    const response = await fetch('https://api.autentique.com.br/v2/graphql', {
      method: 'POST',
      body: data,
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (response.status === 200) {
      const contractInfo = await response.json();
      if (contractInfo.data && contractInfo.data.createDocument) {
        const document = contractInfo.data.createDocument;
        const signee = document.signatures.find((signature: { action: null | { name: string } }) => signature.action?.name === 'SIGN');
        return await saveContract({ studentEmail: signee.email, contractId: document?.id, planId, start, end, });
      } else return {
        message: 'Não foi possível salvar o contrato.',
        status: 400,
      };
    } else {
      return {
        message: 'Não foi possível gerar o contrato.',
        status: 400,
      };
    }
  } catch (error) {
    return {
      message: 'Erro desconhecido ao gerar o contrato.',
      status: 500,
    };
  }
}

export async function getStudents () {
  const user: User | null = await userInfo();

  if (user?.user_metadata.role !== 'admin') {
    return {
      message: UNAUTHORIZED,
      status: 401,
    };
  }


  const supabaseAdmin = createClientAdmin();

  let { data: profiles, } = await supabaseAdmin
    .from('profile')
    .select()
    .match({
      schoolId: user.user_metadata.schoolId,
      type: 'student',
    })
    .order('status', { ascending: false, })
    .returns<Profile[] | null>();

  if (!profiles) {
    return {
      message: 'Não encontramos alunos.',
      status: 400,
    };
  }
  return { data: profiles, status: 200, };
}

export async function getPlans () {
  const user: User | null = await userInfo();

  if (user?.user_metadata.role !== 'admin') {
    return {
      message: UNAUTHORIZED,
      status: 401,
    };
  }


  const supabaseAdmin = createClientAdmin();

  let { data: plans, } = await supabaseAdmin
    .from('plan')
    .select()
    .match({
      schoolId: user.user_metadata.schoolId,
    })
    .eq('active', true)
    .returns<Plan[] | null>();

  if (!plans) {
    return {
      message: 'Não encontramos planos.',
      status: 400,
    };
  }
  return { data: plans, status: 200, };
}

export async function getSchool () {
  const user: User | null = await userInfo();

  if (user?.user_metadata.role !== 'admin') {
    return {
      message: UNAUTHORIZED,
      status: 401,
    };
  }


  const supabaseAdmin = createClientAdmin();

  let { data: school, } = await supabaseAdmin
    .from('school')
    .select()
    .match({
      id: user.user_metadata.schoolId,
    })
    .single();

  if (!school) {
    return {
      message: 'Não encontramos a escola.',
      status: 400,
    };
  }
  return { data: school, status: 200, };
}

export async function saveContract({ studentEmail, contractId, planId, start, end, }: { studentEmail: string, contractId: string, planId?: string, start: string, end?: string }) {
  const user = await userInfo();
  const supabaseAdmin = createClientAdmin();

  const { data: student, } = await supabaseAdmin
    .from('profile')
    .select()
    .match({ email: studentEmail, })
    .maybeSingle();

    console.log({
      id: contractId,
      studentId: student.id,
      schoolId: user?.user_metadata.schoolId,
      planId,
      start: formatFromBrDate(start),
      end: end && formatFromBrDate(end),
    });

  const { error, } = await supabaseAdmin
    .from('contract')
    .upsert({
      id: contractId,
      studentId: student.id,
      schoolId: user?.user_metadata.schoolId,
      planId,
      start: formatFromBrDate(start),
      end: end && formatFromBrDate(end),
    }, { onConflict: 'id', });

  if (error) return {
    message: 'Erro ao salvar contrato no banco de dados',
    status: 400,
  };

  return {
    status: 200,
    message: 'Contrato criado com sucesso.',
  };
}