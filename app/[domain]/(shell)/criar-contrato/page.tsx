'use client';

import { useEffect, useRef, useState, } from 'react';
import ContractForm from './components/contract-form';
import { createContract, getPlans, getSchool, getStudents, } from './actions';
import Contract from './components/contract';
import { Button, Form, Label, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/index';
import { useForm, useWatch, } from 'react-hook-form';
import { validateCPF, } from '@/lib/utils';
import * as z from 'zod';
import { Plan, Profile, School, } from '@/utils/constants/types';
import { zodResolver, } from '@hookform/resolvers/zod';
import { format, parseISO, } from 'date-fns';
import { toast, } from '@/components/ui/shard/use-toast';
import { sendPushNotificationToUser, } from '@/app/actions';
import { useRouter, } from 'next/navigation';

export default function ContractBuilder() {
  const [students, setStudents,] = useState<Profile[] | null>(null);
  const [plans, setPlans,] = useState<Plan[] | null>(null);
  const [selectedStudent, setSelectedStudent,] = useState<Profile | null>(null);
  const [selectedPlan, setSelectedPlan,] = useState<Plan | null>(null);
  const [school, setSchool,] = useState<School | null>(null);
  const [label, setLabel,] = useState('Enviar');
  const componentRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  const formSchema = z.object({
    birthdate: z.string({ required_error: 'Campo obrigatório.', }).regex(/^\d{2}\/\d{2}\/\d{4}$/, 'Formato inválido.'),
    start: z.string({ required_error: 'Campo obrigatório.', }).regex(/^\d{2}\/\d{2}\/\d{4}$/, 'Formato inválido.'),
    end: z.string().regex(/^\d{2}\/\d{2}\/\d{4}$/, 'Formato inválido.').optional(),
    cpf: z.string({ required_error: 'Campo obrigatório.', })
      .regex(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/, 'Formato inválido.')
      .refine(validateCPF, { message: 'CPF inválido.',  }),
    email: z.string({ required_error: 'Campo email é obrigatório.', })
      .min(5, { message: 'No mínimo 5 letras.', })
      .max(50, { message: 'No máximo 50 letras.', }),
    name: z.string({ required_error: 'Campo nome é obrigatório.', })
      .min(5, { message: 'No mínimo 5 letras.', })
      .max(50, { message: 'No máximo 50 letras.', }),
    hours: z.string().optional(),
    priceUpdates: z.string().optional(),
    reimbursement: z.string().optional(),
    cancel: z.string().optional(),
    recurrency: z.string({ required_error: 'Campo obrigatório.', }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      birthdate: '',
      cpf: '',
      name: '',
      email: '',
      hours: 'Os horários poderão sofrer alterações sem aviso prévio, de acordo com as necessidades da contratada, e por motivos de força maior.',
    },
  });

  const watchedValues = useWatch({ // Use useWatch to track changes
    control: form.control,
    name: ['start', 'end', 'name', 'birthdate', 'cpf', 'email', 'hours', 'priceUpdates', 'reimbursement', 'cancel', 'recurrency',], // Specify the fields to watch
  });

  const submit = async () => {
    setLabel('Enviando...');
    if (componentRef.current) {
      const htmlContent = componentRef.current.innerHTML;
      const blob = new Blob([htmlContent,], { type: 'text/html', });
      const data = new FormData();
      data.append('operations', `{"query":"mutation CreateDocumentMutation($document: DocumentInput!, $signers: [SignerInput!]!, $file: Upload!) {createDocument(sandbox: true, document: $document, signers: $signers, file: $file) {id name refusable sortable created_at signatures { public_id name email created_at action { name } link { short_link } user { id name email }}}}", "variables":{"document": {"name": "Contrato de adesão"},"signers": [{"email": "${form.getValues('email')}","action": "SIGN"}],"file":null}}`);
      data.append('map', '{"file": ["variables.file"]}');
      data.append('file', blob, 'my-file.html');
      const response = await createContract({ data, planId: selectedPlan?.id, start: watchedValues['0'], end: watchedValues['1'], });
      if (response.status === 200) {
        setLabel('Enviado');
        const student = students?.find(st => st.id === selectedStudent);
        const response = await sendPushNotificationToUser(student?.id, {
          title: 'Contrato',
          body: 'Um novo contrato foi enviado ao seu email.',
          url : `https://${window.location.hostname}.com.br`,
        });
        if (response.status !== 200) {
          toast({
            variant: 'destructive',
            title: response.message,
          });
        }
        router.push('/contratos');
      } else {
        setLabel('Enviar');
      }
      toast({
        title: response.message,
      });
    }
  };

  const onPlanSelect = (planId: string) => {
    const plan = plans?.find(plan => plan.id === planId);
    if (plan)
    setSelectedPlan(plan);
  };

  useEffect(() => {
    (async () => {
      const response = await getStudents();
      if (response.status === 200) {
        setStudents(response.data);
      }
      const responsePlans = await getPlans();
      if (responsePlans.status === 200) {
        setPlans(responsePlans.data);
      }
      const responseSchool = await getSchool();
      if (responseSchool.status === 200) {
        setSchool(responseSchool.data);
      }
    })();
  }, []);

  useEffect(() => {
    console.log('🚀 ~ useEffect ~ selectedStudent:', selectedStudent);
    if (!selectedStudent) return;
    const student = students?.find(st => st.id === selectedStudent);
    console.log('🚀 ~ useEffect ~ student:', student);
    if (!student) return;
    if (student.birthdate)
    form.setValue('birthdate', format(parseISO(student?.birthdate), 'dd/MM/yyyy'));
    form.setValue('email', student.email);
    form.setValue('name', student.name);
    form.setValue('cpf', student.cpf);
  }, [selectedStudent,]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(submit)}  className="flex flex-col gap-4 w-full">
        <div className='w-full' >
          <div className='grid sm:grid-cols-[1fr_2fr] gap-8'>
            <div className='max-w-sm'>
            {plans && plans.length > 0 && <Label>Selecionar plano</Label> }
              {plans && plans.length > 0 && (
                <Select onValueChange={onPlanSelect} >
                  <SelectTrigger className="h-8 my-4">
                    <SelectValue placeholder='Planos' />
                  </SelectTrigger>
                  <SelectContent side="top">
                    {plans.map((plan) => (
                      <SelectItem key={plan.id} value={plan.id}>
                        {plan.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
              {students && students.length > 0 && <Label>Selecionar aluno</Label> }
              {students && students.length > 0 && (
                <Select onValueChange={setSelectedStudent} >
                  <SelectTrigger className="h-8 my-4">
                    <SelectValue placeholder='Alunos' />
                  </SelectTrigger>
                  <SelectContent side="top">
                    {students.map((student) => (
                      <SelectItem key={student.id} value={student.id}>
                        {student.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
              <ContractForm form={form} />
            </div>
            <div ref={componentRef} className='w-full'>
              <Contract
                ref={componentRef}
                schoolName={school?.name}
                cnpj={school?.cnpj}
                plan={selectedPlan?.name}
                price={selectedPlan?.price}
                start={watchedValues['0']}
                end={watchedValues['1']}
                studentName={watchedValues['2']}
                birthDate={watchedValues['3']}
                cpf={watchedValues['4']}
                email={watchedValues['5']}
                hours={watchedValues['6']}
                priceUpdates={watchedValues['7']}
                reimbursement={watchedValues['8']}
                cancel={watchedValues['9']}
                recurrency={watchedValues['10']}
              />
            </div>
          </div>
          <div>
            <Button type='submit' className='w-full mt-4'>{label}</Button>
          </div>
        </div>
      </form>
    </Form>
  );
}

