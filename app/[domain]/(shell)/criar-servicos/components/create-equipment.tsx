import {
  Button,
  Input,
  Label,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  useToast,
} from '@/components/index';
import { ax, } from '@/lib/utils';
import { Equipment, } from '@/utils/constants/types';
import { equipmentGroups, URLS, } from '@/utils/supabase/constants';
import { useState, } from 'react';

type CreatedEquipment = {
  name: string | undefined,
  type: string | undefined,
  quantity: string | undefined,
}

// eslint-disable-next-line no-unused-vars
type AfterCreateEquipment = (equipment: Equipment) => void;

const initialEquipment = {
  name: undefined,
  type: undefined,
  quantity: undefined,
};

const CreateEquipment = ({ afterCreateEquipment, }: { afterCreateEquipment: AfterCreateEquipment }) => {
  const [saveLabel, setSaveLabel,] = useState('Salvar Equipamento');
  const [equipment, setEquipment,] = useState<CreatedEquipment>(initialEquipment);
  const { toast, } = useToast();

  const saveEquipment = async (e) => {
    e.preventDefault();
    if (!equipment || !equipment.name || !equipment.quantity) {
      toast({
        variant: 'destructive',
        title: 'Preencha nome e quantidade.',
      });
    } else {
      setSaveLabel('Salvando Equipamento');
      const response = await ax.post(`${URLS.EQUIPMENTS}/create`, {
        name: equipment.name,
        quantity: parseInt(equipment.quantity),
        type: equipment.type,
      });
      if (response.status === 200) {
        toast({
          title: 'Equipamento salvo com sucesso!',
        });
        setEquipment(initialEquipment);
        setSaveLabel('Salvar');
        afterCreateEquipment(response.data);
      } else {
        setSaveLabel('Salvar Equipamento');
        toast({
          variant: 'destructive',
          title: response.data.message,
        });
      }
    }
  };

  const setSelectedGroup = (group) => {
    setEquipment({ ...equipment, type: group, });
  };

  return (
    <div className="w-full flex flex-col gap-4">
      <div className="flex flex-col gap-2">
        <Label htmlFor="name">
          Nome
        </Label>
        <Input value={equipment.name} onChange={(e) => setEquipment({ ...equipment, name: e.target.value, })} className="col-span-3" />
      </div>
      <div className="flex flex-col gap-2">
        <Label htmlFor="quantity">
          Quantidade
        </Label>
        <Input value={equipment?.quantity} onChange={(e) => setEquipment({ ...equipment, quantity: e.target.value, })} className="col-span-3" />
      </div>
      <div className="flex flex-col gap-2">
        <Label htmlFor="type">
          Grupo
        </Label>
        <Select onValueChange={setSelectedGroup}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder='Seleciona grupo do equipamento' />
            </SelectTrigger>
            <SelectContent>
              {
                Object.keys(equipmentGroups).map(key => (
                  <SelectGroup key={key}>
                    <SelectLabel className='text-base'>{[key,]}</SelectLabel>
                    {
                      equipmentGroups[key].map(group => (
                        <SelectItem
                          key={group}
                          value={group}
                        >
                          {group}
                        </SelectItem>
                      ))
                    }
                  </SelectGroup>
                ))
              }
            </SelectContent>
          </Select>
      </div>
      <Button onClick={saveEquipment}>{saveLabel}</Button>
    </div>
  );
};

export default CreateEquipment;