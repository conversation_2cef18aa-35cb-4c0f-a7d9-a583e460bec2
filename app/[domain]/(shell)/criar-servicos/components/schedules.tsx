'use client';

import { useState, useEffect, } from 'react';
import { Card, CardContent, CardHeader, CardTitle, Input, Label, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from '@/components/index';
import Columns from './columns';
import { getHoursOfSchedule, } from '@/lib/utils';
import { UseFormReturn, } from 'react-hook-form';
import { z, } from 'zod';
import { formSchema, } from '../../servicos/schema';
import { hours } from '@/utils/constants';

function parseTimeInput(input: string): string[] {
  if (!input) return [];
  return input.split(',').map(time => time.trim());
}
const sortAndRemoveDuplicatesByMinutes = (hours: string[]) => {
  const toMinutes = (hourAndMinute: string) => {
    const [hour, minutes,] = hourAndMinute.split(':').map(Number);
    return hour * 60 + minutes;
  };

  const uniqueTimes = new Set(hours);
  return Array.from(uniqueTimes).sort((a, b) => toMinutes(a) - toMinutes(b));
};

const WeekdaySchedule = ({ form, }: { form: UseFormReturn<z.infer<typeof formSchema>> }) => {
  const [columns, setColumns,] = useState<string[]>([]);

  const addTime = (hour: string) => {
    setColumns(prev => [...prev, hour]);
  }

  useEffect(() => {
    let timeSlots;
    const schedules = form.getValues('schedules');
    if (schedules && Object.keys(schedules).length) {
      const input = getHoursOfSchedule(schedules);
      timeSlots = sortAndRemoveDuplicatesByMinutes(parseTimeInput(input));
      setColumns(timeSlots);
    }
  }, []);

  return (
    <Card className='w-full max-w-[95vw] mx-auto'>
      <CardHeader>
        <CardTitle>Horários</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='space-y-4'>
          <div className='flex items-center gap-4'>
            <Label>Aula começa às:</Label>
            <Select onValueChange={addTime}>
              <SelectTrigger className='h-8 w-auto'>
                <SelectValue placeholder='00:00' />
              </SelectTrigger>
              <SelectContent side='top'>
                {hours.map((hour) => (
                  <SelectItem key={hour} value={hour}>
                    {hour}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        {columns.length > 0 && (
          <Columns
            form={form}
            columns={columns}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default  WeekdaySchedule;