'use client';

import { Checkbox, FormControl, FormField, FormItem, FormMessage, } from '@/components/index';
import { daysOfWeek, daysOfWeekStartingMonday, } from '@/utils/constants';
import React from 'react';
import { formSchema, } from '../../servicos/schema';
import { UseFormReturn, } from 'react-hook-form';
import { z, } from 'zod';

type Props = {
  form: UseFormReturn<z.infer<typeof formSchema>>,
  columns: string[],
}

const Columns = ({ form, columns, }: Props) => {
  return (
    <div className="mt-8 overflow-x-auto">
      <div style={{ display: 'grid', gridTemplateColumns: `100px repeat(${columns.length}, 60px)`, gap: '10px', }}>
        <div className="col-start-2 col-span-full grid grid-cols-subgrid">
          {columns.map(slot => (
            <div key={slot} className="text-left text-xs font-medium">
              {slot}
            </div>
          ))}
        </div>
        {daysOfWeekStartingMonday.map(day => (
          <>
            <div key={day} className="sticky left-0 z-10 bg-background flex items-center font-medium">
              {day}
            </div>
            <div className='col-start-2 col-span-full grid grid-cols-subgrid'>
              {columns.map(slot => (
                <FormField
                  key={`${day}-${slot}`}
                  control={form.control}
                  name={`schedules.${daysOfWeek.indexOf(day)}`}
                  render={({ field, }) => (
                    <FormItem className='flex items-center space-y-0'>
                      <FormControl>
                        <Checkbox
                          id={`schedules-${day}-${slot}`}
                          checked={field.value?.includes(slot) ?? false}
                          onCheckedChange={(checked) => {
                            const currentValues = field.value ?? [];
                            const newValues = checked
                              ? [...currentValues, slot,]
                              : currentValues.filter((value) => value !== slot);
                            field.onChange(newValues);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                  }
                />
              ))}
            </div>
          </>
        ))}
      </div>
    </div>
  );
};

export default Columns;