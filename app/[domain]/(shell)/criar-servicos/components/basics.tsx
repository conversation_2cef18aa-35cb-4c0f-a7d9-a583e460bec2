'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Switch,
  Textarea,
} from '@/components/index';
import { FormLabel, } from '@/components/ui/shard/form';
import { formSchema, } from '../../servicos/schema';
import { UseFormReturn, } from 'react-hook-form';
import { z, } from 'zod';
import { Sport, } from '@/utils/constants/types';
import { Dispatch, SetStateAction, useEffect, useState, } from 'react';
import { getAllSchoolSports, } from '@/app/actions';
import { toast, } from '@/components/ui/shard/use-toast';
import { CancelationWindow, } from '@/utils/supabase/constants';

const Basics = ({ form, requireBooking, setRequireBooking, }: { form: UseFormReturn<z.infer<typeof formSchema>>, requireBooking: boolean, setRequireBooking: Dispatch<SetStateAction<boolean>> }) => {
  const [sports, setSports,] = useState<Sport[]>([]);

  useEffect(() => {
    (async () => {
      const response = await getAllSchoolSports();
      if (response.status === 200) {
        setSports(response.data);
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })();
  }, []);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Serviço</CardTitle>
        <CardDescription>Qual serviço você oferece?</CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className="flex items-center space-x-2">
          <Label htmlFor="airplane-mode">Esse serviço precisa ser agendado?</Label>
          <Switch id='isBooked' checked={requireBooking} onCheckedChange={setRequireBooking} />
        </div>
        {requireBooking && (
            <FormField
              control={form.control}
              name='basics.policy'
              render={({ field, }) => (
                <FormItem>
                  <FormLabel>Reembolso por cancelamento</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value} defaultValue={form.getValues('basics.policy')} >
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder='Horas' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {CancelationWindow.map(window => (
                          <SelectItem key={window.hour} value={window.hour}>{window.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        <FormField
          control={form.control}
          name='basics.sportId'
          render={({ field, }) => (
            <FormItem>
              <FormLabel>Esporte</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={form.getValues('basics.sportId')} >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder='Selecionar' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {sports.map(sport => (
                        <SelectItem key={sport.id} value={sport.id}>{sport.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="basics.title"
          render={({ field, }) => (
            <FormItem>
              <FormLabel>Nome</FormLabel>
              <FormControl>
                <Input {...field} placeholder='O que você oferece?' />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="basics.description"
          render={({ field, }) => (
            <FormItem>
              <FormLabel>Descrição</FormLabel>
              <FormControl>
                <Textarea {...field} placeholder='Detalhes sobre a aula.' />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
};

export default Basics;