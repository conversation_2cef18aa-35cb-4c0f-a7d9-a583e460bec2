import React, { useEffect, useState, } from 'react';
import { Equipment, EquipmentCheckbox, } from '@/utils/constants/types';
import { toast, } from '@/components/ui/shard/use-toast';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Checkbox,
  FormControl,
  FormField,
  FormItem,
  Label,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/index';
import CreateEquipment from './create-equipment';
import { URLS, } from '@/utils/supabase/constants';
import { UseFormReturn, } from 'react-hook-form';
import { formSchema, } from '../../servicos/schema';

const Equipments = ({ form, isCreating = false, }: { form: UseFormReturn<z.infer<typeof formSchema>>, isCreating?: boolean }) => {
  const [equipmentList, setEquipmentList,] = useState<EquipmentCheckbox[]>([]);
  const [showCreateEquipment, setShowCreateEquipment,] = useState(false);

  useEffect(() => {
    (async () => {
      const response = await fetch(URLS.EQUIPMENTS);
      if (response.status === 200) {
        const data: Equipment[]  = await response.json();
        const equipmentsWithSelectedField = data.map(equipment => ({ ...equipment, selected: isCreating, }));
        setEquipmentList(equipmentsWithSelectedField);

        if (isCreating) {
          form.setValue('equipments', equipmentsWithSelectedField.map(e => ({ id: e.id, })));
        }
      } else {
        const data: { message: string }  = await response.json();
        toast({
          variant: 'destructive',
          title: data.message,
        });
      }
    })();
  }, [isCreating, form,]);

  const afterCreateEquipment = (newEquipment: Equipment) => {
    const newEquipmentWithSelected = { ...newEquipment, selected: isCreating, };
    setEquipmentList([...equipmentList, newEquipmentWithSelected,]);
    if (isCreating) {
      const currentEquipments = form.getValues('equipments');
      form.setValue('equipments', [...currentEquipments, { id: newEquipment.id, },]);
    }
    setShowCreateEquipment(false);
  };

  const createEquipment = (e) => {
    e.preventDefault();
    setShowCreateEquipment(!showCreateEquipment);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Equipamentos</CardTitle>
        <CardDescription></CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Selecionar</TableHead>
              <TableHead>Quantidade</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {equipmentList.map((item) => (
              <FormField
                key={item.id}
                control={form.control}
                name="equipments"
                render={({ field, }) => (
                  <TableRow className={`w-full ${item.selected ? 'bg-muted' : ''}`}>
                    <TableCell>
                      <FormItem>
                        <FormControl>
                          <>
                            <Checkbox
                              id={item.id}
                              checked={!!field.value.find((f: Equipment) => f.id === item.id)}
                              onCheckedChange={(checked) => {
                                const isChecked = checked === true;
                                if (isChecked) {
                                  field.onChange([...field.value, { id: item.id, },]);
                                } else {
                                  field.onChange(field.value.filter((i: { id: string }) => i.id !== item.id));
                                }
                                setEquipmentList(equipmentList.map(e =>
                                  e.id === item.id ? { ...e, selected: isChecked, } : e
                                ));
                              }}
                            />
                            <Label htmlFor={item.id} className='cursor-pointer ml-3'>{item.name}</Label>
                          </>
                        </FormControl>
                      </FormItem>
                    </TableCell>
                    <TableCell className='pl-10'>
                      <Label>{item.quantity}</Label>
                    </TableCell>
                  </TableRow>
                )}
              />
            ))}
          </TableBody>
        </Table>
      </CardContent>
      <CardFooter className='flex flex-col gap-2'>
        {
          showCreateEquipment && (
            <CreateEquipment afterCreateEquipment={afterCreateEquipment} />
          )
        }
        <Button className='w-full' onClick={createEquipment}>{showCreateEquipment ? 'Esconder' : 'Criar Equipamento'}</Button>
      </CardFooter>
    </Card>
  );
};

export default Equipments;