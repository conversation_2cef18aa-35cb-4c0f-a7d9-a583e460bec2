'use client';

import React, { useEffect, useState, } from 'react';
import { Profile, ProfileCheckbox, } from '@/utils/constants/types';
import { toast, } from '@/components/ui/shard/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Checkbox,
  FormControl,
  FormField,
  FormItem,
  Label,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/index';
import { URLS, } from '@/utils/supabase/constants';

const Teachers = ({ form, }: { form: any }) => {
  const [profileList, setProfileList,] = useState<ProfileCheckbox[]>([]);

  useEffect(() => {
    (async () => {
      const response = await fetch(URLS.USER.TEACHERS);
      if (response.status === 200) {
        const data: Profile[] = await response.json();
        const isSelected = (id: string): boolean => {
          return form.getValues('teachers') && !!form.getValues('teachers').find(teacher => teacher.id === id);
        };
        const profilesWithSelectedField = data.map(profile => ({ ...profile, selected: isSelected(profile.id), }));
        setProfileList(profilesWithSelectedField);
        if (!form.getValues('teachers')) {
          form.setValue('teachers', profilesWithSelectedField.map(e => ({ id: e.id, })));
        }
      } else {
        const data: { message: string } = await response.json();
        toast({
          variant: 'destructive',
          title: data.message,
        });
      }
    })();
  }, [form,]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Professores</CardTitle>
        <CardDescription></CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Selecionar</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {profileList.map((profile) => (
              <FormField
                key={profile.id}
                control={form.control}
                name='teachers'
                render={({ field, }) => {
                  // console.log('🚀 ~ Teachers ~ l:', field);
                  return (
                    <TableRow className={`w-full ${profile.selected ? 'bg-muted' : ''}`}>
                      <TableCell>
                        <FormItem>
                          <FormControl>
                            <>
                              <Checkbox
                                id={profile.id}
                                checked={!!field.value.find((f: Profile) => f.id === profile.id)}
                                onCheckedChange={(checked) => {
                                  const isChecked = checked === true;
                                  if (isChecked) {
                                    field.onChange([...field.value, { id: profile.id, },]);
                                  } else {
                                    field.onChange(field.value.filter((i: { id: string }) => i.id !== profile.id));
                                  }
                                  setProfileList(profileList.map(e =>
                                    e.id === profile.id ? { ...e, selected: isChecked, } : e
                                  ));
                                }}
                              />
                              <Label htmlFor={profile.id} className='cursor-pointer ml-3'>{profile.name}</Label>
                            </>
                          </FormControl>
                        </FormItem>
                      </TableCell>
                    </TableRow>
                  );
                }}
              />
            ))}
          </TableBody>
          <FormField
              control={form.control}
              name="randomTeacher"
              render={({ field, }) => (
                <div className="flex items-center space-x-2 mt-4 ml-4">
                  <Checkbox
                    id='randomTeacher'
                    checked={!field.value}
                    onCheckedChange={(e) => field.onChange(!e) }
                  />
                  <Label htmlFor='randomTeacher' className='cursor-pointer ml-3'>
                    Estudante pode escolher o professor no agendamento
                  </Label>
                </div>
              )}
            />
        </Table>
      </CardContent>
    </Card>
  );
};

export default Teachers;