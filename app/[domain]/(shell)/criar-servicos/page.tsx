'use client';

import { useRouter, } from 'next/navigation';
import { useForm, } from 'react-hook-form';
import { z, } from 'zod';
import { zodResolver, } from '@hookform/resolvers/zod';
import Equipments from './components/equipments';
import { URLS, } from '@/utils/supabase/constants';
import Schedules from './components/schedules';
import Basics from './components/basics';
import { toast, } from '@/components/ui/shard/use-toast';
import { Button, Form, } from '@/components/index';
import { useState, } from 'react';
import { ax, } from '@/lib/utils';
import Teachers from './components/teachers';
import { formSchema, } from '../servicos/schema';

const Service = () => {
  const [saveLabel, setSaveLabel,] = useState('Salvar');
  const [requireBooking, setRequireBooking,] = useState(false);
  const route = useRouter();

  const form: any = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      equipments: [],
      teachers: [],
      schedules: {},
      randomTeacher: false,
    },
  });

  const saveService = async (formData: z.infer<typeof formSchema>) => {
    setSaveLabel('Salvando');
    if (Object.keys(formData.schedules).length === 0) {
      toast({
        variant: 'destructive',
        title: 'Horário é obrigatório.',
      });
      setSaveLabel('Salvar');
      return;
    }
    const response = await ax.post(URLS.SERVICE_CREATE, {
      formData,
    });
    if (response.status === 200) {
      setSaveLabel('Salvo');
      toast({
        title: 'Serviço criado com sucesso!',
      });
      route.push('/servicos');
    } else {
      setSaveLabel('Salvar');
      toast({
        variant: 'destructive',
        title: response.data.message,
      });
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(saveService)} className="space-y-4 w-full mt-4">
          <Basics form={form} requireBooking={requireBooking} setRequireBooking={setRequireBooking} />
          <Schedules form={form} />
          <Teachers form={form} />
          <Equipments form={form} isCreating={true} />
          <Button type="submit" className='w-full'>{saveLabel}</Button>
        </form>
      </Form>
    </div>
  );
};

export default Service;
