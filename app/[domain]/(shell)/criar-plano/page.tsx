'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Switch,
} from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import { ChangeEvent, useEffect, useState, } from 'react';
import { useForm, } from 'react-hook-form';
import { z, } from 'zod';
import { zodResolver, } from '@hookform/resolvers/zod';
import { savePlan, } from './actions';
import { useRouter, } from 'next/navigation';
import { Service, } from '@/utils/constants/types';
import { getAllSchoolServices, } from '@/app/actions';

const frequencyOptions = {
  weekly: {
    frequency: 7,
    frequencyType: 'days',
  },
  biweekly: {
    frequency: 15,
    frequencyType: 'days',
  },
  monthly: {
    frequency: 1,
    frequencyType: 'months',
  },
  bimonthly: {
    frequency: 2,
    frequencyType: 'months',
  },
  trimonthly: {
    frequency: 3,
    frequencyType: 'months',
  },
  semianual: {
    frequency: 6,
    frequencyType: 'months',
  },
  anual: {
    frequency: 12,
    frequencyType: 'months',
  },
};

const requiredMessage = 'Campo obrigatório.';
const formSchema = z.object({
  name: z.string({ required_error: requiredMessage, })
    .min(5, { message: 'Nome muito curto, mínimo 5 letras.', })
    .max(40, { message: 'Nome muito longo, máximo 40 letras.', }),
  price: z.string({ required_error: requiredMessage, })
    .min(3, { message: 'Preço não permitido.', })
    .max(11, { message: 'Preço não permitido.', }),
  interest: z.string().optional(),
  fine: z.string().optional(),
  fineType: z.string().optional(),
  frequency: z.string({ required_error: requiredMessage, }),
  repetitions: z.string().optional(),
  billingDay: z.string().optional(),
  display: z.boolean().default(true),
  services: z.array(z.object({
    id: z.string(),
  })),
});

type PlanFormData = z.infer<typeof formSchema>;

const CreatePlanForm = () => {
  const [services, setServices,] = useState<Service[] | null>(null);
  const [isLoading, setIsLoading,] = useState(false);
  const router = useRouter();

  const form = useForm<PlanFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      display: true,
      services: [],
    },
  });

  const watchFrequency = form.watch('frequency');
  const watchFineType = form.watch('fineType');

  const onSubmit = async (formData: z.infer<typeof formSchema>) => {
    let finalFormData = {
      ...formData,
      frequency: frequencyOptions[formData.frequency].frequency as string,
      frequencyType: frequencyOptions[formData.frequency].frequencyType as string,
      repetitions: formData.repetitions === 'undetermined' ? null : formData.repetitions,
    };

    setIsLoading(true);
    const response = await savePlan(finalFormData);
    if (response.status === 201) {
      toast({
        description: 'Plano criado com sucesso.',
      });
      router.push('/planos');
    } else {
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
    setIsLoading(false);
  };

  const formatPrice = (value: string) => {
    const digits = value.replace(/\D/g, '');

    const formatter = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    const numericValue = parseInt(digits, 10) / 100;
    return formatter.format(numericValue);
  };

  const formatPercentage = (input: string) => {
    if (input === '') {
      return '0,00%';
    }

    try {
      const num = parseInt(input, 10);
      if (isNaN(num)) {
        return '0,00%';
      }

      const percentage = num / 100; // Divide by 100

      return percentage.toLocaleString('pt-BR', { // Use de-DE locale for comma as decimal separator
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }) + '%';
    } catch (error) {
      return '0,00%';
    }
  };


  const handleValueChange = (event: ChangeEvent<HTMLInputElement>, field: 'price' | 'fine') => {
    const rawValue = event.target.value.replace(/\D/g, '');
    const formattedValue = formatPrice(rawValue);
    form.setValue(field, formattedValue, { shouldValidate: true, });
  };

  const handlePercentageChange = (event: ChangeEvent<HTMLInputElement>, field: 'interest' | 'fine') => {
    const rawValue = event.target.value.replace(/\D/g, '');
    const formattedValue = formatPercentage(rawValue);
    form.setValue(field, formattedValue, { shouldValidate: true, });
  };

  useEffect(() => {
    (async () => {
      const response = await getAllSchoolServices();
      if (response.status === 200) {
        setServices(response.data);
      } else {
        toast({
          variant: 'destructive',
          description: response.message,
        });
      }
    })();
  }, []);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8 w-full">
        <Card>
          <CardHeader>
            <CardTitle>Serviços</CardTitle>
            <CardDescription>Com esse plano os alunos vão pode user os serviços selecionados.</CardDescription>
            <CardDescription></CardDescription>
          </CardHeader>
          <CardContent className='flex flex-col gap-2'>
            {services && services.map((service) => (
              <FormField
                key={service.id}
                control={form.control}
                name='services'
                render={({ field, }) => (
                  <FormItem className='w-48 flex items-center justify-between'>
                    <FormLabel className='mr-4'>{service.title}</FormLabel>
                    <FormControl>
                      <Switch
                        checked={!!field.value.find((f: Service) => f.id === service.id)}
                        onCheckedChange={(checked) => {
                          const isChecked = checked === true;
                          if (isChecked) {
                            field.onChange([...field.value, { id: service.id, },]);
                          } else {
                            field.onChange(field.value.filter((i: { id: string }) => i.id !== service.id));
                          }
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            ))}
          </CardContent>
        </Card>
        <FormField
          control={form.control}
          name='name'
          render={({ field, }) => (
            <FormItem>
              <FormLabel>Nome</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Algum texto que mostre ao usuário o que ele está assinando."
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='price'
          render={({ field, }) => (
            <FormItem>
              <FormLabel htmlFor="price">Preço</FormLabel>
              <FormControl>
                <Input
                  id="price"
                  {...field}
                  onChange={(e) => handleValueChange(e, 'price')}
                  placeholder="R$ 0,00"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='interest'
          render={({ field, }) => (
            <FormItem>
              <FormLabel htmlFor='interest'>Juros por atraso</FormLabel>
              <FormControl>
                <Input
                  id='interest'
                  {...field}
                  onChange={(e) => handlePercentageChange(e, 'interest')}
                  placeholder='0,00%'
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='fineType'
          render={({ field, }) => (
            <FormItem>
              <FormLabel>
                Tipo de multa por atraso de pagamento
              </FormLabel>
              <Select onValueChange={field.onChange} >
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder='Selecionar' />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value='FIXED'>Valor fixo</SelectItem>
                  <SelectItem value='PERCENTAGE'>Porcentagem</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        {
          watchFineType === 'FIXED' && (
            <FormField
              control={form.control}
              name='fine'
              render={({ field, }) => (
                <FormItem>
                  <FormLabel htmlFor='fine'>Multa por atraso</FormLabel>
                  <FormControl>
                    <Input
                      id='fine'
                      {...field}
                      onChange={(e) => handleValueChange(e, 'fine')}
                      placeholder='R$ 0,00'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )
        }
        {
          watchFineType === 'PERCENTAGE' && (
            <FormField
              control={form.control}
              name='fine'
              render={({ field, }) => (
                <FormItem>
                  <FormLabel htmlFor='fine'>Multa por atraso em porcentagem</FormLabel>
                  <FormControl>
                    <Input
                      id='fine'
                      {...field}
                      onChange={(e) => handlePercentageChange(e, 'fine')}
                      placeholder='0,00%'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )
        }
        <div className='grid grid-cols-1 sm:grid-cols-3 gap-6'>
          <FormField
            control={form.control}
            name='frequency'
            render={({ field, }) => (
              <FormItem>
                <FormLabel className=''>
                  Cobrança
                </FormLabel>
                <Select onValueChange={field.onChange} >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder='Período' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value='weekly'>Semanal</SelectItem>
                    <SelectItem value='biweekly'>Quinzenal</SelectItem>
                    <SelectItem value='monthly'>Mensal</SelectItem>
                    <SelectItem value='bimonthly'>Bimestral</SelectItem>
                    <SelectItem value='trimonthly'>Trimestral</SelectItem>
                    <SelectItem value='semianual'>Semestral</SelectItem>
                    <SelectItem value='anual'>Anual</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          {watchFrequency === 'monthly'  && (
            <FormField
              control={form.control}
              name='billingDay'
              render={({ field, }) => (
                <FormItem>
                  <FormLabel className=''>
                    Dia da cobrança
                  </FormLabel>
                  <Select onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder='Selecionar' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Array.from({ length: 28, }, (_, i) => i + 1).map(m => (
                        <SelectItem key={m} value={m.toString()}>
                          {m}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          {watchFrequency === 'monthly'  && (
            <FormField
              control={form.control}
              name='repetitions'
              render={({ field, }) => (
                <FormItem>
                  <FormLabel className=''>
                    Repetições
                  </FormLabel>
                  <Select onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder='Quantas vezes vai ser cobrado?' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='undetermined'>
                        Indeterminado
                      </SelectItem>
                      {Array.from({ length: 12, }, (_, i) => i + 1).map(m => (
                        <SelectItem key={m} value={m.toString()}>
                          {m}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>
        <FormField
          control={form.control}
          name='display'
          render={({ field, }) => (
            <FormItem className='flex items-center space-y-0'>
              <FormLabel htmlFor='display' className='cursor-pointer mr-3'>
                Comprar apenas pelo link
              </FormLabel>
              <FormControl>
                <Switch
                  id='display'
                  checked={!field.value}
                  onCheckedChange={e => field.onChange(!e)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full">
          {isLoading ? 'Criando...' : 'Criar plano'}
        </Button>
      </form>
    </Form>
  );
};

export default CreatePlanForm;