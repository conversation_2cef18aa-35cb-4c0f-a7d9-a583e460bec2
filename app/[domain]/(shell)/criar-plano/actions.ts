'use server';

import { Plan, PlanService, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestMaybeSingleResponse, PostgrestResponse, } from '@supabase/supabase-js';
import { addMonths, } from 'date-fns';

const parseBrazilianReal = (realValue: string) => {
  const cleanValue = realValue.replace(/[R$.,\s%]/g, '');
  return cleanValue.replace(/(\d{2})$/, '.$1');
};

type FormData = {
  name: string,
  price: string,
  interest?: string,
  fine?: string,
  fineType?: 'FIXED' | 'PERCENTAGE',
  billingDay: number,
  repetitions?: string,
  frequency: string,
  frequencyType: 'months' | 'days',
  display: boolean,
  services: { id: string }[]
}

type SupabasePlan = Exclude<Plan, 'createdAt' | 'id' | 'active'>

export const savePlan = async (formData: FormData,) => {
  const supabase = createClient();

  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user || user.user_metadata.role !== 'admin') {
    return { message: 'Sem permissão', status: 401, };
  }

  const planSupabaseFormat: Exclude<Plan, 'createdAt' | 'id' | 'active'> = {
    billingDay: formData.billingDay,
    name: formData.name,
    schoolId: user.user_metadata.schoolId,
    price: parseFloat(parseBrazilianReal(formData.price)),
    expires: formData.frequencyType === 'months' && formData.repetitions ? addMonths(new Date(), parseInt(formData.repetitions)) : null,
    interest: formData.interest && parseFloat(parseBrazilianReal(formData.interest)),
    fine: formData.fine && parseFloat(parseBrazilianReal(formData.fine)),
    fineType: formData.fineType,
    frequency: parseInt(formData.frequency),
    frequencyType: formData.frequencyType,
    display: formData.display,
  };
  console.log('🚀 ~ savePlan ~ planSupabaseFormat:', planSupabaseFormat);
  const supabasePlan = await savePlanSupabase({ plan: planSupabaseFormat, services: formData.services, });
  if (supabasePlan.status === 201) {
    return supabasePlan;
  }
  return { message: 'Erro ao criar plano.', status: 400, };
};


const savePlanSupabase = async ({ plan, services, }: { plan: SupabasePlan, services: { id: string }[] }) => {
  const supabase = createClientAdmin();
  const { data, }: PostgrestMaybeSingleResponse<Plan>  = await supabase
    .from('plan')
    .insert(plan)
    .select()
    .maybeSingle();

  if (!data) return { message: 'Erro ao criar plano.', status: 400, };

  const formattedPlanServices = services.map(service => ({
    serviceId: service.id,
    planId: data.id,
  }));
  const { data: planService, }: PostgrestResponse<PlanService> = await supabase
    .from('service_plan')
    .insert(formattedPlanServices)
    .select();

  if (!planService || !planService.length) {
    await supabase
      .from('plan')
      .delete()
      .match({ id: data.id, });
    return { message: 'Erro ao criar plano.', status: 400, };
  }
  return { data, status: 201, };
};
