'use server';

import { Card, CardContent, CardDescription, CardHeader, CardTitle, } from '@/components/ui/shard/card';
import { createClientAdmin, } from '@/utils/supabase/server';
import { Badge, } from '@/components/ui/shard/badge';
import { redirect, } from 'next/navigation';
import { Button, NothingFound, } from '@/components/index';
import { Axe, CalendarIcon, ClockIcon, Plus, UsersIcon, } from 'lucide-react';
import { daysOfWeek, } from '@/utils/constants';
import Link from 'next/link';
import { groupedHours, } from '@/lib/utils';
import { userInfo, } from '@/supabase/verifications/user-info';
import { User, } from '@supabase/supabase-js';
import { format, } from 'date-fns/format';


const Services = async  () => {
  const user: User | null = await userInfo();
  if (user && user.user_metadata.role !== 'admin') redirect('/login');
  if (!user) redirect('/login');

  const supabase = createClientAdmin();
  let { data: services, } = await supabase
    .from('service')
    .select()
    .order('createdAt', { ascending: false, })
    .match({ schoolId: user.user_metadata.schoolId, });


  if (!services) return (
    <>
      <NothingFound label='Erro ao buscar serviços registrados.' />
      <div className="fixed bottom-6 right-6 z-50">
        <Button asChild size="lg" className="rounded-full shadow-lg h-14 w-14 p-0 md:w-auto md:px-4 md:h-12">
          <Link href='/criar-servicos'>
            <Plus className="h-6 w-6" />
          </Link>
        </Button>
      </div>
    </>
  );

  if (services.length === 0) return (
    <>
      <NothingFound label='Sem serviços registrados.' />
      <div className="fixed bottom-6 right-6 z-50">
        <Button asChild size="lg" className="rounded-full shadow-lg h-14 w-14 p-0 md:w-auto md:px-4 md:h-12">
          <Link href='/criar-servicos'>
            <Plus className="h-6 w-6" />
          </Link>
        </Button>
      </div>
    </>
  );

  const equipmentPromises = services.map(async service => {
    const { data: serviceEquipment, } = await supabase.from('service_equipment').select('equipment(*)').match({ serviceId: service.id, active: true, });
    if (serviceEquipment) {
      service.equipments = serviceEquipment.map(service => {
        return service.equipment;
      });
    }
    return service;
  });

  services = await Promise.all(equipmentPromises);

  const schedulePromises = services.map(async service => {
    const { data: schedules, } = await supabase.from('schedule').select().match({ serviceId: service.id, active: true, });
    if (schedules) {
      service.schedules = groupedHours(schedules);
    }
    return service;
  });
  services = await Promise.all(schedulePromises);

  return (
    <div className="container mx-auto p-4">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {services.map((service) => (
          <Link key={service.id}  href={`/servicos/${service.id}`}>
            <Card key={service.id} className="overflow-hidden">
              <CardHeader className="pb-3">
                <CardTitle>{service.title}</CardTitle>
                <CardDescription className="flex items-center mt-2">
                  <CalendarIcon className="mr-2 h-4 w-4 opacity-70" />
                  Criado em {format(new Date(service.createdAt), 'dd/MM/yyyy')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">{service.description}</p>
                <div className="flex items-center justify-between mb-4">
                  <Badge variant="secondary" className="flex items-center">
                    <UsersIcon className="mr-1 h-3 w-3" />
                    {service.limit ? `Limite: ${service.limit}` : 'Sem limite'}
                  </Badge>
                </div>
                <div className="mb-4">
                  <h4 className="text-sm font-semibold mb-2">Equipmentos:</h4>
                  <ul className="text-xs text-muted-foreground list-disc list-inside">
                    {service.equipments && service.equipments.length && service.equipments.map((item, index) => (
                      <li key={index} className="flex items-center">
                        <Axe className="mr-2 h-3 w-3" />
                        {item.name}
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="mb-4">
                  <h4 className="text-sm font-semibold mb-2">Horários:</h4>
                  <ul className="text-xs text-muted-foreground">
                    {service.schedules && service.schedules.map((slot, index) => (
                      <li key={index} className="flex items-center mb-1">
                        <ClockIcon className="mr-2 h-3 w-3" />
                        {daysOfWeek[slot.number]}: {slot.hours.join(', ')}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
      <div className="fixed bottom-6 right-6 z-50">
        <Button asChild size="lg" className="rounded-full shadow-lg h-14 w-14 p-0 md:w-auto md:px-4 md:h-12">
          <Link href='/criar-servicos'>
            <Plus className="h-6 w-6" />
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default Services;