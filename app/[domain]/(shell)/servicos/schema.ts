import { requiredMessage, } from '@/utils/supabase/constants';
import { z, } from 'zod';

export const formSchema = z.object({
  basics: z.object({
    title: z.string({ required_error: requiredMessage, }).min(10, { message: '<PERSON>e ter no mínimo 10 letras.', }).max(40, { message: 'Deve ter no máximo 40 letras.', }),
    description: z.string().max(255).optional(),
    sportId: z.string({ required_error: requiredMessage, }),
    policy: z.string().nullable().optional(),
  }),
  schedules: z.record(z.string(), z.array(z.string()).optional()),
  equipments: z.array(z.object({
    id: z.string(),
  })),
  teachers: z.array(
    z.object({
      id: z.string({ required_error: requiredMessage, }),
    })
  ),
  randomTeacher: z.boolean(),
});