'use client';

import { useRouter, } from 'next/navigation';
import { useForm, } from 'react-hook-form';
import { z, } from 'zod';
import { zodResolver, } from '@hookform/resolvers/zod';
import { toast, } from '@/components/ui/shard/use-toast';
import { Button, Form, } from '@/components/index';
import { useEffect, useState, } from 'react';
import Basics from '../../criar-servicos/components/basics';
import Schedules from '../../criar-servicos/components/schedules';
import Equipments from '../../criar-servicos/components/equipments';
import Loading from '../../loading';
import { URLS, } from '@/utils/supabase/constants';
import { formSchema, } from '../schema';
import Teachers from '../../criar-servicos/components/teachers';

export type ServiceFormSchema = z.infer<typeof formSchema>

const Service = ({ params: { id, },  }: { params: { id: string },  }) => {
  const [saveLabel, setSaveLabel,] = useState('Salvar');
  const [loading, setLoading,] = useState(true);
  const [requireBooking, setRequireBooking,] = useState(false);
  const route = useRouter();

  const form = useForm<ServiceFormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      equipments: [],
      teachers: [],
      schedules: {},
    },
  });

  const saveService = async (formData: ServiceFormSchema) => {
    if (Object.keys(formData.schedules).length === 0) {
      toast({
        variant: 'destructive',
        title: 'Horário é obrigatório.',
      });
      setSaveLabel('Salvar');
      return;
    }
    setSaveLabel('Salvando');
    const response = await fetch(URLS.SERVICE_UPDATE, {
      method: 'PATCH',
      body: JSON.stringify({
        formData,
        serviceId: id,
      }),
    });
    if (response.status === 200) {
      setSaveLabel('Salvo');
      toast({
        title: 'Serviço atualizado com sucesso!',
      });
      route.push('/servicos');
    } else {
      const data = await response.json();
      setSaveLabel('Salvar');
      toast({
        variant: 'destructive',
        title: data.message,
      });
    }
  };

  useEffect(() => {
    (async () => {
      const response = await fetch(`${URLS.SERVICE}?serviceId=${id}`);
      if (response.status === 200) {
        const service = await response.json();
        form.setValue('basics', {
          title: service.title,
          description: service.description || '',
          limit: service.limit,
          sportId: service.sportId,
          ...(service.policy !== null && { policy: service.policy.toString(), }),
        });
        form.setValue('schedules', service.schedules);
        form.setValue('teachers', service.teachers);
        form.setValue('equipments', service.equipments);
        form.setValue('randomTeacher', service.randomTeacher);
        setRequireBooking(service.policy !== null && service.policy !== undefined);
      } else {
        const data = await response.json();
        toast({
          variant: 'destructive',
          title: data.message,
        });
      }
      setLoading(false);
    })();
  }, []);

  useEffect(() => {
    if (!requireBooking) {
      if (form.getValues('basics.policy') !== null) {
        form.setValue('basics.policy', null);
      }
    }
  }, [requireBooking,]);

  return (
    <div className="container mx-auto p-4 space-y-8">
      {loading && <Loading />}
      {!loading && (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(saveService)} className="space-y-4 w-full mt-4">
            <Basics form={form} requireBooking={requireBooking} setRequireBooking={setRequireBooking} />
            <Schedules form={form} />
            <Teachers form={form} />
            <Equipments form={form} isCreating={false} />
            <Button type="submit" className='w-full'>{saveLabel}</Button>
          </form>
        </Form>
      )}
    </div>
  );
};

export default Service;