'use client';

import { useEffect, useState, } from 'react';
import { format, } from 'date-fns';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/index';
import { Pack, PackProfile, Profile, Schedule, Service, TeacherSchedule, } from '@/utils/constants/types';
import { useToast, } from '@/components/ui/shard/use-toast';
import { groupData, } from '@/lib/utils';
import { useRouter, } from 'next/navigation';
import DaySelector from '@/components/ui/day-selector';
import TeacherSelect from '@/components/ui/teacher-selector';
import HoursSelector from '@/components/ui/hours-selector';
import EquipmentSelector from '@/components/ui/equipment-selector';
import PackSelector from './pack-selector';
import ServiceSelector from './service-selector';
import { book, getAvailableSchedules, } from '../actions';

const Book = () => {
  const [selectedDate, setSelectedDate,] = useState<Date | undefined>();
  const [selectedEquipments, setSelectedEquipments,] = useState<{ id: string, key?: string}[]>([]);
  const [packProfile, setPackProfile,] = useState<PackProfile & { pack: Pack } | null>();
  const [service, setService,] = useState<Service | null>();
  const [teacherSchedules, setTeacherSchedules,] = useState<(TeacherSchedule & { schedule: Schedule, profile: Profile })[]>([]);
  const [selectedTeacherScheduleId, setSelectedTeacherScheduleId,] = useState<string | null>(null);
  const [selectedTeacherId, setSelectedTeacherId,] = useState<string | null>();
  const [selectedHour, setSelectedHour,] = useState<string | null>(null);
  const [hours, setHours,] = useState<string[]>();
  const [availableTeachers, setAvailableTeachers,] = useState<Profile[] | null>(null);
  const [selectedScheduleId, setSelectedScheduleId,] = useState<string | null>();
  const [saveLabel, setSaveLabel,] = useState('Agendar');
  const { toast, } = useToast();
  const router = useRouter();

  const selectEquipment = ({ equipmentId, checked, key, }:{ equipmentId: string, checked?: string | boolean, key: string }) => {
    if (checked === 'indeterminate') return;

    if (typeof checked === 'boolean') {
      setSelectedEquipments(prev =>
        checked ? [...prev, { id: equipmentId, },] : prev.filter(equipment => equipment.id !== equipmentId)
      );
    } else { // Handle select changes
      setSelectedEquipments(prev => {
        return prev.filter(item => item.key !== key).concat({ id: equipmentId, key, }); // Keep only one per key
      });
    }
  };

  const bookService = async () => {
    if (!selectedScheduleId || !packProfile || !service || !selectedDate || !selectedTeacherId || !selectedTeacherScheduleId) return null;
    setSaveLabel('Agendando');
    const response = await book({
      day: format(selectedDate, 'yyyy-MM-dd'),
      equipments: selectedEquipments,
      packProfileId: packProfile.id,
      serviceId: service.id,
      status: 'approved',
      teacherId: selectedTeacherId,
      teacherScheduleId: selectedTeacherScheduleId,
      scheduleId: selectedScheduleId,
    });
    if (response.status === 200) {
      toast({
        title: 'Agendamento concluído.',
      });
      router.push('/meus-agendamentos');
    } else {
      setSaveLabel('Agendar');
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
  };

  useEffect(() => {
    if (!service || !selectedDate) return;
    (async () => {
      const response = await getAvailableSchedules({ selectedDate, serviceId: service.id, });
      if (response.status !== 200) {
        toast({
          variant: 'destructive',
          title: response.message,
        });
        return;
      }
      console.log('🚀 ~ response:', response);
      setTeacherSchedules(response.data);
      const groupedData = groupData(response.data);
      if (!groupedData.schedules || groupedData.schedules.length === 0) {
        toast({
          variant: 'destructive',
          title: 'Nenhum horário disponível.',
        });
        return;
      }
      setHours(groupedData.schedules.map(schedule => schedule.hour));
    })();
  }, [selectedDate, service,]);

  useEffect(() => {
    if (!selectedHour) return;
    const teachersByHours = teacherSchedules
      .filter(teacherSchedule => teacherSchedule.schedule.hour === selectedHour)
      .map(teacherSchedule => teacherSchedule.profile);

    if (service?.randomTeacher) {
      setSelectedTeacherId(teachersByHours[Math.floor(Math.random() * teachersByHours.length)].id);
    } else {
      setAvailableTeachers(teachersByHours);
    }
  }, [selectedHour,]);

  useEffect(() => {
    if (!selectedTeacherId) return;
    const teacherSchedule = teacherSchedules.find(teacherSchedule => teacherSchedule.profile.id === selectedTeacherId && teacherSchedule.schedule.hour === selectedHour);
    if (!teacherSchedule) return;
    setSelectedTeacherScheduleId(teacherSchedule.id);
    setSelectedScheduleId(teacherSchedule.schedule.id);
  }, [selectedTeacherId,]);

  return (
    <Card className='w-full max-w-2xl mx-auto'>
      <CardHeader>
        <CardTitle>Agendar aula</CardTitle>
        <CardDescription>Escolha o serviço, equipamento e horário.</CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <PackSelector onSelect={setPackProfile} />
        {packProfile && <ServiceSelector packId={packProfile.pack.id} onSelect={setService} /> }
        <div className='grid sm:grid-cols-2 gap-4 mt-4'>
          {service && <DaySelector onSelect={setSelectedDate} />}
          {hours && <HoursSelector hours={hours} onSelect={setSelectedHour} />}
        </div>
        {availableTeachers && <TeacherSelect teachers={availableTeachers} onSelect={setSelectedTeacherId} />}
        {selectedScheduleId && service  && <EquipmentSelector
          selectedEquipments={selectedEquipments}
          serviceId={service.id}
          selectedScheduleId={selectedScheduleId}
          selectedDate={selectedDate}
          onSelect={selectEquipment}
        /> }
      </CardContent>
      <CardFooter>
        <Button onClick={bookService} className='w-full' disabled={!selectedHour}>{saveLabel}</Button>
      </CardFooter>
    </Card>
  );
};

export default Book;