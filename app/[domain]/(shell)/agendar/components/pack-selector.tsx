'use client';

import { useEffect, useState, } from 'react';
import {
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/index';
import { Pack, PackProfile, } from '@/utils/constants/types';
import { useToast, } from '@/components/ui/shard/use-toast';
import { getProfilePacks, } from '../actions';
import Loading from '../loading';

const PackSelector = ({ onSelect, }) => {
  const [packsProfile, setPacksProfile,] = useState<(PackProfile & { pack: Pack })[]>([]);
  const { toast, } = useToast();

  useEffect(() => {
    (async () => {
      const response = await getProfilePacks();
      if (response.status === 200) {
        console.log('🚀 ~ response:', response);
        setPacksProfile(response.data);
      } else {
        toast({
          variant: 'destructive',
          description: response.message,
        });
      }
    })();
  }, []);

  if (!packsProfile) return <Loading />;

  return (
    <div className="space-y-2">
      <Label htmlFor="service">Pacote</Label>
      <Select onValueChange={(e) => onSelect(packsProfile.find(packProfile => packProfile.id === e ))} required>
        <SelectTrigger id="service">
          <SelectValue placeholder="Selecione um pacote" />
        </SelectTrigger>
        <SelectContent>
          {packsProfile && packsProfile.map((packProfile,) => (
            <SelectItem
              key={packProfile.id}
              value={packProfile.id}
              className="cursor-pointer">
              {packProfile.pack.name}
            </SelectItem>)
          )}
        </SelectContent>
      </Select>
    </div>
  );
};

export default PackSelector;