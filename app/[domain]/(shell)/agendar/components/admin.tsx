'use client';

import { useEffect, useState, } from 'react';
import { format, } from 'date-fns';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/index';
import { Lead, Profile, Schedule, Service, TeacherSchedule, } from '@/utils/constants/types';
import { useToast, } from '@/components/ui/shard/use-toast';
import { useRouter, } from 'next/navigation';
import DaySelector from '@/components/ui/day-selector';
import TeacherSelect from '@/components/ui/teacher-selector';
import HoursSelector from '@/components/ui/hours-selector';
import EquipmentSelector from '@/components/ui/equipment-selector';
import ServiceSelector from './service-selector';
import SearchUser from '../../presenca/search-user';
import { bookByAdmin, getAvailableSchedules, getUserPackProfile, } from '../actions';
import { groupData, } from '@/lib/utils';
import { Check, X, } from 'lucide-react';

const Book = () => {
  const [selectedDate, setSelectedDate,] = useState<Date | undefined>();
  const [selectedEquipments, setSelectedEquipments,] = useState<{ id: string, key?: string }[]>([]);
  const [service, setService,] = useState<Service | null>();
  const [selectedScheduleId, setSelectedScheduleId,] = useState<string | null>(null);
  const [selectedHour, setSelectedHour,] = useState<string | null>(null);
  const [saveLabel, setSaveLabel,] = useState('Agendar');
  const [hours, setHours,] = useState<string[]>();
  const [teacherSchedules, setTeacherSchedules,] = useState<(TeacherSchedule & { schedule: Schedule, profile: Profile })[]>([]);
  const [user, setUser,] = useState<Profile | Lead | null>(null);
  const [selectedTeacherId, setSelectedTeacherId,] = useState<string | null>();
  const [availableTeachers, setAvailableTeachers,] = useState<Profile[] | null>(null);
  const [selectedTeacherScheduleId, setSelectedTeacherScheduleId,] = useState<string | null>(null);
  const [showDialog, setShowDialog,] = useState(false);
  const [confirmLabel, setConfirmLabel,] = useState('Agendar');
  const { toast, } = useToast();
  const router = useRouter();

  const selectEquipment = ({ equipmentId, checked, key, }:{ equipmentId: string, checked?: string | boolean, key: string }) => {
    if (checked === 'indeterminate') return;

    if (typeof checked === 'boolean') {
      setSelectedEquipments(prev =>
        checked ? [...prev, { id: equipmentId, },] : prev.filter(equipment => equipment.id !== equipmentId)
      );
    } else { // Handle select changes
      setSelectedEquipments(prev => {
        return prev.filter(item => item.key !== key).concat({ id: equipmentId, key, }); // Keep only one per key
      });
    }
  };

  const bookService = async (ignorePackProfile?: boolean) => {
    if (!service || !selectedScheduleId || !user || !selectedDate || !selectedScheduleId || !selectedTeacherId || !selectedTeacherScheduleId) {
      toast({
        variant: 'destructive',
        title: 'Faltam informações.',
      });
      setSaveLabel('Agendar');
      return;
    }

    const response = await bookByAdmin({
      ...(user.type === 'student' ? { studentId: user.id, }: { leadId: user.id, }),
      day: format(selectedDate, 'yyyy-MM-dd'),
      equipments: selectedEquipments,
      serviceId: service.id,
      status: 'approved',
      teacherId: selectedTeacherId,
      teacherScheduleId: selectedTeacherScheduleId,
      scheduleId: selectedScheduleId,
      ignorePackProfile,
    });
    if (response.status === 200) {
      toast({
        title: 'Agendamento concluído.',
      });
      router.push('/agendamentos');
    } else {
      setSaveLabel('Agendar');
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
  };

  const onBookHandler = async () => {
    setSaveLabel('Agendando');
    console.log('🚀 ~ onBookHandle ~ user:', user);
    if (user?.type === 'student')  {
      const response = await getUserPackProfile({ userId: user.id, });
      if (response.status === 200) {
        await bookService();
      } else {
        setShowDialog(true);
      }
    } else {
      await bookService();
    }
  };

  const confirmNoPackBooking = async () => {
    setConfirmLabel('Agendando');
    await bookService(true);
    setShowDialog(false);
  };

  useEffect(() => {
    if (!service || !selectedDate) return;
    (async () => {
      const response = await getAvailableSchedules({ selectedDate, serviceId: service.id, });
      if (response.status !== 200) {
        toast({
          variant: 'destructive',
          title: response.message,
        });
        return;
      }
      setTeacherSchedules(response.data);
      const groupedData = groupData(response.data);
      console.log('🚀 ~ groupedData:', groupedData);
      if (!groupedData.schedules || groupedData.schedules.length === 0) {
        toast({
          variant: 'destructive',
          title: 'Nenhum horário disponível.',
        });
        return;
      }
      setHours(groupedData.schedules.map(schedule => schedule.hour));
    })();
  }, [selectedDate, service,]);

  useEffect(() => {
    if (!selectedHour) return;
    const teachersByHours = teacherSchedules
      .filter(teacherSchedule => teacherSchedule.schedule.hour === selectedHour)
      .map(teacherSchedule => teacherSchedule.profile);

    if (service?.randomTeacher) {
      setSelectedTeacherId(teachersByHours[Math.floor(Math.random() * teachersByHours.length)].id);
    } else {
      setAvailableTeachers(teachersByHours);
    }
  }, [selectedHour,]);

  useEffect(() => {
    if (!selectedTeacherId) return;
    const teacherSchedule = teacherSchedules.find(teacherSchedule => teacherSchedule.profile.id === selectedTeacherId && teacherSchedule.schedule.hour === selectedHour);
    if (!teacherSchedule) return;
    setSelectedTeacherScheduleId(teacherSchedule.id);
    setSelectedScheduleId(teacherSchedule.schedule.id);
  }, [selectedTeacherId,]);

  return (
    <>
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Agendar aula</CardTitle>
          <CardDescription>Escolha o serviço, equipamento e horário.</CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <ServiceSelector onSelect={setService} />
          <div className="grid sm:grid-cols-2 gap-4 mt-4">
            {service && <DaySelector onSelect={setSelectedDate} />}
            {hours && <HoursSelector hours={hours} onSelect={setSelectedHour} />}
          </div>
          {availableTeachers && <TeacherSelect teachers={availableTeachers} onSelect={setSelectedTeacherId} />}
          {selectedTeacherId && (
            <SearchUser onSelect={setUser} />
          )}
          {user && (
            <div className='flex gap-2 w-fit p-2 justify-center items-center'>
              {user.name}
              <Button variant='destructive' onClick={() => setUser(null)} >X</Button>
            </div>
          )}
          {selectedScheduleId && service && <EquipmentSelector
            selectedEquipments={selectedEquipments}
            serviceId={service.id}
            selectedScheduleId={selectedScheduleId}
            selectedDate={selectedDate}
            onSelect={selectEquipment}
          /> }
        </CardContent>
        <CardFooter>
          <Button onClick={onBookHandler} className="w-full" disabled={!selectedScheduleId}>{saveLabel}</Button>
        </CardFooter>
      </Card>
      <Dialog open={!!showDialog} onOpenChange={() => setShowDialog(!showDialog)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Alerta de créditos</DialogTitle>
            <DialogDescription>
              Aluno não possui créditos suficientes para agendar. Deseja marcar mesmo assim?
            </DialogDescription>
          </DialogHeader>
          <div className='flex justify-end space-x-2 pt-4'>
            <Button  onClick={confirmNoPackBooking}>
              <Check className='mr-2 h-4 w-4' />
              {confirmLabel}
            </Button>
            <Button variant='destructive' onClick={() => setShowDialog(false)}>
              <X className='mr-2 h-4 w-4' />
              Cancelar
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>

  );
};

export default Book;