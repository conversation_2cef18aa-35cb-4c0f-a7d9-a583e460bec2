'use client';

import { useEffect, useState, } from 'react';
import {
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/index';
import { Service, } from '@/utils/constants/types';
import { useToast, } from '@/components/ui/shard/use-toast';
import { getServices, } from '../actions';

const ServiceSelector = ({ packId, onSelect, }: { packId?: string, onSelect: (service?: Service) => void }) => {
  const [services, setServices,] = useState<Service[]>([]);
  const { toast, } = useToast();

  useEffect(() => {
    (async () => {
      const response = await getServices({ packId, });
      if (response.status === 200) {
        setServices(response.data);
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })();
  }, [packId,]);

  return (
    <div className="space-y-2">
        <Label htmlFor="service">Serviço</Label>
        <Select onValueChange={(serviceId) => onSelect(services.find(service => service.id === serviceId))} required>
          <SelectTrigger id="service">
            <SelectValue placeholder="Selecione um serviço" />
          </SelectTrigger>
          <SelectContent>
            {services && services.map((service) => (<SelectItem key={service.id} value={service.id} className="cursor-pointer">{service.title}</SelectItem>))}
          </SelectContent>
        </Select>
      </div>
  );
};

export default ServiceSelector;