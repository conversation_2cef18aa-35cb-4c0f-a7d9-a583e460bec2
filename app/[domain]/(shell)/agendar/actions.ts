'use server';

import { generateRandomString, } from '@/lib/utils';
import { UNAUTHORIZED, } from '@/utils/constants';
import { Pack, PackProfile, PackService, Profile, Schedule, Service, TeacherSchedule, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { format, } from 'date-fns';
import { PostgrestMaybeSingleResponse, User, } from '@supabase/supabase-js';
import { accountedForBookingStatus, } from '@/utils/supabase/constants';
import { userInfo, } from '@/supabase/verifications/user-info';

type GetPackSuccess = { data: (PackProfile & { pack: Pack })[], status: 200 }

type GetPackFail = { message: string, status: 400 | 401 }

export const getProfilePacks = async (): Promise<GetPackSuccess | GetPackFail> => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user) return { message: UNAUTHORIZED, status: 401, };

  const supabaseAdmin = createClientAdmin();

  const { data, } = await supabaseAdmin
  .from('pack_profile')
  .select('id, pack(*)')
  .in('paymentStatus', ['confirmed', 'received',])
  .match({ purchaseStatus: 'active', profileId: user.id, })
  .returns<(PackProfile & { pack: Pack })[] | null>();

  if (!data) return { message: 'Erro ao buscar serviços.', status: 400, };
  if (!data.length) return { message: 'Usuário não tem pacotes.', status: 400, };
  return { data, status: 200, };
};

type PackServiceCustom = PackService & { service: Service }

type GetPackServiceSuccess = { data: Service[], status: 200 }

type GetPackServiceFail = { message: string, status: 400 | 401 }

export const getServices = async ({ packId, }: { packId?: string }): Promise<GetPackServiceSuccess | GetPackServiceFail> => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (!user) return { message: UNAUTHORIZED, status: 401, };

  const supabaseAdmin = createClientAdmin();

  if (!packId) {
    const { data: services, } = await supabaseAdmin
      .from('service')
      .select()
      .match({ schoolId: user.user_metadata.schoolId, })
      .returns<Service[]>();

    if (!services || services.length === 0) return {
      message: 'Não encontramos serviços cadastrados.',
      status: 400,
    };

    return { data: services, status: 200, };
  }

  const { data: packServices, } = await supabaseAdmin
    .from('service_pack')
    .select('service(*)')
    .match({ packId, })
    .returns<PackServiceCustom[]>();

  if (!packServices || packServices.length === 0) return {
    message: 'Não encontramos serviços atrelados a esse pacote.',
    status: 400,
  };

  return { data: packServices.map(packService => packService.service), status: 200, };
};

type Params = {
  studentId?: string,
  leadId?: string,
  day: string,
  equipments: { id: string, key?: string }[],
  serviceId: string,
  status: 'approved',
  scheduleId: string,
  teacherId: string,
  teacherScheduleId: string,
  ignorePackProfile?: boolean,
}
export const bookByAdmin = async ({
  studentId,
  leadId,
  day,
  equipments,
  serviceId,
  status,
  scheduleId,
  teacherId,
  teacherScheduleId,
  ignorePackProfile,
}: Params) => {
  console.log('🚀 ~ ignorePackProfile:', ignorePackProfile);
  let packProfile = null;

  if (studentId && !ignorePackProfile) {
    const response = await getUserPackProfile({ userId: studentId, });
    if (response.status === 200) {
      packProfile = response.data;

      const verificationResponse = await verifyUserPack({ userId: studentId, packProfileId: packProfile.id, });

      if (verificationResponse.status !== 200) return {
        message: UNAUTHORIZED,
        status: 401,
      };

    } else return response;
  }

  const supabaseAdmin = createClientAdmin();
  const { data: booking, error, } = await supabaseAdmin
  .from('booking')
  .insert({
    code: generateRandomString(),
    day,
    serviceId,
    scheduleId,
    status,
    teacherId,
    teacherScheduleId,
    ...(packProfile && !ignorePackProfile && { packProfileId: packProfile.id, }),
    ...(studentId && { userId: studentId, }),
    ...(leadId && { leadId, }),
  })
  .select()
  .maybeSingle();

  console.log('🚀 ~ error:', error);
  if (error) return {
    message: 'Erro ao salvar agendamento.',
    status: 400,
  };

  if (equipments.length) {
    const equipmentsPromises = equipments.map(async equipment => {
      return await supabaseAdmin.from('booking_equipment').insert({
        bookingId: booking.id,
        equipmentId: equipment.id,
      });
    });
    await Promise.all(equipmentsPromises);
  }

  return { status: 200, };
};

type BookParams = {
  day: string,
  equipments: { id: string, key?: string }[],
  packProfileId: number,
  serviceId: string,
  status: 'approved',
  scheduleId: string,
  teacherId: string,
  teacherScheduleId: string,
}

const verifyUserPack = async ({ userId, packProfileId, }: { userId: string, packProfileId: number }): Promise<{ data: PackProfile & { pack: Pack }, status: 200 } | { message: string, status: 400 | 401 }> => {
  const supabaseAdmin = createClientAdmin();
  const { data: packProfile, }: PostgrestMaybeSingleResponse<PackProfile & { pack: Pack }> = await supabaseAdmin
    .from('pack_profile')
    .select('*, pack(id, use)')
    .in('paymentStatus', ['received', 'confirmed',])
    .match({
      id: packProfileId,
      profileId: userId,
      purchaseStatus: 'active',
    })
    .maybeSingle();

  if (!packProfile) return { message: 'Erro ao procurar pacote do usuário.', status: 400, };

  return {
    data: packProfile,
    status: 200,
  };
};

export const getUserPackProfile = async ({ userId, }: { userId: string }): Promise<{ data: PackProfile & { pack: Pack }, status: 200 } | { message: string, status: 400 }> => {
  const supabaseAdmin = createClientAdmin();
  const { data: packProfile, } = await supabaseAdmin
    .from('pack_profile')
    .select('*, pack(id, use)')
    .in('paymentStatus', ['received', 'confirmed',])
    .match({
      profileId: userId,
      purchaseStatus: 'active',
    })
    .order('expireDate', { ascending: true, });

  if (!packProfile || packProfile.length === 0) return {
    message: 'Usuário não possui pacotes.',
    status: 400,
  };

  return {
    data: packProfile[0],
    status: 200,
  };
};

export const verifyUserCredit = async ({ packProfile, }: { packProfile: PackProfile & { pack: Pack } }) => {
  const supabaseAdmin = createClientAdmin();
  const { count, } = await supabaseAdmin
    .from('booking')
    .select('*', { count: 'exact', head: true, })
    .eq('packProfileId', packProfile.id)
    .in('status', accountedForBookingStatus);

  if (packProfile.pack.use !== null && count !== null && (count + packProfile.used) >= packProfile.pack.use) return {
    message: 'Usuário não possui créditos.',
    status: 400,
  };
  return { status: 200, };
};

export const book = async ({ day, equipments, packProfileId, serviceId, status, scheduleId, teacherId, teacherScheduleId, }: BookParams) => {
  const user = await userInfo();
  if (!user) return { message: UNAUTHORIZED, status: 401, };

  const verificationResponse = await verifyUserPack({ userId: user.id, packProfileId, });
  if (verificationResponse.status !== 200) return {
    message: UNAUTHORIZED,
    status: 401,
  };

  const packProfile = verificationResponse.data;

  const verificationUserCreditResponse = await verifyUserCredit({ packProfile, });

  if (verificationUserCreditResponse.status !== 200) return {
    message: verificationUserCreditResponse.message,
    status: 400,
  };

  const supabaseAdmin = createClientAdmin();
  const { data: booking, error, } = await supabaseAdmin
    .from('booking')
    .insert({
      code: generateRandomString(),
      day,
      packProfileId,
      serviceId,
      scheduleId,
      status,
      teacherId,
      teacherScheduleId,
      userId: user.id,
    })
    .select()
    .maybeSingle();

  console.log('🚀 ~ book ~ error:', error);
  if (error) return {
    message: 'Erro ao salvar agendamento.',
    status: 400,
  };

  if (equipments.length) {
    const equipmentsPromises = equipments.map(async equipment => {
      return await supabaseAdmin.from('booking_equipment').insert({
        bookingId: booking.id,
        equipmentId: equipment.id,
      });
    });
    await Promise.all(equipmentsPromises);
  }

  return { status: 200, };
};

type ASParams = { selectedDate: Date, serviceId: string }
type GASResponse = { data: (TeacherSchedule & { schedule: Schedule, profile: Profile })[], status: 200 } | { message: string, status: 400 }
export const getAvailableSchedules = async ({ selectedDate, serviceId: selectedServiceId, }: ASParams): Promise<GASResponse> => {
  const selectedDay = format(selectedDate, 'yyyy-MM-dd');
  const supabaseAdmin = createClientAdmin();
  // RETORNA TODOS OS TEACHER SCHEDULE QUE NÃO TEM BOOKING, OU BOOKING TÁ CANCELADO OU DESISTIDO
  const { data, error, } = await supabaseAdmin
    .rpc('get_available_schedules', { selectedDay, selectedServiceId, });
  console.log('🚀 ~ getAvailableSchedules ~ data:', data);

  if (error) return {
    message: 'Erro ao buscar horários disponíveis.',
    status: 400,
  };

  return {
    data,
    status: 200,
  };
};
