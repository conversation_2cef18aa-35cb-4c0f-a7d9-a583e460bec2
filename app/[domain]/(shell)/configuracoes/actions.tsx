'use server';

import { createClient, createClientAdmin, } from '@/utils/supabase/server';

export const saveClosedDays = async (closedWeekdays: any, holidays: any) => {
  const supabaseAdmin = createClientAdmin();
  const supabase = createClient();
  const { data: { user,}, } = await supabase.auth.getUser();
  const { error, } = await supabaseAdmin
    .from('school_settings')
    .update({
      closed: holidays,
      schoolId: user?.user_metadata.schoolId,
      closedWeekDays: closedWeekdays,
    })
    .match({ schoolId: user?.user_metadata.schoolId, })
    .select();
  console.log('🚀 ~ saveClosedDays ~ error:', error);
  if(error) return {
    message: 'Erro ao salvar configurações.',
    status : 400,
  };
  return {
    message:'Datas salvas com sucesso!',
    status: 200,
  };
};

export const getClosedDays = async () => {
  const supabase = createClient();
  const { data: { user,}, } = await supabase.auth.getUser();
  const supabaseAdmin = createClientAdmin();
  const schoolId = user?.user_metadata.schoolId;
  const { data, error, } = await supabaseAdmin
  .from('school_settings')
  .select('closed, closedWeekDays')
  .eq('schoolId', schoolId )
  .maybeSingle();

  if(error) return {
    message: 'Erro ao buscar feriados salvos',
    status: 400,
  };
  return data;
};

export const getQrCodeUrl = async (domain: string) => {
  try {
    const supabaseAdmin = createClientAdmin();

    // Get the public URL for the QR code image
    const { data, } = supabaseAdmin.storage
      .from(domain)
      .getPublicUrl('qr-code.png');

    if (!data) {
      return {
        message: 'QR code não encontrado',
        status: 404,
      };
    }

    return {
      data: data.publicUrl,
      status: 200,
    };
  } catch (error) {
    console.error('Error getting QR code URL:', error);
    return {
      message: 'Erro ao buscar QR code',
      status: 500,
    };
  }
};
