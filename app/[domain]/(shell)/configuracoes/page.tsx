'use client';

import { useEffect, useState, } from 'react';
import {
  Button,
  Calendar,
  Table,
  TableBody,
  TableCell, TableHead,
  TableHeader,
  TableRow,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/index';
import { X, Download, } from 'lucide-react';
import { format, } from 'date-fns';
import { saveClosedDays, getClosedDays, getQrCodeUrl, } from './actions';
import { ptBR, } from 'date-fns/locale';
import { daysOfWeek, } from '@/utils/constants';
import { toast, } from '@/components/ui/shard/use-toast';
import Image from 'next/image';

export default function SchoolSchedule({ params, }: { params: { domain: string } }) {
  const [holidays, setHolidays,] = useState<Date[]>([]);
  const [selectedDate, setSelectedDate,] = useState<Date | undefined>();
  const [weeklyClosures, setWeeklyClosures,] = useState<number[]>([]);
  const [qrCodeUrl, setQrCodeUrl,] = useState<string | null>(null);
  const [isDownloading, setIsDownloading,] = useState(false);

  useEffect(() => {
    (async () => {
      const response: any = await getClosedDays();
      console.log('🚀 ~ response:', response);
      if (response.status === 400) {
        toast({
          title: response.message,
        });
      }
      else {
        const date = response?.closed || [];
        setHolidays(date);
        setWeeklyClosures(response.closedWeekDays);
      }
    })();

    // Fetch QR code URL
    (async () => {
      const qrResponse = await getQrCodeUrl(params.domain);
      if (qrResponse.status === 200 && qrResponse.data) {
        setQrCodeUrl(qrResponse.data);
      } else {
        toast({
          title: qrResponse.message,
          variant: 'destructive',
        });
      }
    })();
  }, [params.domain,]);

  const formatDate = (date : Date) => {
    const rawDate = new Date(date);
    const formattedDate = new Date(rawDate.valueOf() + rawDate.getTimezoneOffset() * 60 * 1000);
    return format(formattedDate, 'PPP', { locale: ptBR, });
  };

  const handleWeeklyClosureChange = (day: number) => {
    setWeeklyClosures(prev =>
      prev.includes(day) ? prev.filter(d => d !== day) : [...prev, day,]
    );
  };

  const addHoliday = () => {
    if (selectedDate && !holidays.some((date) => date.getTime() === selectedDate.getTime())) {
      setHolidays([...holidays, selectedDate,]);
    }
  };

  const removeHoliday = (dateToRemove: Date) => {
    setHolidays(holidays.filter((date) => date.getTime() !== dateToRemove.getTime()));
  };

  const handleSave = async () => {
    const response = await saveClosedDays(weeklyClosures, holidays,);
    if(response?.status === 200) {
      toast({
        title:response.message,
      });
    }
    else toast({
      title:response.message,
    });
  };

  const downloadQrCode = async () => {
    if (!qrCodeUrl) return;

    try {
      setIsDownloading(true);

      // Fetch the image as a blob
      const response = await fetch(qrCodeUrl);
      if (!response.ok) throw new Error('Failed to fetch QR code');

      const blob = await response.blob();

      // Create a blob URL
      const blobUrl = URL.createObjectURL(blob);

      // Create a temporary link to download the QR code
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `qr-code-${params.domain}.png`;

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the blob URL
      setTimeout(() => {
        URL.revokeObjectURL(blobUrl);
      }, 100);
    } catch (error) {
      console.error('Error downloading QR code:', error);
      toast({
        title: 'Erro ao baixar QR Code',
        variant: 'destructive',
      });
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <div className="container mx-auto py-4 space-y-8">
      <Tabs defaultValue="calendar" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="calendar">Calendário e Feriados</TabsTrigger>
          <TabsTrigger value="qrcode">QR Code da Escola</TabsTrigger>
        </TabsList>

        <TabsContent value="calendar" className="space-y-4 mt-4">
          <div>
            <h2 className="text-lg font-semibold mb-2">Dias nos quais a escola fecha:</h2>
            <div className="flex flex-wrap gap-2">
              {daysOfWeek.map((day, index) => (
                <Button
                  key={day}
                  type="button"
                  variant={weeklyClosures.includes(index) ? 'default' : 'outline'}
                  onClick={() => handleWeeklyClosureChange(index)}
                >
                  {day}
                </Button>
              ))}
            </div>
          </div>

          <div className="grid py-8 md:grid-cols-2 gap-8">
            <div className="space-y-2 mt-30">
              <h2 className="text-xl font-semibold">Adicionar Feriado:</h2>
              <div className="border rounded-lg p-4">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  className="rounded-md border"
                />
                <Button onClick={addHoliday} className="w-full mt-4">
                  Adicionar Feriado
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <h2 className="text-xl font-semibold">Fechada nos feriados:</h2>
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Data</TableHead>
                      <TableHead className="w-[100px]">Remover</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {holidays.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={2} className="text-center text-muted-foreground">
                          Sem feriados
                        </TableCell>
                      </TableRow>
                    ) : (
                      holidays.map((date, index) => (
                        <TableRow key={index}>
                          <TableCell>{(formatDate(date))}</TableCell>
                          <TableCell>
                            <Button variant="ghost" size="icon" onClick={() => removeHoliday(date)}>
                              <X className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>

          <Button onClick={handleSave} className="w-full md:w-auto">
            Salvar alterações
          </Button>
        </TabsContent>

        <TabsContent value="qrcode" className="space-y-4 mt-4">
          <div className="p-6 space-y-4">
            {qrCodeUrl ? (
              <div className="flex flex-col items-center gap-4">
                <p className="text-sm text-muted-foreground mt-4">
                  Scannear o QR pra marcar presença na aula no perído de meia hora antes até meia hora depois da aula.
                </p>
                <div className="relative w-64 h-64 border rounded-md overflow-hidden">
                  <Image
                    src={qrCodeUrl}
                    alt="QR Code da Escola"
                    fill
                    style={{ objectFit: 'contain', }}
                  />
                </div>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={downloadQrCode}
                  disabled={isDownloading}
                >
                  <Download className="mr-2 h-4 w-4" />
                  {isDownloading ? 'Baixando...' : 'Baixar QR Code'}
                </Button>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">QR code não disponível</p>
                <p className="text-sm text-muted-foreground mt-2">
                  O QR code da escola será gerado automaticamente quando necessário.
                </p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

