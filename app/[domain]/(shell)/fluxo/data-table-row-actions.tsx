'use client';

import { Row, } from '@tanstack/react-table';
import { MoreHorizontal, } from 'lucide-react';

import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/index';
import { paymentSchema, } from './schema';
import { payDebt, } from './actions';
import { toast, } from '@/components/ui/shard/use-toast';

interface DataTableRowActionsProps<TData> {
  row: Row<TData>
}

export function DataTableRowActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const payment = paymentSchema.parse(row.original);
  const { id, status, } = payment;

  const handlePaymentConfirmed = async (id: string) => {
    const response = await payDebt({ id, });
    if (response.status === 200) {
      toast({
        title: 'Cobrança paga com sucesso.',
      });
    } else {
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
  };

  if (status !== 'pending') return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
        >
          <MoreHorizontal />
          <span className="sr-only">Abrir menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        {status === 'pending' && (
          <DropdownMenuItem
            onSelect={() => handlePaymentConfirmed(id)}
            className="cursor-pointer"
          >
            Pago
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}