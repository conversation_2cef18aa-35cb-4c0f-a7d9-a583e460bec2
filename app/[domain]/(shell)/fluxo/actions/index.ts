'use server';

import { createClientAdmin, } from '@/utils/supabase/server';

export const payDebt = async ({ id, }: { id: string }) => {
  const supabase = createClientAdmin();
  const { data, error, } = await supabase
    .from('debt')
    .update({
      paid: true,
      paidDate: new Date(),
    })
    .match({ id, })
    .select();

  if (error) return {
    message: 'Erro ao pagar cobrança.',
    status: 400,
  };

  return { data, status: 200, };
};