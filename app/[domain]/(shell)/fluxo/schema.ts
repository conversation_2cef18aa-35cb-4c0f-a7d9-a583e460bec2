import { z, } from 'zod';

// We're keeping a simple non-relational schema here.
// IRL, you will have a schema for your data models.
export const paymentSchema = z.object({
  id: z.string(),
  name: z.string(),
  status: z.string(),
  netValue: z.number(),
  paymentDate: z.string().optional(),
  typeId: z.string().optional(),
  type: z.string(),
});


export type Payment = z.infer<typeof paymentSchema>