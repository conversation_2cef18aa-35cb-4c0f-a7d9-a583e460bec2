'use client';

import { ColumnDef, } from '@tanstack/react-table';

import { Payment, } from './schema';
import { DataTableColumnHeader, } from './data-table-column-header';
import { Badge, } from '@/components/ui/shard/badge';
import { format, parseISO, } from 'date-fns';
import { paymentStatuses, } from '@/utils/constants';
import { formatToBrazilianReal, } from '@/lib/utils';
import { ArrowDown, ArrowUp, } from 'lucide-react';
import { DataTableRowActions, } from './data-table-row-actions';

export const columns: ColumnDef<Payment>[] = [
  {
    accessorKey: 'type',
    header: ({ column, }) => (
      <DataTableColumnHeader column={column} title="Tipo" />
    ),
    cell: ({ row, }) => {
      const type = row.getValue('type');
      return (
        <div>
          <span>{type === 'in' ? <ArrowUp /> : <ArrowDown />}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'name',
    header: ({ column, }) => (
      <DataTableColumnHeader column={column} title="Pagamento" />
    ),
    cell: ({ row, }) => {
      const name = row.getValue('name');
      return (
        <div>
          <span>{name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: ({ column, }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row, }) => {
      const paymentStatus = paymentStatuses[row.getValue('status').toLowerCase()];
      return <Badge variant={paymentStatus.color} >{paymentStatus.label}</Badge>;
    },
  },
  {
    accessorKey: 'netValue',
    header: ({ column, }) => (
      <DataTableColumnHeader column={column} title="Valor" />
    ),
    cell: ({ row, }) => {
      return (
        <div className="flex items-center">
          <span>{formatToBrazilianReal(row.getValue('netValue'))}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'paymentDate',
    header: ({ column, }) => (
      <DataTableColumnHeader column={column} title="Data" />
    ),
    cell: ({ row, }) => {
      const dateApproved = row.getValue('paymentDate');
      return (
        <div className="flex items-center">
          <span>{format(parseISO(dateApproved), 'dd/MM/yyyy')}</span>
        </div>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row, }) => <DataTableRowActions row={row} />,
  },
];