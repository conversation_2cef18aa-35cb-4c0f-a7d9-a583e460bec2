import { Metadata, } from 'next';

import { redirect, } from 'next/navigation';
import { createClientAdmin, } from '@/utils/supabase/server';
import { paymentSchema, } from './schema';
import { z, } from 'zod';
import { Tabs, } from '@radix-ui/react-tabs';
import { TabsContent, TabsList, TabsTrigger, } from '@/components/ui/shard/tabs';
import { User, } from '@supabase/supabase-js';
import { columns, } from './columns';
import { DataTable, } from './data-table';
import { Debt, Sale, Subdomains, } from '@/utils/constants/types';
import { format, getDaysInMonth, } from 'date-fns';
import { NothingFound, } from '@/components/index';
import { userInfo, } from '@/supabase/verifications/user-info';

export const metadata: Metadata = {
  title: 'Informações financeiras.',
  description: 'Visualização de gerenciamento financeiro.',
};

type Payment = {
  externalReference: string,
  description: string,
  status: string,
}

const getPayments = async ({ payments, label, }: { payments: Payment[], label: 'plan' | 'pack' }) => {
  const filteredPayments = payments
  .filter(payment => {
    if (!payment.externalReference) return false;
    if (label === 'plan') return payment.externalReference.includes('plan');
    return payment.externalReference.includes('pack');
  })
  .map(payment => ({
    ...payment,
    status: payment?.status?.toLowerCase(),
    name: payment?.description,
    type: 'in',
  }));

  return z.array(paymentSchema).parse(filteredPayments);
};

const getDebts = async ({ user, }: { user: User }) => {
  const today = new Date();
  today.setDate(getDaysInMonth(today));
  const supabaseAdmin = createClientAdmin();

  const { data: debts, } = await supabaseAdmin
    .from('debt')
    .select()
    .lte('billingDate', format(today, 'yyyy-MM-dd'))
    .order('billingDate', { ascending: false, })
    .eq('schoolId', user.user_metadata.schoolId)
    .returns<Debt[] | null>();

  let debtsFormatted = (debts || []).map(debt => ({
    id: debt.id,
    name: debt.name,
    paymentDate: debt.billingDate,
    status: debt.paid ? 'paid' : 'pending',
    netValue: debt.value,
    type: 'out',
  }));
  return z.array(paymentSchema).parse(debtsFormatted);
};

const getSales = async ({ schoolId, }: { schoolId: string }) => {
  const today = new Date();
  today.setFullYear(new Date().getFullYear());
  today.setDate(getDaysInMonth(today));
  today.setUTCHours(23, 59, 59);
  const supabaseAdmin = createClientAdmin();

  const { data: sales, } = await supabaseAdmin
    .from('sale')
    .select()
    .lte('billingDate', format(today, 'yyyy-MM-dd'))
    .order('billingDate', { ascending: false, })
    .eq('schoolId', schoolId)
    .returns<Sale[] | null>();

  let salesFormatted = (sales || []).map(sale => ({
    id: sale.id,
    name: sale.name,
    paymentDate: sale.billingDate,
    status: 'confirmed',
    netValue: sale.value,
    type: 'in',
  }));
  return z.array(paymentSchema).parse(salesFormatted);
};

const Dashboard = async ({ params: { domain, }, }: { params: { domain: Subdomains } }) => {
  const user = await userInfo();

  if (!user || user?.user_metadata.role !== 'admin') redirect('/login');

  const tokenName = `AS_API_KEY_${domain}`;
  const token = process.env[tokenName];
  const headers = {
    access_token: token,
    'User-Agent': 'meu-mestre',
  };
  const year = new Date();
  year.setFullYear(new Date().getFullYear() - 1);
  year.setDate(1);
  year.setMonth(0);
  console.log('🚀 ~ Dashboard ~ year:', year);
  const queryParams = `limit=5000&status=RECEIVED&dateCreated[ge]=${format(year, 'yyyy-MM-dd')}`;
  const response = await fetch(`https://api.asaas.com/v3/payments?${queryParams}`, {
    headers,
  });

  if (response.status === 200) {
    const { data: payments, } = await response.json() as { data: Payment[] };
    console.log('🚀 ~ Dashboard ~ payments:', payments);
    const paymentsPlanZod = [...await getPayments({ payments, label: 'plan', }), ...await getSales({ schoolId: user.user_metadata.schoolId, }),];
    console.log('🚀 ~ Dashboard ~ paymentsPlanZod:', paymentsPlanZod);
    const paymentsPackZod = await getPayments({ payments, label: 'pack', });
    console.log('🚀 ~ Dashboard ~ paymentsPackZod:', paymentsPackZod);

    const debts = await getDebts({ user, });
    const orderedPaymentAndDebts = [...debts, ...paymentsPlanZod, ...paymentsPackZod,]
    .sort((a, b) => (new Date(b.paymentDate) - new Date(a.paymentDate)));


    return (
      <Tabs defaultValue='geral' className='w-full'>
        <TabsList className='flex w-full mb-6'>
          <TabsTrigger value='geral' className='flex-1'>Geral</TabsTrigger>
          <TabsTrigger value='plans' className='flex-1'>Planos</TabsTrigger>
          <TabsTrigger value='pack' className='flex-1'>Pacotes</TabsTrigger>
        </TabsList>
        <TabsContent value='geral'>
          <DataTable data={orderedPaymentAndDebts} columns={columns} />
        </TabsContent>
        <TabsContent value='plans'>
          <DataTable data={paymentsPlanZod} columns={columns} />
        </TabsContent>
        <TabsContent value='pack'>
          <DataTable data={paymentsPackZod} columns={columns} />
        </TabsContent>
      </Tabs>
    );

  } else return <NothingFound label='Nenhum pagamento encontrado.' />;
};

export default Dashboard;