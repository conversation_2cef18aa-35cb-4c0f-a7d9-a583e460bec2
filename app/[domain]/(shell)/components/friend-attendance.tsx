'use client';

import { useState, useEffect, } from 'react';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/index';
import { motion, AnimatePresence, } from 'framer-motion';
import { getToday, } from '@/lib/utils';
import { getMonth, } from 'date-fns';
import { toast, } from '@/components/ui/shard/use-toast';
import { getFriendsAttendance, saveFriendAttendance, } from '../presenca/actions';

type Student = {
  userId: string
  name: string
  profileImage: string
}

const FriendAttendance = () => {
  const [availableStudents, setAvailableStudents,] = useState<Student[]>([]);
  const [selectedStudents, setSelectedStudents,] = useState<Student[]>([]);
  const [displayedStudents, setDisplayedStudents,] = useState<Student[]>([]);
  const [drawerOpen, setDrawerOpen,] = useState(true);

  useEffect(() => {
    (async () => {
      const today = getToday();
      const month = getMonth(today);
      console.log('🚀 ~ month:', month);
      const serviceId = localStorage.getItem('serviceId');
      const scheduleHour = localStorage.getItem('scheduleHour');
      const response = await getFriendsAttendance({ scheduleHour, serviceId, });
      if (response.status === 200) {
        setAvailableStudents(response.data);
        setDisplayedStudents(response.data?.slice(0, 9));
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })();
  }, []);

  const selectStudent = async (student: Student) => {
    const serviceId = localStorage.getItem('serviceId');
    const scheduleHour = localStorage.getItem('scheduleHour');
    if (!serviceId || !scheduleHour) return;
    const response = await saveFriendAttendance({
      serviceId,
      scheduleHour,
      friendId: student.id,
    });
    if (response.status !== 200) {
      toast({
        variant: 'destructive',
        title: response.message,
      });
      return;
    }
    toast({
      title: 'Confirmado.',
    });
    setSelectedStudents((prev) => [...prev, student,]);
    setAvailableStudents((prev) => prev.filter((s) => s.id !== student.id));
    setDisplayedStudents((prev) => {
      const newDisplayed = prev.filter((s) => s.id !== student.id);
      if (availableStudents.length > selectedStudents.length + 1) {
        const newStudent = availableStudents.find((s) => !displayedStudents.includes(s) && s.id !== student.id);
        if (newStudent) {
          return [...newDisplayed, newStudent,];
        }
      }
      return newDisplayed;
    });
  };

  return (
    <Drawer open={drawerOpen} onOpenChange={setDrawerOpen}>
      <DrawerContent>
        <div className='mx-auto w-full max-w-4xl'>
          <DrawerHeader>
            <DrawerTitle>Quem mais foi pra aula?</DrawerTitle>
            <DrawerDescription>
              Clique no amigo que também estava presente.
            </DrawerDescription>
          </DrawerHeader>
          <div className='p-4'>
            <div className='grid grid-cols-3 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6'>
              <AnimatePresence>
                {displayedStudents.map((student) => (
                  <motion.div
                    key={student.userId}
                    initial={{ opacity: 0, scale: 0.8, }}
                    animate={{ opacity: 1, scale: 1, }}
                    exit={{ opacity: 0, scale: 0.8, }}
                    transition={{ duration: 0.3, }}
                    className='flex flex-col items-center'
                    onClick={() => selectStudent(student)}
                  >
                    <Avatar className='h-16 w-16 cursor-pointer border-2 border-white shadow-lg transition-transform hover:scale-105 object-cover'>
                      <AvatarImage src={student.profileImage} alt='Nome do aluno' />
                      <AvatarFallback>{student.name[0]}</AvatarFallback>
                    </Avatar>
                    <p className='mt-2 text-center text-sm font-medium'>{`${student.name.split(' ')[0]} ${student.name.split(' ')[1]}`}</p>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default FriendAttendance;