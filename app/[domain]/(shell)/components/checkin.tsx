'use client';

import { useState, useEffect, } from 'react';
import { Belt, Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Skeleton, } from '@/components/index';
import { getAttendanceSinceLastRank, getPoints, getRequiredClassesForNextRank, } from '@/components/ui/belt/actions';
import { getProfile, getProfileSports, getScheduleByServiceAndDay, getServicesBySport, } from '@/app/actions';
import { getStudentRank, sportHasRank, } from '../alunos/actions';
import { saveStudentAttendance, } from '../presenca/actions';
import { toast, } from '@/components/ui/shard/use-toast';
import { Profile, Rank, Schedule, Service, } from '@/utils/constants/types';
import FriendAttendance from './friend-attendance';
import { getToday, } from '@/lib/utils';
import { getDay, } from 'date-fns';

const CheckIn = ({ userId, }: { userId: string }) => {
  const [services, setServices,] = useState<Service[] | null>(null);
  const [schedule, setSchedule,] = useState<Schedule | null>(null);
  const [selectedServiceId, setSelectedServiceId,] = useState<string | null>(null);
  const [sportId, setSportId,] = useState<string | null>(null);
  const [open, setOpen,] = useState(false);
  const [data, setData,] = useState<{
    profile: Profile
    currentRank: Rank & { nextRank: Rank }
    nextRank: Rank
    requiredClasses: number
    progress: number
  } | null>(null);
  const [showFriendAttendance, setShowFriendAttendance,] = useState(false);

  const handleCheckIn = async () => {
    if (!selectedServiceId || !schedule) return;
    const saveAttendanceResponse = await saveStudentAttendance({ serviceId: selectedServiceId, scheduleHour: schedule.hour, });
    if (saveAttendanceResponse.status === 200) {
      toast({
        title: 'Check In feito com sucesso.',
      });
      setData(data => data && { ...data, progress: data.progress + 1, });
    } else {
      toast({
        variant: 'destructive',
        title: saveAttendanceResponse.message,
      });
    }
    setShowFriendAttendance(true);
  };

  useEffect(() => {
    const fetchData = async () => {
      const profileSportsResponse = await getProfileSports();

      if (profileSportsResponse.status === 400) {
        toast({
          variant: 'destructive',
          title: profileSportsResponse.message,
        });
        return;
      }

      const sport = profileSportsResponse.data[0];
      setSportId(sport.id);

      const sportHasRankResponse = await sportHasRank({ sportId: sport.id, });
      if (sportHasRankResponse.status !== 200 || !sportHasRankResponse.data) return;
      setOpen(true);

      const profileResponse = await getProfile(userId);
      if (profileResponse.status !== 200) return;
      const profile = profileResponse.data;

      const studentRankResponse = await getStudentRank({ userId: profile.id, sportId: sport.id, });
      if (studentRankResponse.status !== 200) return;
      const studentRank = studentRankResponse.data;

      if (!studentRank.rank || !studentRank.rank.nextRank) return;

      const currentRank = studentRank.rank;
      const nextRank = currentRank.nextRank;

      const requiredClassesResponse = await getRequiredClassesForNextRank({
        rankId: nextRank.id,
        schoolId: profile.schoolId,
      });
      const requiredClasses = (requiredClassesResponse.status === 200 && requiredClassesResponse.data.classes) || 20;

      const attendanceResponse = await getAttendanceSinceLastRank({
        profileId: profile.id,
        sportId: sport.id,
        dateLastRank: studentRank.createdAt,
      });
      console.log('🚀 ~ fetchData ~ attendanceResponse:', attendanceResponse);
      const attendance = (attendanceResponse.status === 200 && attendanceResponse.data.length) || 0;

      const pointsResponse = await getPoints({
        profileId: profile.id,
        sportId: sport.id,
        dateLastRank: studentRank.createdAt,
      });
      const points = (pointsResponse.status === 200 && pointsResponse.data.length) || 0;
      console.log('🚀 ~ fetchData ~ points:', points);

      setData({
        profile,
        currentRank,
        nextRank,
        requiredClasses,
        progress: attendance + points,
      });
    };

    fetchData();
  }, [userId,]);

  useEffect(() => {
    (async () => {
      if (!sportId) return null;
      const servicesResponse = await getServicesBySport({ sportId, });
      if (servicesResponse.status === 200) {
        setServices(servicesResponse.data);
        setSelectedServiceId(servicesResponse.data[0].id);
      } else {
        toast({
          variant: 'destructive',
          title: servicesResponse.message,
        });
      }
    })();
  }, [sportId,]);

  useEffect(() => {
    (async () => {
      if (!selectedServiceId) return null;
      const today = getToday();
      const dayOfWeek = getDay(today);
      const scheduleResponse = await getScheduleByServiceAndDay({
        serviceId: selectedServiceId,
        dayOfWeek,
      });
      console.log('🚀 ~ scheduleResponse:', scheduleResponse);
      if (scheduleResponse.status === 200) {
        setSchedule(scheduleResponse.data);
      } else {
        toast({
          variant: 'destructive',
          title: scheduleResponse.message,
        });
      }
    })();
  }, [selectedServiceId,]);

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="flex flex-col gap-6 pb-10 w-full px-4">
        <DrawerHeader className='flex flex-col items-start py-0'>
          <DrawerTitle>Presença na aula das {schedule?.hour}</DrawerTitle>
          <DrawerDescription className='text-start'>Clique duas vezes na Faixa pra informar ao professor que você compareceu à aula.</DrawerDescription>
        </DrawerHeader>
        {sportId && (
          <Select onValueChange={setSelectedServiceId} value={selectedServiceId} className='w-full'>
            <SelectTrigger className='h-12'>
              <SelectValue placeholder='Serviços' />
            </SelectTrigger>
            <SelectContent side='top'>
              {services && services.map((service) => (
                <SelectItem key={service.id} value={service.id} className='h-10'>
                  {service.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
        {data && (
          <Belt
            label='Clique duas vezes na faixa'
            rank={data.currentRank}
            required={data.requiredClasses}
            attendance={data.progress}
            onDoubleClick={handleCheckIn}
          />
        )}
        {!data && (
          <div>
            <Skeleton className="h-8 w-full" />
          </div>
        )}
        {showFriendAttendance && <FriendAttendance  />}
      </DrawerContent>
    </Drawer>
  );
};

export default CheckIn;

