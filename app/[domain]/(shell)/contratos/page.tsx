'use client';

import { useEffect, useState, } from 'react';
import { Button, Input, Table, TableBody, TableCell, TableHead, TableHeader, TableRow, } from '@/components/index';
import { CheckCircle, Plus, XCircle, } from 'lucide-react';
import { Contract, Plan, Profile, } from '@/utils/constants/types';
import { getContracts, } from './actions';
import { toast, } from '@/components/ui/shard/use-toast';
import { format, parseISO, } from 'date-fns';
import Link from 'next/link';

export default function ContractDisplay() {
  const [searchTerm, setSearchTerm,] = useState('');
  const [contracts, setContracts,] = useState<(Contract & { profile: Profile, plan: Plan })[] | []>([]);

  // Filter contracts based on search term
  const filteredContracts = contracts.filter(({ profile, plan, }) =>
    profile.name.includes(searchTerm.toLowerCase()) ||
    plan.name.includes(searchTerm.toLowerCase())
  );

  useEffect(() => {
    (async () => {
      const response = await getContracts();
      if (response.status === 200) {
        setContracts(response.data);
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })();
  }, []);

  return (
    <div className='py-6'>
      <Input
        placeholder='Procurar contrato'
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className='max-w-sm mb-4'
      />
      <div className='rounded-md border w-full'>
        <Table className='w-full'>
          <TableHeader>
            <TableRow>
              <TableHead>Estudante</TableHead>
              <TableHead>Nome</TableHead>
              <TableHead>Início</TableHead>
              <TableHead>Fim</TableHead>
              <TableHead>Assinado</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredContracts.map((contract) => (
              <TableRow key={contract.id}>
                <TableCell>{contract.profile.name}</TableCell>
                <TableCell>{contract.plan.name}</TableCell>
                {contract.start ? <TableCell>{format(parseISO(contract.start), 'dd/MM/yyyy')}</TableCell> : <TableCell>Indefinido</TableCell> }
                {contract.end ? <TableCell>{format(parseISO(contract.end), 'dd/MM/yyyy')}</TableCell> : <TableCell>Indefinido</TableCell> }
                <TableCell>
                  {contract.signed ? (
                    <div className='flex items-center'>
                      <CheckCircle className='h-6 w-6 text-green-500 ml-4' />
                    </div>
                  ) : (
                    <div className='flex items-center'>
                      <XCircle className='h-6 w-6 text-red-500 ml-4' />
                    </div>
                  )}
                </TableCell>
                <TableCell className='flex'>
                  <Link href={contract.signed ? `https://api.autentique.com.br/documentos/${contract.id}/assinado.pdf` : contract.originalUrl} size='sm' className='ml-auto border py-2 px-4 rounded-md'>
                    Contrato
                  </Link>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      <div className="fixed bottom-6 right-6 z-50">
        <Button asChild size="lg" className="rounded-full shadow-lg h-14 w-14 p-0 md:w-auto md:px-4 md:h-12">
          <Link href='/criar-contrato'>
            <Plus className="h-6 w-6" />
          </Link>
        </Button>
      </div>
    </div>
  );
}

