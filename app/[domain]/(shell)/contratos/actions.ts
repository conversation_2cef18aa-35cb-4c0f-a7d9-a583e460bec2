'use server';

import { userInfo, } from '@/supabase/verifications/user-info';
import { Contract, Plan, Profile, } from '@/utils/constants/types';
import { createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestResponse, } from '@supabase/supabase-js';

export const getContracts = async (): Promise<{ data: (Contract & { profile: Profile, plan: Plan })[], status: 200 } | { message: string, status: 400 | 401 | 404 }> => {
  const user = await userInfo();

  if (user?.user_metadata.role !== 'admin') return {
    message: 'Você não tem permissão para acessar esta página.',
    status: 401,
  };

  const { data, error, }: PostgrestResponse<Contract & { profile: Profile, plan: Plan }> = await createClientAdmin()
  .from('contract')
  .select('*, profile(*), plan(*)')
  .match({ schoolId: user?.user_metadata.schoolId, });

  console.log('🚀 ~ getContracts ~ data:', data);
  console.log('🚀 ~ getContracts ~ error:', error);
  if (error) {
    console.error('Error fetching contracts:', error);
    return { message: 'Erro ao buscar contratos.', status: 400, };
  }

  if (data.length === 0) return {
    message: 'Nenhum contrato encontrado.',
    status: 404,
  };

  return { data, status: 200, };
};