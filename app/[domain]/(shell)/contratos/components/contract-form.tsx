import { Button, FormControl, FormField, FormItem, FormMessage, Input, Label, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Textarea, } from '@/components/index';
import { ChangeEvent, useState, } from 'react';
import { formatDate, formatPhoneNumber, handleCpfChange, validateCPF, } from '@/lib/utils';

interface ContractFormProps {
  initialDetails: {
    partyA: string
    partyB: string
    startDate: string
    endDate: string
    terms: string
  }
  onSubmit: (details: {
    partyA: string
    partyB: string
    startDate: string
    endDate: string
    terms: string
  }) => void
}

export default function ContractForm({ form, }: ContractFormProps) {
  const handleFieldChange = (e: ChangeEvent<HTMLInputElement>, field) => {
    const formatted = formatDate(e.target.value);
    form.setValue(field, formatted, { shouldValidate: true, });
  };

  const onCpfChange = (e: ChangeEvent<HTMLInputElement>) => {
    const formattedValue = handleCpfChange(e.target.value);
    form.setValue('cpf', formattedValue, { shouldValidate: true, });
  };

  return (
    <div className='space-y-5'>
      <FormField
        control={form.control}
        name='name'
        render={({ field, }) => (
          <FormItem  className='flex-[3_3_0%]'>
            <FormControl>
              <Input placeholder='Nome completo do aluno' {...field} type='text' />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name='email'
        render={({ field, }) => (
          <FormItem  className='flex-[3_3_0%]'>
            <FormControl>
              <Input placeholder='Email do aluno' {...field} type='text' />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name='cpf'
        render={({ field, }) => (
          <FormItem  className='flex-[3_3_0%]'>
            <FormControl>
              <Input
                placeholder='CPF do aluno'
                {...field}
                onChange={onCpfChange}
                maxLength={14}
                type='text'
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name='birthdate'
        render={({ field, }) => (
          <FormItem  className='flex-[3_3_0%]'>
            <FormControl>
              <Input
                placeholder='Data de nascimento do aluno'
                {...field}
                onChange={(e) => handleFieldChange(e, 'birthdate')}
                maxLength={10}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name='start'
        render={({ field, }) => (
          <FormItem  className='flex-[3_3_0%]'>
            <FormControl>
              <Input
                placeholder='Início do plano dd/mm/aaaa'
                {...field}
                onChange={(e) => handleFieldChange(e, 'start')}
                maxLength={10}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name='end'
        render={({ field, }) => (
          <FormItem  className='flex-[3_3_0%]'>
            <FormControl>
              <Input
                placeholder='Fim do plano (deixar em branco caso seja indefinido)'
                {...field}
                onChange={(e) => handleFieldChange(e, 'end')}
                maxLength={10}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name='recurrency'
        render={({ field, }) => (
          <FormItem>
            <FormControl>
              <Select onValueChange={field.onChange} >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder='Recorrência' />
                </SelectTrigger>
                <SelectContent>
                  {['Única', 'Semanal', 'Mensal', 'Semestral', 'Anual',].map(type => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
                </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name='hours'
        render={({ field, }) => (
          <FormItem  className='flex-[3_3_0%]'>
            <FormControl>
              <Textarea
                placeholder='Horário que o serviço é disponibilizado'
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name='priceUpdates'
        render={({ field, }) => (
          <FormItem  className='flex-[3_3_0%]'>
            <FormControl>
              <Textarea
                placeholder='Regras de mudança de preço dos produtos.'
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name='reimbursement'
        render={({ field, }) => (
          <FormItem  className='flex-[3_3_0%]'>
            <FormControl>
              <Textarea
                placeholder='Regras de reembolso.'
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name='cancel'
        render={({ field, }) => (
          <FormItem  className='flex-[3_3_0%]'>
            <FormControl>
              <Textarea
                placeholder='Regras de cancelamento de plano.'
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}