import { NothingFound, } from '@/components/index';
import PurchaseCard from './components/purchase-card';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { Pack, Service, } from '@/utils/constants/types';

const Packs = async () => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  const isAdmin = user?.user_metadata.role === 'admin';

  const supabaseAdmin = createClientAdmin();
  const { data, } = await supabaseAdmin
  .from('pack')
  .select()
  .match({ schoolId: user?.user_metadata.schoolId, active: true, })
  .order('price', { ascending: true, })
  .returns<Pack[] | null>();

  if (!data) {
    return <NothingFound label='Erro ao buscar pacotes.' />;
  }

  if (data.length === 0) {
    return <NothingFound label='Nenhum pacote encontrado.' />;
  }

  const packPromises = data.map(async (pack: Pack) => {
    const { data: packService, } = await supabaseAdmin
    .from('service_pack')
    .select('service(*)')
    .match({ packId: pack.id, })
    .returns<{ service: Service }[]>();
    return {
      ...pack,
      services: packService?.map(ps => ps.service),
    };
  });
  const packs = await Promise.all(packPromises);

  return (
    <div className='grid grid-cols-auto-fill-300 gap-10'>
      {packs && packs.length > 0 && packs.map((pack) => {
        return <PurchaseCard key={pack.id} pack={pack} isAdminPage={isAdmin} />;
      })}
      </div>
  );
};

export default Packs;