'use server';

import { BillingType, Pack, PackProfile, PackService, Subdomains, } from '@/utils/constants/types';
import { FormData, } from './[packId]/components/payment-form';
import { createClientAdmin, } from '@/utils/supabase/server';
import { UNAUTHORIZED, } from '@/utils/constants';
import { headers, } from 'next/headers';
import { format, } from 'date-fns';
import { userInfo, } from '@/supabase/verifications/user-info';
import { PostgrestError, PostgrestMaybeSingleResponse, } from '@supabase/supabase-js';
import dictionary from '@/lib/dictionary';
import { revalidatePath } from 'next/cache';

type CreditCardType = { type: 'CREDIT_CARD', formData: FormData }

type GetErrorMessageType = {
  error: PostgrestError | null,
  service: string,
}


type OtherType = {
  type: Exclude<BillingType, 'CREDIT_CARD'>,
}

type FormDataProp = {
  formData?: FormData,
  domain: Subdomains
  pack: Pack,
} & (CreditCardType | OtherType)

type PaymentAs = {
	object: 'subscription',
	id: string,
	dateCreated: Date,
	customer: string,
	value: number,
	nextDueDate: Date,
	description: string,
	billingType: BillingType,
	deleted: false,
	status: 'ACTIVE',
	externalReference: null,
}
type ResponseType = { data: PaymentAs, status: 201 } | { message: string, status: 400 | 401 }

type Body = {
  billingType: BillingType,
  customer: string | null,
  dueDate: string,
  creditCard?: {
    holderName: string,
    number: string,
    expiryMonth: string,
    expiryYear: string,
    ccv: string,
  },
  creditCardHolderInfo?: {
    name: string,
    email: string,
    cpfCnpj: string,
    postalCode: string,
    phone: string,
    addressNumber: string,
  }
  description: string,
  externalReference: string,
  value: number,
  nextDueDate?: string | null,
  totalValue?: number
  installmentCount?: number
}

type GetBody = {
  asId: string,
  type: BillingType,
  pack: Pack
}

export async function getIPAddress() {
    return headers().get('x-forwarded-for');
}

const getBody = async ({ asId, type, pack, }: GetBody) => {
  let body: Body = {
    customer: asId,
    billingType: type,
    value: pack.price,
    externalReference: `pack:${pack.id}`,
    description: pack.name,
    dueDate: format(new Date(), 'yyyy-MM-dd'),
  };

  return body;
};

const getCreditCardInfo = ({ formData, }: { formData: FormData }) => {
  const today = new Date();
  today.setUTCHours(today.getUTCHours() - 3);
  const nextDay = format(today, 'yyyy-MM-dd');

  return {
    creditCard: {
      holderName: formData.holderName,
      number: formData.cardNumber,
      expiryMonth: formData.expiryMonth,
      expiryYear: formData.expiryYear,
      ccv: formData.ccv,
    },
    creditCardHolderInfo: {
      name: formData.name,
      email: formData.email,
      cpfCnpj: formData.cpf,
      postalCode: formData.cep,
      phone: formData.phone,
      addressNumber: formData.addressNumber,
    },
    nextDueDate: nextDay,
  };
};

const createAsaasPayment = async ({ domain, body, }: { domain: string, body: Body }) => {
  const asaasApiKey = process.env[`AS_API_KEY_${domain}`];
  const url = 'https://api.asaas.com/v3/payments/';
  const response = await fetch(url, {
    body: JSON.stringify(body),
    headers: {
      access_token: asaasApiKey,
      'User-Agent': 'meu-mestre',
    },
    method: 'POST',
  });

  const data = await response.json();
  console.log('🚀 ~ createAsaasPayment ~ data:', data);
  if (response.status === 200) {
    return { data, status: 200, };
  } else {
    return { message: data.errors[0].description || 'Erro ao criar pagamento junto ao banco.', status: 400, };
  }
};

export const createPayment = async ({ domain, formData, pack, type, }: FormDataProp): Promise<ResponseType> => {
  const profileResponse = await getProfile();

  if (!profileResponse.data) return { message: UNAUTHORIZED, status: 401, };

  const packFromDatabase = await getPack({ id: pack.id, });

  if (packFromDatabase.error) return { message: 'Não achamos o pacote selecionado.', status: 401, };

  if (packFromDatabase.data) {
    if (!profileResponse.data.asId) return { message: 'Usuário sem conta no asaas.', status: 400, };

    let body: Body = await getBody({
      asId: profileResponse.data.asId,
      type,
      pack,
      ...(formData?.installments && formData.installments > 1 && {
        installmentCount: formData.installments,
        totalValue: pack.price,
      }),
    });

    if (type === 'CREDIT_CARD') {
      const creditCardInfo = getCreditCardInfo({ formData, });
      body = { ...body, ...creditCardInfo, };
    }

    const assasPaymentResponse = await createAsaasPayment({ domain, body, });

    if (assasPaymentResponse.status !== 200) {
      return { message: assasPaymentResponse.message || 'Não foi possível realizar o pagamento.', status: 400, };
    }
    revalidatePath('/compras');
    return { data: assasPaymentResponse.data, status: 201, };
  }
  return { message: 'Não foi possível realizar o pagamento.', status: 400, };
};

export const getPack = async ({ id, }: { id: string }) => {
  const supabaseAdmin = createClientAdmin();
  const { data, error, }: PostgrestMaybeSingleResponse<Pack> = await supabaseAdmin
    .from('pack')
    .select()
    .match({ id, })
    .maybeSingle();

  return { data, error, };
};

export const getPackService = async ({ id, }: { id: string }): Promise<{ data: PackService[] | null, status: 200 } | { message: string, status: 400 }> => {
  const supabaseAdmin = createClientAdmin();
  const { data, } = await supabaseAdmin
    .from('service_pack')
    .select()
    .eq('packId', id);

  if (data) return { data, status: 200, };
  return { message: 'Erro ao buscar serviços do pacote.', status: 400, };
};

export const getProfile = async () => {
  const user = await userInfo();

  if (!user) return { message: 'Não foi possível encontrar o usuário.', status: 400, };

  const supabaseAdmin = createClientAdmin();
  const { data, } = await supabaseAdmin
    .from('profile')
    .select()
    .match({ id: user.id, })
    .maybeSingle();

  if (data) return { data, status: 200, };
  return { message: 'Não foi possível encontrar o usuário.', status: 400, };
};

export const hidePack = async ({ packId, }: { packId: string }) => {
  const supabase = createClientAdmin();
  const { data, error, } = await supabase
  .from('pack')
  .update({
    active: false,
  })
  .match({ id: packId, });

  console.log('🚀 ~ deletePack ~ error:', error);
  if (error) return {
    message: 'Erro ao desativar pacote.', status: 400,
  };

  return {
    status: 200,
  };
};

const getErrorMessage = ({ error, service, }: GetErrorMessageType) => {
  return error && error.message && error.message in dictionary
      ? dictionary[error.message]
      : `Não foi possível salvar ${service}.`;
};