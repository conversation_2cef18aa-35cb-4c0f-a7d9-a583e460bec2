'use client';

import { Button, } from '@/components/index';
import React, { useState, } from 'react';
import { toast, } from '@/components/ui/shard/use-toast';
import { hidePack, } from '../actions';

const HidePack = ({ packId, }: { packId: string }) => {
  const [label, setLabel,] = useState('Deletar');
  const onClick = async () => {
    setLabel('Desativando');
    const response = await hidePack({ packId, });
    if (response.status === 200) {
      toast({
        title: 'Pacote não será mais mostrado pros alunos.',
      });
    } else {
      toast({
        variant: 'destructive',
        title: response.message,
      });
      setLabel('Desativar');
    }
  };

  return (
    <Button
      variant='secondary'
      onClick={onClick}
      className='w-full'
    >
      Desativar
    </Button>
  );
};

export default HidePack;