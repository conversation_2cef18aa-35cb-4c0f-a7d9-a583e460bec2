import { <PERSON><PERSON>, Card, CardContent, CardDescription, Card<PERSON>ooter, CardTitle, Separator, } from '@/components/index';
import { Pack, Service, } from '@/utils/constants/types';
import { formatToBrazilianReal, } from '@/lib/utils';
import { Check, } from 'lucide-react';
import Link from 'next/link';
import React from 'react';
import HidePack from './hide-pack';

const Use = ({ isAdminPage, pack, }: { isAdminPage?: boolean, pack: Pack & { services: Service[]  | undefined } }) => {
  console.log('🚀 ~ Use ~ pack:', pack);
  return (
    <Card key={pack.id} className='flex flex-col w-full max-w-96 h-fit'>
      <div className="py-4 p-5 flex flex-row justify-between items-center">
        <CardTitle>{pack.name}</CardTitle>
        <CardTitle>{formatToBrazilianReal(pack.price)}</CardTitle>
      </div>
      <Separator />
      <CardContent className='flex flex-col p-6 gap-4'>
        <div className="flex items-center">
          <Check className="mr-2 h-4 w-4 text-primary" />
          <span>Direito a {pack.use} aula(s)</span>
        </div>
        {pack.services && pack.services.map(service => (
          <div key={service.id} className="flex items-center">
            <Check className="mr-2 h-4 w-4 text-primary" />
            <span>{service.title}</span>
          </div>
        ))}
        <div className="flex items-center">
          <Check className="mr-2 h-4 w-4 text-primary" />
          <span>Acompanhamento do professor</span>
        </div>
        <div className="flex items-center">
          <Check className="mr-2 h-4 w-4 text-primary" />
          <span>Acesso a todos os equipamentos</span>
        </div>
        <div className="flex items-center">
          <Check className="mr-2 h-4 w-4 text-primary" />
          <span>Acompanhe seu progresso</span>
        </div>
        <CardDescription className='text-xs text-center mx-auto block'>Válido por {pack.expires} dias.</CardDescription>
      </CardContent>
      <CardFooter className='flex flex-col space-y-2'>
        <Link className='w-full' href={`/pacotes/${pack.id}`}><Button className='w-full'>{isAdminPage ? 'Editar' : 'Comprar'}</Button></Link>
        {isAdminPage && <HidePack packId={pack.id} />}
      </CardFooter>
    </Card>
  );
};

export default Use;