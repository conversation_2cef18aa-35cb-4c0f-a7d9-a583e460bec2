'use client';

import { Button, NothingFound, } from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import React, { useState, } from 'react';
import { createPayment, } from '../../actions';
import { BillingType, Pack, Profile, Subdomains, } from '@/utils/constants/types';
import SeeInvoice from '../../../compras/components/see-invoice';

type Params = {
  domain: Subdomains,
  pack: Pack,
  type: Exclude<BillingType, 'CREDIT_CARD'>
  profile: Profile
}

const BoletoPix = ({ domain, pack, type, profile, }: Params) => {
  const [label, setLabel,] = useState<string>(type === 'BOLETO' ? 'Gerar Boleto' : 'Gerar PIX');
  const [invoiceUrl, setInvoiceUrl,] = useState<string | null>(null);

  const submit = async () => {
    setLabel('Gerando pagamento');
      const responseCreatePayment = await createPayment({ domain, pack, type, });
      if (responseCreatePayment.status === 201) {
        toast({
          description: 'Pagamento enviado.',
        });
        setLabel('Pagamento enviado');
        setInvoiceUrl(responseCreatePayment.data.invoiceUrl);
      } else {
        toast({
          variant: 'destructive',
          description: responseCreatePayment.message,
        });
        setLabel('Gerar pagamento');
      }
  };

  {!profile.asId && (
    <NothingFound label='Usuário não tem conta no banco provedor.' />
  );}

  return (
    <div className="space-y-4">
      <p>
        Iremos enviar um email com dados do {type === 'PIX' ? 'pix': 'boleto' } pra você.<br/>
        Quando verificarmos o pagamento sua compra será ativada.
      </p>
      {invoiceUrl && (
        <SeeInvoice url={invoiceUrl} />
      )}
      <Button onClick={submit} className='w-full'>{label}</Button>
    </div>
  );
};

export default BoletoPix;