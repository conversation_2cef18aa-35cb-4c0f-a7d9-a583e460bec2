'use client';

import { ChangeEvent, useState, } from 'react';
import { useF<PERSON>, Controller, } from 'react-hook-form';
import { zodResolver, } from '@hookform/resolvers/zod';
import { z, } from 'zod';
import {
  Button,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/index';
import { formatCep, formatPhoneNumber, handleCpfChange, validateCPF, } from '@/lib/utils';
import { Pack, Subdomains, } from '@/utils/constants/types';
import { toast, } from '@/components/ui/shard/use-toast';
import { useRouter, } from 'next/navigation';
import { createPayment, } from '../../actions';

export const formSchema = z.object({
  // Credit Card Data
  holderName: z.string({ required_error: 'Campo obrigatório.', }),
  cardNumber: z.string().regex(/^\d{16}$/, 'Número precisa ter 16 dígitos.'),
  expiryMonth: z.string().regex(/^(0[1-9]|1[0-2])$/, 'Vencimento inválido.'),
  expiryYear: z.string().regex(/^\d{4}$/, 'Vencimento inválido'),
  ccv: z.string().regex(/^\d{3}$/, 'CCV precisa ter 3 dígitos.'),

  // User Data
  name: z.string({ required_error: 'Campo obrigatório.', }).min(4, { message: 'Deve ter no mínimo 4 letras', }),
  email: z.string().email('Email inválido.'),
  cpf: z.string({ required_error: 'Campo obrigatório.', }).regex(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/, 'Formato inválido.').refine(validateCPF, { message: 'CPF inválido.',  }),
  cep: z.string({ required_error: 'Campo obrigatório.', }).length(9, { message: 'CEP deve ter 9 caracteres.', }),
  phone: z.string({ required_error: 'Campo obrigatório.', }).min(14, { message: 'Telefone deve ter pelo menos 14 caracteres.', }),
  addressNumber: z.string({ required_error: 'Campo obrigatório.', }),
  installments: z.coerce.number({ required_error: 'Campo obrigatório.', }).optional(),
});

export type FormData = z.infer<typeof formSchema>

export default function PaymentForm({ domain, pack, }: { domain: Subdomains, pack: Pack, }) {
  const [label, setLabel,] = useState('Comprar');
  const router = useRouter();
  const { control, handleSubmit, formState: { errors, }, setValue, } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      holderName: '',
      cardNumber: '',
      expiryMonth: '',
      expiryYear: '',
      ccv: '',
      name: '',
      email: '',
      cpf: '',
      cep: '',
      phone: '',
      addressNumber: '',
    },
  });

  const handlePhoneChange = (e: ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    setValue('phone', formatted, { shouldValidate: true, });
  };

  const onCpfChange = (e: ChangeEvent<HTMLInputElement>) => {
    const formattedValue = handleCpfChange(e.target.value);
    setValue('cpf', formattedValue, { shouldValidate: true, });
  };

  const onCepChange = (e: ChangeEvent<HTMLInputElement>) => {
    const formattedValue = formatCep(e.target.value);
    setValue('cep', formattedValue, { shouldValidate: true, });
  };

  const onSubmit = async (data: FormData) => {
    setLabel('Enviando');
    const response = await createPayment({
      domain,
      formData: data,
      pack,
      type: 'CREDIT_CARD',
    });
    if (response.status === 201) {
      toast({
        description: 'Assinatura criada com sucesso.',
      });
      setLabel('Enviado');
      router.push('/compras');
    } else {
      toast({
        variant: 'destructive',
        description: response.message,
      });
      setLabel('Comprar');
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="w-full">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Informações do cartão de crédito</CardTitle>
          <CardDescription>Preencha com detalhes do seu cartão</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="holderName">Nome no cartão</Label>
            <Controller
              name="holderName"
              control={control}
              render={({ field, }) => (
                <Input id="holderName" {...field} />
              )}
            />
            {errors.holderName && <p className="text-sm text-red-500">{errors.holderName.message}</p>}
          </div>
          <div className="space-y-2">
            <Label htmlFor="cardNumber">Número do cartão</Label>
            <Controller
              name="cardNumber"
              control={control}
              render={({ field, }) => (
                <Input id="cardNumber" {...field} />
              )}
            />
            {errors.cardNumber && <p className="text-sm text-red-500">{errors.cardNumber.message}</p>}
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="expiryMonth">Vencimento</Label>
              <Controller
                name="expiryMonth"
                control={control}
                render={({ field, }) => (
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <SelectTrigger id="expiryMonth">
                      <SelectValue placeholder="Mês" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12, }, (_, i) => i + 1).map(month => (
                        <SelectItem key={month} value={month.toString().padStart(2, '0')}>
                          {month.toString().padStart(2, '0')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.expiryMonth && <p className="text-sm text-red-500">{errors.expiryMonth.message}</p>}
            </div>
            <div className="mt-8">
              <Controller
                name="expiryYear"
                control={control}
                render={({ field, }) => (
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <SelectTrigger id="expiryYear">
                      <SelectValue placeholder="Ano" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 10, }, (_, i) => new Date().getFullYear() + i).map(year => (
                        <SelectItem key={year} value={year.toString()}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.expiryYear && <p className="text-sm text-red-500">{errors.expiryYear.message}</p>}
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="ccv">Código de segurança</Label>
            <Controller
              name="ccv"
              control={control}
              render={({ field, }) => (
                <Input id="ccv" {...field} />
              )}
            />
            {errors.ccv && <p className="text-sm text-red-500">{errors.ccv.message}</p>}
          </div>
          {pack.installments !== null &&
            <div className="mt-8">
              <Controller
                name="installments"
                control={control}
                render={({ field, }) => (
                  <Select onValueChange={field.onChange}>
                    <SelectTrigger id="installments">
                      <SelectValue placeholder="Parcelas" />
                    </SelectTrigger>
                    <SelectContent>
                      {pack.installments !== null && Array.from({ length: pack.installments + 1, }, (_, i) => i).map((_: any, index: number) => {
                        if (index === 0) return;
                        return (
                          <SelectItem key={index} value={index.toString()}>
                            {index}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.expiryYear && <p className="text-sm text-red-500">{errors.expiryYear.message}</p>}
            </div>
          }
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Informações titular do cartão</CardTitle>
          <CardDescription>Preencha com os detalhes pessoais do titular do cartão</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Nome</Label>
            <Controller
              name="name"
              control={control}
              render={({ field, }) => (
                <Input id="name" {...field} />
              )}
            />
            {errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Controller
              name="email"
              control={control}
              render={({ field, }) => (
                <Input id="email" type="email" {...field} />
              )}
            />
            {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Telefone</Label>
            <Controller
              name="phone"
              control={control}
              render={({ field, }) => (
                <Input
                  id="phone"
                  type="phone"
                  {...field}
                  onChange={handlePhoneChange}
                />
              )}
            />
            {errors.phone && <p className="text-sm text-red-500">{errors.phone.message}</p>}
          </div>
          <div className="flex flex-col gap-4">
            <div className="space-y-2">
              <Label htmlFor="cpf">CPF</Label>
              <Controller
                name="cpf"
                control={control}
                render={({ field, }) => (
                  <Input
                    id='cpf'
                    {...field}
                    onChange={onCpfChange}
                    maxLength={14}
                  />
                )}
              />
              {errors.cpf && <p className="text-sm text-red-500">{errors.cpf.message}</p>}
            </div>
            <div className="space-y-2">
              <Label htmlFor="cep">CEP</Label>
              <Controller
                name="cep"
                control={control}
                render={({ field, }) => (
                  <Input
                    id="cep" {...field}
                    onChange={onCepChange}
                  />
                )}
              />
              {errors.cep && <p className="text-sm text-red-500">{errors.cep.message}</p>}
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="addressNumber">Número da moradia</Label>
            <Controller
              name="addressNumber"
              control={control}
              render={({ field, }) => (
                <Input id="addressNumber" {...field} />
              )}
            />
            {errors.addressNumber && <p className="text-sm text-red-500">{errors.addressNumber.message}</p>}
          </div>
        </CardContent>
      </Card>

      <CardFooter>
        <Button type="submit" className="w-full">{label}</Button>
      </CardFooter>
    </form>
  );
}

