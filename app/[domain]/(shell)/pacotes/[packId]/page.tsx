'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/index';
import { formatToBrazilianReal, } from '@/lib/utils';
import PaymentForm from './components/payment-form';
import BoletoPix from './components/boleto-pix';
import { Pack, PackService, Profile, Subdomains, } from '@/utils/constants/types';
import CreateProductForm from '../../criar-pacote/page';
import { useEffect, useState, } from 'react';
import Loading from '../loading';
import { getPack, getPackService, getProfile, } from '../actions';

const Packs = ({ params: { domain, packId, }, }: { params: { domain: Subdomains, packId: string } }) => {
  const [activeTab, setActiveTab,] = useState('credit-card');
  const [profile, setProfile,] = useState<Profile | null>(null);
  const [pack, setPack,] = useState<Pack | null>(null);
  const [services, setServices,] = useState<PackService[] | null>(null);

  useEffect(() => {
    (async () => {
      const profileResponse = await getProfile();
      if (profileResponse.status === 200) {
        setProfile(profileResponse.data);
      }

      const packResponse = await getPack({ id: packId, });
      if (packResponse.data) {
        setPack(packResponse.data);
      }

      const servicesResponse = await getPackService({ id: packId, });
      if (servicesResponse.status === 200) {
        setServices(servicesResponse.data);
      }
    })();
  }, []);

  if (!profile || !pack) return <Loading />;

  if (profile.type === 'admin' && pack && services) {
    return <CreateProductForm product={{ ...pack, services, }} />;
  }

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">Finalizar Compra</CardTitle>
        <CardDescription className="text-center">
          Você está contratando o pacote <span className="font-semibold">{pack.name}</span> no valor de{' '}
          <span className="font-semibold">{formatToBrazilianReal(pack.price)}</span>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="credit-card">Cartão</TabsTrigger>
            <TabsTrigger value="boleto">Boleto</TabsTrigger>
            <TabsTrigger value="pix">Pix</TabsTrigger>
          </TabsList>
          <TabsContent value="credit-card">
            <PaymentForm pack={pack} domain={domain} profile={profile} />
          </TabsContent>
          <TabsContent value="boleto">
            <BoletoPix domain={domain} pack={pack} profile={profile} type="BOLETO" />
          </TabsContent>
          <TabsContent value="pix">
            <BoletoPix domain={domain} pack={pack} profile={profile} type="PIX" />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default Packs;