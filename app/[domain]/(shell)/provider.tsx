'use client';

import { subscribeUser, } from '@/app/actions';
import SubscribePrompt from '@/app/subscribe-prompt';
import { toast, } from '@/components/ui/shard/use-toast';
import { useEffect, useState, } from 'react';

function urlBase64ToUint8Array(base64String: string) {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

const PushNotificationManager = () => {
  const [isSupported, setIsSupported,] = useState(false);
  const [subscription, setSubscription,] = useState<PushSubscription | null | boolean>(true);
  const [showPrompt, setShowPrompt,] = useState(true);

  useEffect(() => {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      setIsSupported(true);
      registerServiceWorker();
    }
  }, []);

  async function registerServiceWorker() {
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/',
      updateViaCache: 'none',
    });
    const sub = await registration.pushManager.getSubscription();
    setSubscription(sub);
  }

  async function subscribeToPush() {
    const registration = await navigator.serviceWorker.ready;
    const url = urlBase64ToUint8Array(
      process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!
    )
    const sub = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: url,
    });
    setShowPrompt(false);
    setSubscription(sub);
    setShowPrompt(false);
    const response = await subscribeUser(sub);
    if (response.status !== 200) {
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
  }
  // async function unsubscribeFromPush() {
  //   await subscription?.unsubscribe();
  //   setSubscription(null);
  //   await unsubscribeUser();
  // }

  return (
    <>
      {!subscription && isSupported && showPrompt && (
        <SubscribePrompt
          onSubscribe={subscribeToPush}
          onCancel={() => setShowPrompt(false)}
          onDontAsk={() => setShowPrompt(false)}
        />
      )}
    </>
  );
};

export default PushNotificationManager;