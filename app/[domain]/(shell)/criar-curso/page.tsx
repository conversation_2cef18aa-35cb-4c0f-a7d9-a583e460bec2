'use client';

import { useForm, useFieldArray, } from 'react-hook-form';
import { zodResolver, } from '@hookform/resolvers/zod';
import { Plus, } from 'lucide-react';
import { courseSchema, CourseFormData, } from './schema';
import {
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Switch,
} from '@/components/index';
import ModuleForm from './components/module';
import { saveCourse, } from './actions';
import { toast, } from '@/components/ui/shard/use-toast';
import { useRouter, } from 'next/navigation';
import ImageUploader from './components/img-uploader';
import { Subdomains, } from '@/utils/constants/types';

export default function CourseCreator({ params: { domain, }, }: { params: { domain: Subdomains } }) {
  const router = useRouter();

  const form = useForm<CourseFormData>({
    resolver: zodResolver(courseSchema),
    defaultValues: {
      name: '',
      description: '',
      published: false,
      modules: [],
    },
  });

  const { fields: moduleFields, append: appendModule, remove: removeModule, } = useFieldArray({
    control: form.control,
    name: 'modules',
  });

  const onSubmit = async (data: CourseFormData) => {
    const response = await saveCourse({ formData: data, });
    if (response.status === 200) {
      toast({
        title: 'Curso salvo com sucesso.',
      });
      router.push('/cursos');
    } else {
      toast({
        variant: 'destructive',
        title: response.message,
      });
    }
  };

  return (
    <Form {...form} >
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Curso</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <FormField
                control={form.control}
                name='name'
                render={({ field, }) => (
                  <FormItem>
                    <FormControl>
                      <Input {...field} placeholder='Nome do curso' />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='description'
                render={({ field, }) => (
                  <FormItem>
                    <FormControl>
                      <Input {...field} placeholder='Descrição' />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name='published'
                control={form.control}
                render={({ field, }) => (
                  <FormItem>
                    <div className='flex items-center gap-4'>
                      <Switch
                        id='published'
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        />
                        <FormLabel>Ativo</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
              <ImageUploader domain={domain} onImageLoad={(url) => { console.log(url); form.setValue('featureImg', url); }} />
            </div>
          </CardContent>
        </Card>

        <div className='space-y-4'>
          {moduleFields.map((_, moduleIndex) => (
            <ModuleForm
              key={moduleIndex}
              form={form}
              moduleIndex={moduleIndex}
              removeModule={removeModule}
            />
          ))}
          <Button variant='outline' type='button' onClick={() => appendModule({
            name: '',
            description: '',
            lessons: [],
          })}>
            <Plus className='h-4 w-4 mr-2' /> Adicionar módulo
          </Button>
        </div>
        <Button type='submit' size='lg'>Salvar curso</Button>
      </form>
    </Form>
  );
}

