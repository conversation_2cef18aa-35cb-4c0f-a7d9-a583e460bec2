'use server';

import { Course, Lesson, Module, Video, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import Mux from '@mux/mux-node';
import { UNAUTHORIZED, } from '@/utils/constants';
import { CourseFormData, } from '../schema';
import dictionary from '@/lib/dictionary';

type Response = {
  data: {
    url: string,
    videoId: string,
  },
  status: 200
} | {
  message: string,
  status: 400 | 401
}

export const getDirectUploadUrl = async (): Promise<Response> => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (user?.user_metadata.role !== 'admin') return {
    message: UNAUTHORIZED, status: 401,
  };

  const supabaseAdmin = createClientAdmin();

  const { data: video, }: { data: Video | null } = await supabaseAdmin
    .from('video')
    .insert({  schoolId: user?.user_metadata.schoolId, })
    .select()
    .maybeSingle();

  if (video) {
    const client = new Mux({
      tokenId: process.env['MUX_TOKEN_ID'],
      tokenSecret: process.env['MUX_TOKEN_SECRET'],
    });
    const directUpload = await client.video.uploads.create({
      cors_origin: '*',
      new_asset_settings: {
        video_quality: 'basic',
        max_resolution_tier: '1080p',
        passthrough: video.id,
        playback_policy: ['public',],
      },
    });
    if (!directUpload?.url) {
      return { message: 'Algum erro aconteceu ao configurar vídeo.', status: 400, };
    }
    return { data: { url: directUpload.url, videoId: video.id }, status: 200, };
  }

  return { message: 'Algum erro aconteceu ao configurar vídeo.', status: 400, };
};

type ResponseCreateCourse = {
  status: 200
} | {
  message: string,
  status: 400 | 401
}

export const saveCourse = async ({ formData, }: { formData: CourseFormData }): Promise<ResponseCreateCourse> => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();

  if (user?.user_metadata.role !== 'admin') {
    return {
      message: UNAUTHORIZED, status: 401,
    };
  }

  const supabaseAdmin = createClientAdmin();

  const { modules, ...courseFormData } = formData;
  console.log("🚀 ~ saveCourse ~ modules:", modules)

  const { data: course, error, }: { data: Course | null } = await supabaseAdmin
  .from('course')
  .insert({
    ...courseFormData,
    schoolId: user.user_metadata.schoolId,
    slug: courseFormData.name.replaceAll(' ', '-'),
  })
  .select()
  .limit(1)
  .single();
  console.log('🚀 ~ saveCourse ~ error:', error);

  if (error) {
    return {
      message: dictionary[error.message] || 'Erro ao salvar curso.',
      status: 400,
    };
  }

  if (!course) return {
    message: 'Erro ao salvar curso.',
    status: 400,
  };

  modules.forEach(async moduleForm => {
    const { lessons, ...moduleFormData } = moduleForm;
    const { data: module, }: { data: Module | null } = await supabaseAdmin
    .from('module')
    .insert({
      ...moduleFormData,
      courseId: course.id,
    })
    .select()
    .limit(1)
    .single();

    console.log('🚀 ~ saveCourse ~ lessons:', lessons);
    console.log('🚀 ~ saveCourse ~ module:', module);
    if (module) {
      lessons.forEach(async lessonForm => {
        const { data, error, }: { data: Lesson | null } = await supabaseAdmin
          .from('lesson')
          .insert({
            ...lessonForm,
            courseId: course.id,
            moduleId: module.id,
            slug: lessonForm.name.replaceAll(' ', '-'),
          })
          .select()
          .limit(1)
          .single();

        console.log('🚀 ~ saveCourse ~ error:', error);
        console.log('🚀 ~ saveCourse ~ data:', data);
        if (error) {
          return {
            message: dictionary[error.message] || 'Erro ao salvar aula.',
            status: 400,
          };
        }
      });
    }
  });


  return { status: 200, };
};