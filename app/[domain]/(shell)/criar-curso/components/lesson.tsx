'use client';

import { useEffect, useState, } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Input,
} from '@/components/index';
import MuxUploader, {
  MuxUploaderDrop,
  MuxUploaderFileSelect,
  MuxUploaderProgress,
  MuxUploaderStatus,
} from '@mux/mux-uploader-react';
import { getDirectUploadUrl, } from '../actions';
import { Trash2, } from 'lucide-react';
import { UseFieldArrayRemove, UseFormReturn, } from 'react-hook-form';
import { toast, } from '@/components/ui/shard/use-toast';
import { CourseFormData, } from '../schema';

type Props = { form: UseFormReturn<CourseFormData>, lessonIndex: number, moduleIndex: number, removeLesson: UseFieldArrayRemove }

const LessonForm = ({ form, removeLesson, lessonIndex, moduleIndex, }: Props) => {
  const [url, setUrl,] = useState<string | null>(null);

  useEffect(() => {
    (async () => {
      const response = await getDirectUploadUrl();
      if (response.status === 200) {
        setUrl(response.data.url);
        form.setValue(`modules.${moduleIndex}.lessons.${lessonIndex}.videoId`, response.data.videoId);
      } else {
        toast({
          variant: 'destructive',
          title: response.message,
        });
      }
    })();
  }, []);

  return (
    <Card className='w-full p-2 h-full'>
      <CardHeader>
        <CardTitle>Aula</CardTitle>
        <CardDescription>Informações da aula.</CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='flex items-center'>
          <FormField
            control={form.control}
            name={`modules.${moduleIndex}.lessons.${lessonIndex}.name`}
            render={({ field, }) => (
              <FormItem className='w-full'>
                <FormControl>
                  <Input {...field} placeholder='Nome' />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button variant="ghost" size="sm" type='button' onClick={() => removeLesson(moduleIndex)}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>

        <FormField
          control={form.control}
          name={`modules.${moduleIndex}.lessons.${lessonIndex}.description`}
          render={({ field, }) => (
            <FormItem>
              <FormControl>
                <Input {...field} placeholder='Descrição' />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`modules.${moduleIndex}.lessons.${lessonIndex}.sequence`}
          render={({ field, }) => (
            <FormItem>
              <FormControl>
                <Input {...field} onChange={(e) => field.onChange(e.target.value)} placeholder='Sequência' />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="w-full h-full">
          <MuxUploader id={`modules.${moduleIndex}.lessons.${lessonIndex}.name-uploader`} className="hidden" endpoint={url} />
          <MuxUploaderDrop
            id={`modules.${moduleIndex}.lessons.${lessonIndex}.name-uploader`}
            muxUploader={`modules.${moduleIndex}.lessons.${lessonIndex}.name-uploader`}
            className="border border-slate-200 rounded shadow mb-4 space-y-2"
            overlay
            overlayText="Soltar"
          >
            <span slot="heading" className='sm:text-xl'>Arraste o vídeo pra cá</span>
            <span slot="separator"></span>

            <MuxUploaderFileSelect muxUploader={`modules.${moduleIndex}.lessons.${lessonIndex}.name-uploader`}>
              <Button type= 'button'>Selecionar arquivo</Button>
            </MuxUploaderFileSelect>
            <MuxUploaderProgress
              type="percentage"
              muxUploader={`modules.${moduleIndex}.lessons.${lessonIndex}.name-uploader`}
            />
            <MuxUploaderStatus muxUploader={`modules.${moduleIndex}.lessons.${lessonIndex}.name-uploader`}>
              <span slot='heading'>Salvando</span>
            </MuxUploaderStatus>
          </MuxUploaderDrop>
        </div>
      </CardContent>
    </Card>
  );
};

export default LessonForm;
