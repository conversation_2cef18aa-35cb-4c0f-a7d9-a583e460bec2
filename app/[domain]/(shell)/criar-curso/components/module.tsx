'use client';

import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  FormControl,
  FormField,
  FormItem,
  Input,
} from '@/components/index';
import { FormMessage, } from '@/components/ui/shard/form';
import LessonForm from './lesson';
import { Plus, Trash2, } from 'lucide-react';
import { useFieldArray, UseFieldArrayRemove, UseFormReturn, } from 'react-hook-form';
import { CourseFormData, } from '../schema';

type Props = { form: UseFormReturn<CourseFormData>, moduleIndex: number, removeModule: UseFieldArrayRemove }

const ModuleForm = ({ form, moduleIndex, removeModule, }: Props) => {

  const { fields: lessonFields, append: appendLesson, remove: removeLesson, } = useFieldArray({
    control: form.control,
    name: `modules.${moduleIndex}.lessons`,
  });

  return (
    <Card className='w-full h-full'>
      <CardHeader>
        <CardTitle>Módulo</CardTitle>
        <CardDescription>Agrupe as aulas em um módulo.</CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='flex items-center'>
          <FormField
            control={form.control}
            name={`modules.${moduleIndex}.name`}
            render={({ field, }) => (
              <FormItem className='w-full'>
                <FormControl>
                  <Input {...field} placeholder='Nome' />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button variant="ghost" size="sm" onClick={() => removeModule(moduleIndex)}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>

        <FormField
          control={form.control}
          name={`modules.${moduleIndex}.description`}
          render={({ field, }) => (
            <FormItem>
              <FormControl>
                <Input {...field} placeholder='Descrição' />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {lessonFields.map((_, lessonIndex) => (
          <LessonForm
            key={lessonIndex}
            form={form}
            lessonIndex={lessonIndex}
            moduleIndex={moduleIndex}
            removeLesson={removeLesson}
          />
        ))}
        <Button variant='outline' type='button' size='sm' onClick={() => appendLesson({
          name: '',
          description: '',
        })}>
          <Plus className="h-4 w-4 mr-2" /> Adicionar aula
        </Button>
      </CardContent>
    </Card>
  );
};

export default ModuleForm;
