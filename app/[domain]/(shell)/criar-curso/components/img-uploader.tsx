'use client';

import { ChangeEvent, useState, } from 'react';
import { Loader2, } from 'lucide-react';
import NextImage from 'next/image';
import { Input, Label, } from '@/components/index';
import { Subdomains, } from '@/utils/constants/types';

const ImageUploader = ({ domain, onImageLoad, }: { domain: Subdomains, onImageLoad: (url: string) => void, }) => {
  const [selectedImage, setSelectedImage,] = useState<string | null>();
  const [loadingImg, setLoadingImg,] = useState(false);
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const selectImage = (event: ChangeEvent<HTMLInputElement>) => {
    setLoadingImg(true);
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = async () => {
        const img = new Image();
        img.src = reader.result as string;
        img.onload = async () => {
          const formData = new FormData();
          formData.append('image', file);
          formData.append('domain', domain);
          formData.append('folder', 'courses');
          const responseSaveInBucket = await fetch('/api/storage', {
            method: 'POST',
            body: formData,
          });
          if(responseSaveInBucket.status === 200) {
            const avatar = await responseSaveInBucket.json();
            console.log('🚀 ~ img.onload= ~ avatar:', avatar);
            const imgUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${avatar.fullPath}`;
            console.log('🚀 ~ img.onload= ~ imgUrl:', imgUrl);
            setLoadingImg(false);
            setSelectedImage(imgUrl);
            onImageLoad(imgUrl);
          }
        };
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="w-full">
      <Label htmlFor="image-upload" className="sr-only">Selecione imagem</Label>
      <div className="flex items-center justify-start relative">
        <Input
          id="image-upload"
          type="file"
          accept="image/*"
          onChange={selectImage}
          className="hidden"
        />
        <Label
          htmlFor="image-upload"
          className="cursor-pointer border-2 border-dashed rounded-md hover:border-primary"
        >
          {(selectedImage) ? (
            <div className="relative w-full h-full">
              <NextImage
                src={selectedImage}
                alt='Selecione foto do curso'
                className="rounded-md"
                id='course-image'
                height={200}
                width={200}
              />
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center space-y-4 p-4">
              {loadingImg && (
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              )}
              <p className="text-sm text-gray-500">Imagem do curso</p>
            </div>
          )}
        </Label>
      </div>
    </div>
  );
};

export default ImageUploader;