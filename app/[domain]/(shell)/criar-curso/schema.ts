import { z, } from 'zod';

export const lessonSchema = z.object({
  name: z.string( { required_error: 'Nome é obrigatório.', }).max(100, 'Nome precisa ter menos que 100 letras.'),
  description: z.string().max(255, 'Descrição precisa ter menos de 255 letras.').optional(),
  sequence: z.string().optional(),
  videoId: z.string().uuid(),
});

export const moduleSchema = z.object({
  name: z.string( { required_error: 'Nome é obrigatório.', }).max(100, 'Nome precisa ter menos que 100 letras.'),
  description: z.string().max(255, 'Descrição precisa ter menos de 255 letras.').optional(),
  lessons: z.array(lessonSchema),
});

export const courseSchema = z.object({
  name: z.string( { required_error: 'Nome é obrigatório.', }).max(100, 'Nome precisa ter menos que 100 letras.'),
  description: z.string().max(255, 'Descrição precisa ter menos de 255 letras.').optional(),
  published: z.boolean(),
  featureImg: z.string().optional(),
  modules: z.array(moduleSchema),
});

export type LessonFormData = z.infer<typeof lessonSchema>
export type ModuleFormData = z.infer<typeof moduleSchema>
export type CourseFormData = z.infer<typeof courseSchema>

