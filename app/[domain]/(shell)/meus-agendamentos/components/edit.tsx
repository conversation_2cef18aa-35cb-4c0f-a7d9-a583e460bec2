'use client';

import {
  Button,
  useToast,
} from '@/components/index';
import { ax, } from '@/lib/utils';
import { GroupedData, Schedule, Service, } from '@/utils/constants/types';
import { URLS, } from '@/utils/supabase/constants';
import { useEffect, useState, } from 'react';
import { format, getDay, } from 'date-fns';
import { EditIcon, } from 'lucide-react';
import DaySelector from '@/components/ui/day-selector';
import TeacherSelect from '@/components/ui/teacher-selector';
import EquipmentSelector from '@/components/ui/equipment-selector';
import HoursSelector from '@/components/ui/hours-selector';

const Edit = ({ code, service, }: { code: string, service: Service }) => {
  const [showForm, setShowForm,] = useState(false);
  const [selectedDate, setSelectedDate,] = useState<Date | undefined>();
  const [saveLabel, setSaveLabel,] = useState('Atualizar');
  const [selectedEquipmentIds, setSelectedEquipmentsIds,] = useState<string[]>([]);
  const [schedules, setSchedules,] = useState<Schedule[]>([]);
  const [selectedScheduleId, setSelectedScheduleId,] = useState<string | null>(null);
  const [teacherId, setTeacherId,] = useState<string | null>();
  const [schedulesAvailable, setSchedulesAvailable,] = useState<GroupedData | null>(null);
  const { toast, } = useToast();

  useEffect(() => {
    (async () => {
      const scheduleResponse = await fetch(`${URLS.SCHEDULE}?serviceId=${service.id}`);
      if (scheduleResponse.status === 200) {
        const data = await scheduleResponse.json();
        setSchedules(data);
      } else {
        const data = await scheduleResponse.json();
        toast({
          variant: 'destructive',
          title: data.message || 'Erro ao buscar horários.',
        });
      }

      const response = await fetch(`${URLS.SCHEDULE_AVAILABLE}?serviceId=${service.id}`);
      if (response.status === 200) {
        const data = await response.json();
        setSchedulesAvailable(data);
      } else {
        const data = await scheduleResponse.json();
        toast({
          variant: 'destructive',
          title: data.message || 'Erro ao buscar horários disponíveis.',
        });
      }

    })();
  }, []);

  const selectEquipment = (equipmentId: string, checked?: string | boolean) => {
    if (checked === 'indeterminate') return;
    if (typeof checked === 'boolean') {
      setSelectedEquipmentsIds(prev =>
        checked ? [...prev, equipmentId,] : prev.filter(id => id !== equipmentId)
      );
    } else {
      if (selectedEquipmentIds.includes(equipmentId)) {
        setSelectedEquipmentsIds(prev => prev.filter(id => id !== equipmentId));
      } else {
        setSelectedEquipmentsIds(prev => [...prev, equipmentId,]);
      }
    }
  };

  const saveBooking = async () => {
    setSaveLabel('Atualizando');
    if (!selectedDate || !selectedScheduleId) {
      toast({
        variant: 'destructive',
        title: 'Preencha data e hora.',
      });
      setSaveLabel('Atualizar');
      return;
    }
    const schedule = schedules.find(schedule => schedule.number === getDay(selectedDate) && schedule.id === selectedScheduleId);
    const response = await ax.patch(URLS.BOOKING_UPDATE, {
      code,
      day: format(selectedDate, 'yyyy-MM-dd'),
      equipmentsIds: selectedEquipmentIds,
      serviceId: service.id,
      status: 'approved',
      schedule,
      teacherId,
    });
    if (response.status === 200) {
      toast({
        title: 'Agendamento atualizado.',
      });
      setShowForm(false);
    } else {
      setSaveLabel('Atualizar');
      toast({
        variant: 'destructive',
        title: response.data.message,
      });
    }
  };

  if (!showForm) return (
    <Button variant="outline" onClick={() => setShowForm(true)} className="w-full mb-2">
      <EditIcon className="w-4 h-4 mr-2" />
      Editar
    </Button>
  );

  return (
    <div className='flex flex-col gap-2 my-6'>
      <DaySelector
        serviceId={service.id}
        onSelect={setSelectedDate}
      />
      <HoursSelector
        schedules={schedules}
        selectedDate={selectedDate}
        schedulesAvailable={schedulesAvailable}
        onSelect={setSelectedScheduleId}
      />
      {service && (
        <TeacherSelect
          service={service}
          selectedScheduleId={selectedScheduleId}
          selectedDate={selectedDate}
          onSelect={setTeacherId}
        />
      )}
      <EquipmentSelector
        selectedEquipmentIds={selectedEquipmentIds}
        serviceId={service.id}
        selectedScheduleId={selectedScheduleId}
        selectedDate={selectedDate}
        onSelect={selectEquipment}
      />
      <Button onClick={saveBooking} className="w-full">
        {saveLabel}
      </Button>
    </div>
  );
};

export default Edit;