'use server';

import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle, } from '@/components/index';
import { CalendarIcon, Clock, GraduationCap, } from 'lucide-react';
import React from 'react';
import Edit from './edit';
import Cancel from './cancel';
import { Subdomains, } from '@/utils/constants/types';
import { bookingStatuses, } from '@/utils/constants';
import { ptBR, } from 'date-fns/locale';
import { format, isPast, parseISO, } from 'date-fns';
import { CustomBooking, } from '../page';
import { getDayAndHourGlobal, } from '@/lib/utils';
import { getIsPastRefundCancelationWindow, } from '@/supabase/verifications/user-info';

const BookingCard = async ({ booking, }: { booking: CustomBooking, domain: Subdomains }) => {
  const isPastRefundCancelationWindowResponse = await getIsPastRefundCancelationWindow({ booking, });
  const isPastRefundCancelationWindow = isPastRefundCancelationWindowResponse.status === 200 && isPastRefundCancelationWindowResponse?.data;

  return (
    <Card key={booking.id}>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <p>{booking.service.title}</p>
          <Badge variant={bookingStatuses[booking.status].color} >{bookingStatuses[booking.status].label}</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="mb-2 font-semibold">Código: {`${booking.code.slice(0, 3)}-${booking.code.slice(3,5)}`.toUpperCase()}</p>
        <p className="mb-2 flex items-center">
          <CalendarIcon className="mr-2 h-4 w-4" />
          <span>{format(parseISO(booking.day), 'dd MMM yyyy', { locale: ptBR, })}</span>
        </p>
        <p className="mb-2 flex items-center">
          <Clock className="mr-2 h-4 w-4" />
          <span>{booking.schedule.hour}</span>
        </p>
        {!booking.service.randomTeacher && (
          <p className="mb-2 flex items-center">
            <GraduationCap className="mr-2 h-5 stroke-[1.5]" />
            <span>{booking.profile.name}</span>
          </p>
        )}
        {booking.equipments && booking.equipments.length > 0 && (
          (
            <p className="mb-4">
              <span>Equipamentos:</span> {booking.equipments.map(e => e.name).join(', ')}
            </p>
          )
        )}
        {!isPast(getDayAndHourGlobal(booking)) && (booking.status === 'approved' || booking.status === 'pending') && (
          <Cancel booking={booking} isPastRefundCancelationWindow={isPastRefundCancelationWindow} />
        )}
        {!isPastRefundCancelationWindow && (booking.status === 'approved' || booking.status === 'pending') && (
          <Edit code={booking.code} service={booking.service} />
        )}
      </CardContent>
    </Card>
  );
};

export default BookingCard;
