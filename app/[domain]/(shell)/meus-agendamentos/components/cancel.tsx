'use client';

import React, { useState, } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  Button,
} from '@/components/index';
import { toast, } from '@/components/ui/shard/use-toast';
import { URLS, } from '@/utils/supabase/constants';
import { CustomBooking, } from '../page';

const Cancel = ({ booking, isPastRefundCancelationWindow, }: { booking: CustomBooking, isPastRefundCancelationWindow: boolean }) => {
  const [label, setLabel,] = useState('Cancelar');
  const [showCancelConfirmation, setShowCancelConfirmation,] = useState(false);

  const cancelBooking = async () => {
    setLabel('Cancelando');
    const response = await fetch(URLS.BOOKING_STATUS, {
      method: 'PATCH',
      body: JSON.stringify({
        code: booking.code,
        status: 'canceled',
      }),
    });
    if (response.status === 200) {
      toast({
        title: 'Agendamento cancelado com sucesso.',
      });
    } else {
      const data = await response.json();
      setLabel('Cancelar');
      toast({
        variant: 'destructive',
        title: data.message,
      });
    }
  };

  const getAlertText = () => {
    if (isPastRefundCancelationWindow) return 'Não haverá reembolso pois não está no prazo descrito na política de cancelamento.';
    return 'O cancelamento está dentro do prazo. Você poderá criar outro agendamento quando quiser.';
  };

  return (
    <div className="flex justify-between">
      <AlertDialog open={showCancelConfirmation} onOpenChange={() => setShowCancelConfirmation(false)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Deseja cancelar o agendamento?</AlertDialogTitle>
            <span className='py-6 leading-tight'>{getAlertText()}</span>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Manter meu agendamento.</AlertDialogCancel>
            <AlertDialogAction className='bg-red-500 hover:bg-red-400' onClick={cancelBooking}>
              Cancelar meu agendamento.
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <Button variant="destructive" onClick={() => setShowCancelConfirmation(true)} className='w-full'>
        {label}
      </Button>
    </div>
  );
};

export default Cancel;