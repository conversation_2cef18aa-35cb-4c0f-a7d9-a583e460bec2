'use server';

import React from 'react';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { NothingFound, Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger, } from '@/components/index';
import { Booking, Params, Profile, Schedule, Service, } from '@/utils/constants/types';
import { redirect, } from 'next/navigation';
import { PostgrestSingleResponse, } from '@supabase/supabase-js';
import { format,} from 'date-fns';
import BookingCard from './components/booking-card';

type CustomEquipment = { name: string, id: string }
export type CustomBooking = Booking & { service: Service, profile: Profile, schedule: Schedule, equipments: CustomEquipment[] | [] }

export default async function MyBookings({ params: { domain, }, }: Params) {
  const supabase = createClient();
  const supabaseAdmin = createClientAdmin();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user) redirect('/login');

  const today = new Date();
  today.setUTCHours(today.getUTCHours() - 3);
  const formatDate = format(today, 'yyyy-MM-dd');

  let { data: pastBookingsData, } = await supabaseAdmin
    .from('booking')
    .select('*, service(*), schedule(*), profile!booking_teacherId_fkey(*)')
    .match({ userId: user.id, })
    .lt('day', formatDate)
    .order('day', { ascending: false, })
    .returns<CustomBooking[] | null>();

  let { data: nextBookingsData, } = await supabaseAdmin
    .from('booking')
    .select('*, service(*), schedule(*), profile!booking_teacherId_fkey(*)')
    .match({ userId: user.id, })
    .gte('day', formatDate)
    .order('day', { ascending: true, })
    .returns<CustomBooking[] | null>();

  if (pastBookingsData === null || nextBookingsData === null) return (
    <NothingFound label='Erro ao buscar agendamentos.' />
  );

  if (!pastBookingsData?.length && !nextBookingsData?.length) return (
    <NothingFound label='Nenhum agendamento encontrado.' />
  );

  const bookings = [...pastBookingsData, ...nextBookingsData,];

  const equipmentsPromise: PostgrestSingleResponse<{ equipment: CustomEquipment }[]>[] = await Promise.all(bookings.map(async booking => {
    return await supabaseAdmin
      .from('booking_equipment')
      .select('equipment(id, name)')
      .match({ bookingId: booking.id, })
      .returns<{ equipment: CustomEquipment }[]>();
  }));

  equipmentsPromise.map(({ data, }, index) => {
    bookings[index] = {
      ...bookings[index],
      equipments: data ? data.map(({ equipment, }) => equipment) : [],
    };
    return data;
  });

  const pastBookings = pastBookingsData.length ? bookings.slice(0, pastBookingsData.length) : [];
  const nextBookings = nextBookingsData.length ? (pastBookings.length ? bookings.slice(pastBookings.length, bookings.length) : bookings) : [];

  return (
    <Tabs defaultValue='next' className="container mx-auto p-4">
      <TabsList>
        <TabsTrigger value='next'>Próximas</TabsTrigger>
        <TabsTrigger value="previous">Passadas</TabsTrigger>
      </TabsList>
      <TabsContent value='next' className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {nextBookings.length === 0 && (
          <NothingFound label='Nenhum agendamento futuro econtrado.' />
        )}
        {nextBookings.map(booking => (
          <BookingCard key={booking.id} booking={booking} domain={domain} />
        )) }
      </TabsContent>
      <TabsContent value="previous" className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {pastBookings.length === 0 && (
          <NothingFound label='Nenhum agendamento passado econtrado.' />
        )}
        {pastBookings.map(booking => (
          <BookingCard key={booking.id} booking={booking} domain={domain} />
        )) }
      </TabsContent>
    </Tabs>
  );
}
