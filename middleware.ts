import { NextRequest, } from 'next/server';
import { updateSession, } from './utils/supabase/middleware';

export const config = {
  matcher: [
    /*
     * Match all paths except for:
     * 1. /api routes
     * 2. /_next (Next.js internals)
     * 3. /_static (inside /public)
     * 4. all root files inside /public (e.g. /favicon.ico)
     */
    '/((?!api/|_next/|_static/|_vercel/|auth/|[\\w-]+\\.\\w+).*)',
  ],
};

export default async function middleware(req: NextRequest) {
  if (req.nextUrl.href !== 'https://meumestre.com.br/') {
    return await updateSession(req);
  }
}
