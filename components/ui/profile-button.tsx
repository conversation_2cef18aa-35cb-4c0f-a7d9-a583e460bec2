'use client';

import { useEffect, useState, } from 'react';
import { useRouter, } from 'next/navigation';
import { createClient, } from '@/utils/supabase/client';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/index';
import Link from 'next/link';
import { CircleUser, } from 'lucide-react';
import { User, } from '@supabase/supabase-js';


export default  function ProfileButton({ user, }: { user: User }) {
  const [userName, setUserName,] = useState<string>();
  const [profileImage, setProfileImage,] = useState('');
  const router = useRouter();
  const supabase = createClient();

  const logout = async () => {
    await supabase.auth.signOut();
  };

  supabase.auth.onAuthStateChange(async (event) => {
    if (event == 'SIGNED_OUT') router.push('/login');
  });

  useEffect(() => {
    const getProfileImage = async() => {
      const response = await fetch('/api/user');
      const user = await response.json();
      setProfileImage(user.profileImage);
      setUserName(user.name);
    };
    getProfileImage();
  }, []);

  return (
    <div className='ml-auto'>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant='secondary' size='icon' className='flex rounded-full'>
            <Avatar>
              <AvatarImage src={(profileImage)} alt='Imagem de perfil do usuário.' className='object-cover' />
                <AvatarFallback>
                  <CircleUser></CircleUser>
                </AvatarFallback>
            </Avatar>
            <span className='sr-only'>Toggle user menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end'>
          <DropdownMenuLabel>{userName}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <Link href='/perfil'><DropdownMenuItem className='cursor-pointer'>Perfil</DropdownMenuItem></Link>
          { user.user_metadata.role === 'admin' && <Link href='/configuracoes'><DropdownMenuItem className='cursor-pointer'>Configurações</DropdownMenuItem></Link>
          }
          { user.user_metadata.role === 'student' && <Link href='/compras'><DropdownMenuItem className='cursor-pointer'>Compras</DropdownMenuItem></Link> }
          <DropdownMenuItem onClick={logout} className='cursor-pointer'>Deslogar</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}