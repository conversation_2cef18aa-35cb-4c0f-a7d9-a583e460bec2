'use client';

import { useRouter, } from 'next/navigation';
import MuxPlayer from '@mux/mux-player-react';
import { LESSON_STATUS, } from '@/utils/constants';
import { useState, } from 'react';
import { Button, } from './shard/button';
import { ProgressTypes, } from '@/utils/constants/types';
import { URLS, } from '@/utils/supabase/constants';

type VideoParams = {
  courseId: number,
  courseSlug: string,
  lessonId: number,
  lessonSequence: number,
  playbackId: string,
  videoId: number,
  videoTitle: string,
  viewerUserId: string,
  className?: string
}

const VideoPlayer = ({
  courseId,
  courseSlug,
  lessonId,
  lessonSequence,
  playbackId,
  videoId,
  videoTitle,
  viewerUserId,
  className = '',
}: VideoParams) => {
  const [timer, setTimer,] = useState(false);
  const router = useRouter();
  let nextLessonTimer: ReturnType<typeof setTimeout>;

  const moveToNextLesson = async (nextLessonSlug: string) => {
    clearTimeout(nextLessonTimer);
    router.push(`/cursos/${courseSlug}/aula/${nextLessonSlug}`);
  };

  const cancelMoveToNextLesson = () => {
    clearTimeout(nextLessonTimer);
    setTimer(false);
  };

  const onEnded = async () => {
    await fetch(URLS.COURSE.PROGRESS, {
      method: 'POST',
      body: JSON.stringify({
        courseId,
        lessonId,
        lessonSequence,
        status: LESSON_STATUS.COMPLETE as ProgressTypes,
      }),
    });
    const response = await fetch(`${URLS.COURSE.LESSON}?courseId=${courseId}&sequence=${lessonSequence + 1}`);
    if (response.status === 200) {
      const lesson = await response.json();
      setTimer(true);
      nextLessonTimer = setTimeout(() => {
        moveToNextLesson(lesson.slug);
      }, 3700);
    }
  };

  return (
    <div className='w-full'>
      <MuxPlayer
        onEnded={onEnded}
        className={className}
        playbackId={playbackId}
        metadata={{
          video_id: videoId,
          video_title: videoTitle,
          viewer_user_id: viewerUserId,
        }}
      />
      {timer && (
        <div className='flex gap-2 items-center mt-2'>
          <span>Próxima aula em alguns segundos...</span>
          <Button variant='outline' className='border-none hover:bg-white
          ' onClick={cancelMoveToNextLesson}>Cancelar</Button>
        </div>
      )}
    </div>
  );
};

export default VideoPlayer;