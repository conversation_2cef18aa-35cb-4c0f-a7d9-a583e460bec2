'use client';

import { useState, } from 'react';
import { Card, CardDescription, CardHeader, CardTitle, } from '@/components/ui/shard/card';
import { Button, } from '@/components/ui/shard/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/shard/form';
import { Input, } from '@/components/ui/shard/input';
import H1 from '@/components/ui/typography/h1';
import { useToast, } from '@/components/ui/shard/use-toast';
import { zodResolver, } from '@hookform/resolvers/zod';
import { useForm, } from 'react-hook-form';
import { z, } from 'zod';
import { createClient, } from '@/utils/supabase/client';
import H2 from '@/components/ui/typography/h2';

export default function NewPassword() {
  const [sendButtonLabel, setSendButtonLabel,] = useState('Salvar');
  const { toast, } = useToast();
  const supabase = createClient();
  const [isDisabled, setIsDisabled, ] = useState(true);
  const [placeholder, setPlaceholder, ] = useState('*********');
  const { register, } = useForm();

  const formSchema = z.object({
    password: z.string().min(7, { message: 'Senha precisa ter mínimo 7 dígitos.', }),
    passwordConfirmation: z.string().min(7, { message: 'Senha precisa ter mínimo 7 dígitos.', }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: '',
      passwordConfirmation: '',
    },
  });

  const updatePassword = async ({ password, passwordConfirmation, }: z.infer<typeof formSchema>) => {
    if (password !== passwordConfirmation) {
      toast({ variant: 'destructive', title: 'As duas senhas precisam ser iguais.',  });
      return null;
    }
    setSendButtonLabel('Salvando senha');
    const { data: { user: authUser, }, } = await supabase.auth.getUser();
    const { data, } = await supabase.auth.updateUser({
      email: authUser?.email,
      password,
    });
    if (data) {
      toast({
        title: 'Senha salva',
      });
      setSendButtonLabel('Senha salva');
      setIsDisabled(true);
      form.reset();
      setPlaceholder('*********');
    }
  };

  if (sendButtonLabel === 'Enviado') return (
    <main className='flex items-center flex-col gap-14 justify-center h-full'>
      <H1>Link enviado</H1>
      <H2>Verifique seu email.</H2>
    </main>
  );
  function cleanPasswordForm() {
    setIsDisabled(!isDisabled);
    setSendButtonLabel('Salvar');
    form.reset();
    setPlaceholder('');
  }

  return (
    <Form {...form}>
      <Card className='w-full pb-6'>
        <CardHeader>
          <CardTitle>Nova Senha</CardTitle>
          <CardDescription>Alterar senha atual</CardDescription>
        </CardHeader>
        <form onSubmit={form.handleSubmit(updatePassword)} className='w-full px-5 space-y-4'>
          <FormField
            {...register('' ,{disabled : isDisabled,})}
            control={form.control}
            name='password'
            render={({ field, }) => (
              <FormItem>
                <FormLabel>Senha</FormLabel>
                <FormControl>
                  <Input {...field} placeholder={placeholder}/>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            {...register('' ,{disabled : isDisabled,})}
            control={form.control}
            name='passwordConfirmation'
            render={({ field, }) => (
              <FormItem>
                <FormLabel>Confirmar senha</FormLabel>
                <FormControl>
                  <Input {...field} placeholder={placeholder}/>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {!isDisabled &&
            <Button type='submit'>{sendButtonLabel}</Button>
          }
          {isDisabled &&
            <Button onClick={cleanPasswordForm} type='submit'>Alterar senha</Button>
          }
        </form>
      </Card>
    </Form>
  );
}
