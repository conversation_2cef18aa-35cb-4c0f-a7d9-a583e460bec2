'use server';

import { Attendance, RankPoint, RankSchool, } from '@/utils/constants/types';
import { createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestMaybeSingleResponse, PostgrestResponse, } from '@supabase/postgrest-js';

type RanksReturn = {
  data: RankSchool,
  status: 200,
} | {
  message: string,
  status: 400
}

export const getRequiredClassesForNextRank = async ({ rankId, schoolId, }: { rankId: string, schoolId: string }): Promise<RanksReturn> => {
  const supabaseAdmin = createClientAdmin();
  const { data, }: PostgrestMaybeSingleResponse<RankSchool> = await supabaseAdmin
  .from('rank_school')
  .select()
  .match({
    rankId,
    schoolId,
  })
  .maybeSingle();

  if (!data) return {
    message: 'Não foi possível buscar as graduações.',
    status: 400,
  };

  return { data, status: 200, };
};

type GetAttendanceProps = {
  profileId: string,
  sportId: string,
  dateLastRank: string,
}
type GetAttendanceResponse = { data: Attendance[], status: 200 } | { message: string, status: 400 };
export const getAttendanceSinceLastRank = async ({ profileId, sportId, dateLastRank, }: GetAttendanceProps): Promise<GetAttendanceResponse> => {
  const supabaseAdmin = createClientAdmin();
  const { data, }: PostgrestResponse<Attendance> = await supabaseAdmin
    .from('attendance')
    .select('*, service!inner()')
    .eq('service.sportId', sportId)
    .eq('userId', profileId)
    .eq('confirmed', true)
    .gt('createdAt', dateLastRank);

  if (!data) return {
    message: 'Erro ao buscar presenças.',
    status: 400,
  };

  return { data, status: 200, };
};

type GetPointsResponse = { data: RankPoint[], status: 200 } | { message: string, status: 400 }
export const getPoints = async ({ profileId, sportId, dateLastRank, }: GetAttendanceProps): Promise<GetPointsResponse> => {
  const supabaseAdmin = createClientAdmin();
  const { data,}: PostgrestResponse<RankPoint> = await supabaseAdmin
    .from('rank_point')
    .select()
    .match({ userId: profileId, sportId, })
    .gt('createdAt', dateLastRank);

  if (!data) return {
    message: 'Erro ao buscar pontos.',
    status: 400,
  };

  return { data, status: 200, };
};