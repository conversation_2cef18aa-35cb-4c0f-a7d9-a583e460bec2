'use client';

import { Rank, } from '@/utils/constants/types';
import { useState, } from 'react';

type Props = {
  label?: string,
  rank: Rank & { nextRank: Rank },
  isCurrent?: boolean,
  onDoubleClick?: () => void,
  required?: number,
  attendance?: number
}

const Belt = ({ label, rank, isCurrent = false, required = 0, attendance = 0, onDoubleClick, }: Props) => {
  const [isClicked, setIsClicked,] = useState(false);

  const handleClick = () => {
    onDoubleClick && onDoubleClick();
    setIsClicked(true);
    setTimeout(() => setIsClicked(false), 50);
  };

  if (rank.color === null) return null;

  const progress = Math.floor(attendance > required ? 100 : (attendance / required) * 100);
  const progressWidth = isCurrent ? '100%' : `${progress}%`;
  const isBlackBelt = rank.color === 'bg-black';

  return (
    <div onDoubleClick={handleClick} className={`${onDoubleClick ? 'cursor-pointer' : ''}`}>
      <div className='flex flex-row justify-between'>
        <span className='text-[15px] text-muted-foreground'>{label || rank.name}</span>
        <span className='text-[15px] text-muted-foreground'>{progress}%</span>
      </div>
      <div className={`relative w-full min-w-[180px] max-w-m h-10 rounded-sm overflow-hidden shadow-md transition-transform duration-150 bg-${rank.color}-200`}
        style={{ transform: isClicked ? 'scale(0.95)' : 'scale(1)', }}
      >
        <div className='w-full h-full'>
          <div
            className={'h-full transition-all duration-500 ease-in-out'}
            style={{ width: progressWidth, backgroundColor: rank.nextRank.color, }}
          ></div>
        </div>
        <div
          className={`absolute sm:hidden bottom-0 h-full flex gap-1 ${isBlackBelt ? 'bg-red-600' : 'bg-black'} px-2`}
          style={{ left: '70%', width: '17%', }}
        >
            {rank.stripe && [...Array(rank.stripe),].map((_, index) => (
              <div key={index} className='w-[7px] h-full bg-white mr-1' aria-hidden='true'></div>
            ))}
            {rank.nextRank.stripe && (
              <div className='w-[7px] bg-white mr-1' aria-hidden='true' style={{ height: progressWidth, }}></div>
            )}
        </div>
        <div
          className={`absolute hidden sm:flex lg:hidden bottom-0 h-full gap-2 ${isBlackBelt ? 'bg-red-600' : 'bg-black'} px-2`}
          style={{ left: '80%', width: '15%', }}
        >
            {rank.stripe && [...Array(rank.stripe),].map((_, index) => (
              <div key={index} className='w-[8px] h-full bg-white mr-1' aria-hidden='true'></div>
            ))}
            {rank.nextRank.stripe && (
              <div className='w-[7px] bg-white mr-1' aria-hidden='true' style={{ height: progressWidth, }}></div>
            )}
        </div>
        <div
          className={`absolute hidden lg:flex bottom-0 h-full gap-2 ${isBlackBelt ? 'bg-red-600' : 'bg-black'} px-2`}
          style={{ left: '80%', width: '8%', }}
        >
            {rank.stripe && [...Array(rank.stripe),].map((_, index) => (
              <div key={index} className='w-[8px] h-full bg-white mr-1' aria-hidden='true'></div>
            ))}
            {rank.nextRank.stripe && (
              <div className='w-[7px] bg-white mr-1' aria-hidden='true' style={{ height: progressWidth, }}></div>
            )}
        </div>
      </div>
    </div>
  );
};

export default Belt;

type StripeSectionProps = {
  isBlackBelt: boolean
  stripeCount: number
  progressHeight: string
  className: string
  style: React.CSSProperties
}

const StripeSection = ({ isBlackBelt, stripeCount, progressHeight, className, style, }: StripeSectionProps) => (
  <div
    className={`absolute top-0 h-full flex gap-1 ${isBlackBelt ? 'bg-red-600' : 'bg-black'} px-2 ${className}`}
    style={style}
  >
    <div className='relative w-full h-full overflow-hidden'>
      {stripeCount && [...Array(stripeCount),].map((_, index) => (
        <div
          key={index}
          className='absolute bottom-0 w-[8px] bg-white mr-1 transition-all duration-500 ease-in-out'
          style={{ height: progressHeight, left: `${index * 12}px`, }}
          aria-hidden='true'
        ></div>
      ))}
    </div>
  </div>
);