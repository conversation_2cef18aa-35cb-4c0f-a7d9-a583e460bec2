'use client';

import { useEffect, useState, } from 'react';
import { format, } from 'date-fns';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Checkbox,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/index';
import { Equipment, } from '@/utils/constants/types';
import { URLS, } from '@/utils/supabase/constants';

type Props = {
  selectedEquipments: { id: string, key?: string }[],
  serviceId: string | undefined | null,
  selectedScheduleId: string | undefined | null,
  selectedDate: Date | undefined | null,
  onSelect: ({ equipmentId, checked, key, }: { equipmentId: string, checked?: string | boolean, key?: string }) => void
}

const EquipmentSelector = ({ selectedEquipments, serviceId, selectedScheduleId, selectedDate, onSelect, }: Props) => {
  const [equipmentsSelects, setEquipmentsSelects,] = useState<{ [key: string]: Equipment[] }>({});
  const [equipmentsCheckboxes, setEquipmentsCheckboxes,] = useState<{ [key: string]: Equipment }>({});

  useEffect(() => {
    (async () => {
      if (!selectedScheduleId || !selectedDate) return;
      const queryParams = `serviceId=${serviceId}&day=${format(selectedDate, 'yyyy-MM-dd')}&scheduleId=${selectedScheduleId}`;
      const response = await fetch(`${URLS.EQUIPMENTS_AVAILABLE}?${queryParams}`);
      if (response.status === 200) {
        const data = await response.json();
        setEquipmentsSelects(data.selects);
        setEquipmentsCheckboxes(data.checkboxes);
      }
    })();
  }, [serviceId, selectedScheduleId, selectedDate,]);

  if (!selectedScheduleId) return null;
  if (!Object.keys(equipmentsSelects).length && !Object.keys(equipmentsCheckboxes).length) return null;
  return (
    <Accordion type="single" collapsible className="w-full">
      <AccordionItem value="item-1">
        <AccordionTrigger className='text-sm'>Equipamentos</AccordionTrigger>
        <AccordionContent>
          <div className="space-y-6">
            {
              !!Object.keys(equipmentsSelects) && Object.keys(equipmentsSelects).map(key => (
                <div key={key}>
                  <Select onValueChange={(e) => onSelect({ equipmentId: e, key, })} required>
                    <SelectTrigger id={key}>
                      <SelectValue placeholder={`Selecione ${key}`} />
                    </SelectTrigger>
                    <SelectContent>
                      {equipmentsSelects[key].map((equipment) => (
                        <SelectItem key={equipment.id} value={equipment.id} className="cursor-pointer">{equipment.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ))
            }
            {
              !!Object.keys(equipmentsCheckboxes).length &&
              <>
                <div className="grid grid-cols-2 gap-2">
                  {
                    Object.keys(equipmentsCheckboxes).map(key => (
                      <div key={key} className="flex items-center space-x-2">
                        <Checkbox
                          id={key}
                          checked={!!selectedEquipments?.find(equipment => equipment.id === equipmentsCheckboxes[key].id)}
                          onCheckedChange={(checked) => onSelect({ equipmentId: equipmentsCheckboxes[key].id, checked, })}
                        />
                        <Label htmlFor={key}>{key}</Label>
                      </div>
                    ))
                  }
                </div>
              </>
            }
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default EquipmentSelector;