'use client';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/index';

type Props = {
  hours: string[],
  onSelect: (value: string) => void
}

const HoursSelector = ({ hours, onSelect, }: Props) => {
  return (
    <div className="space-y-2">
      <Select onValueChange={onSelect} required>
        <SelectTrigger id="time">
          <SelectValue placeholder="Escolha o horário" />
        </SelectTrigger>
        <SelectContent>
          {
            hours.length === 0 && (
              <SelectItem disabled value='sem-horario'>Sem horários.</SelectItem>
            )
          }
          {
            hours.map(hour => (
              <SelectItem
                key={hour}
                value={hour}>{`${hour}`}</SelectItem>
            ))
          }
        </SelectContent>
      </Select>
    </div>
  );
};

export default HoursSelector;