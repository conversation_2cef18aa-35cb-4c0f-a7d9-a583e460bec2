'use client';

import Link from 'next/link';
import { usePathname, } from 'next/navigation';
import { Home, Menu, LucideProps, } from 'lucide-react';
import { cn, } from '@/lib/utils';
import { Button, ScrollArea, Sheet, SheetClose, SheetContent, SheetTrigger, } from '../index';
import { ForwardRefExoticComponent, RefAttributes, } from 'react';
import { linksAdmin, linksStudent, linksTeacher, } from '@/utils/constants';
import { User, } from '@supabase/supabase-js';

const LinkComponent = ({ Icon, label, href, }: { Icon: ForwardRefExoticComponent<Omit<LucideProps, 'ref'> & RefAttributes<SVGSVGElement>>, label: string, href: string }) => {
  const pathname = usePathname();
  return (
    <SheetClose asChild>
      <Link
        href={href}
        className={cn('mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground', pathname.indexOf(href) !== -1 && 'bg-slate-200 text-slate-900')}
      >
        <Icon className="h-4" />
        {label}
      </Link>
    </SheetClose>
  );
};

const NavContent = ({ user, }: { user: User }) => {
  return (
    <ScrollArea>
      <nav className="grid gap-2 text-sm font-medium">
        <Link
          href="/cursos"
          className="flex items-center gap-2 text-lg font-semibold"
        >
          <Home className="h-5" />
          <span>{user.user_metadata.name}</span>
        </Link>
        {user.user_metadata.role === 'admin' && linksAdmin.map(link => (
          <LinkComponent key={link.href} Icon={link.icon} href={link.href} label={link.label} />
        ))}
        {user.user_metadata.role === 'teacher' && linksTeacher.map(link => (
          <LinkComponent key={link.href} Icon={link.icon} href={link.href} label={link.label} />
        ))}
        {user.user_metadata.role === 'student' && linksStudent.map(link => (
          <LinkComponent key={link.href} Icon={link.icon} href={link.href} label={link.label} />
        ))}
      </nav>
    </ScrollArea>
  );
};

export default function NavMobile({ user, }: { user: User }) {
  if (!user) return null;

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className="shrink-0 md:hidden"
        >
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle navigation menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="flex flex-col">
        <NavContent user={user} />
      </SheetContent>
    </Sheet>
  );
}