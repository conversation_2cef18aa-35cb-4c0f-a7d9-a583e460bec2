'use client';

import { useEffect, useState, } from 'react';
import { CalendarIcon, } from 'lucide-react';
import { format, } from 'date-fns';
import {
  Button,
  Calendar,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/index';
import { ptBR, } from 'date-fns/locale';

const DaySelector = ({ onSelect, }: { onSelect: (date: Date) => void }) => {
  const [selectedDate, setSelectedDate,] = useState<Date | undefined>();
  const [open, setOpen,] = useState(false);

  useEffect(() => {
    if (!selectedDate) return;
    onSelect(selectedDate);
  }, [selectedDate,]);

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="w-full justify-start text-left font-normal">
            <CalendarIcon className="mr-2 h-4 w-4" />
            {selectedDate ? format(selectedDate, 'PPP', { locale: ptBR, }) : <span>Selecione uma data</span>}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={setSelectedDate}
            initialFocus
            locale={ptBR}
            fromDate={new Date()}
            weekStartsOn={1}
            onDayClick={() => setOpen(false)}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default DaySelector;