'use client'

import React, { useEffect, useState } from "react"

import { But<PERSON> } from "@/components/ui/shard/button"
import {
  Card,
  CardContent,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/shard/card"
import ModuleItem from "@/components/ui/moduleItem"
import AddModule from "@/components/ui/addModule"
import { createClient } from "@/utils/supabase/client"

export default function Modules({ courseId }: { courseId: number }) {
  const [modules, setModules] = useState([])
  const [showAddModule, setShowAddModule] = useState(false)
  const supabase = createClient()

  async function getModules() {
    const { data } = await supabase.from('module').select().eq('courseId', courseId)
    setModules(data || [])
  }

  useEffect(() => {
    getModules()
  }, [courseId])


  return (
    <>
      <Card className="">
        <CardHeader>
          <CardTitle><PERSON>ó<PERSON><PERSON></CardTitle>
        </CardHeader>
        <CardContent>
          {modules.length > 0 && modules.map(module => <ModuleItem key={module.id} id={module.id} name={module.name} />)}
          {modules.length === 0 && (
            <span>Esse curso não tem módulos.</span>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button onClick={() => setShowAddModule(true)} >Criar módulo</Button>
        </CardFooter>
      </Card>
      <AddModule show={showAddModule} courseId={courseId} onClose={() => setShowAddModule(false)} getModules={getModules} />
    </>
  )
}
