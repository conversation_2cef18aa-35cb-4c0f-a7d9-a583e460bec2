'use client';

import { useEffect, useState, } from 'react';
import { Upload, Loader2, } from 'lucide-react';
import Image from 'next/image';
import { Label, } from './shard/label';
import { Input, } from './shard/input';

const ImageUploader = ({ onImageLoad, faceImage, }) => {
  const [selectedImage, setSelectedImage,] = useState<string | null>();
  const [loadingImg, setLoadingImg,] = useState(false);
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  useEffect(() => {
    if (selectedImage) {
      (( async () => {
        await onImageLoad(selectedImage);
        setLoadingImg(false);
      })());
    }
  }, [selectedImage,]);

  const selectImage = (event) => { setLoadingImg(true); setSelectedImage(event.target.files[0]); };

  return (
    <div className="w-full">
      <Label htmlFor="image-upload" className="sr-only">Selecione imagem</Label>
      <div className="flex items-center justify-center relative">
        {loadingImg && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/10 backdrop-blur-sm z-50">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        )}
        <Input
          id="image-upload"
          type="file"
          accept="image/*"
          onChange={selectImage}
          className="hidden"
        />
        <Label
          htmlFor="image-upload"
          className="cursor-pointer flex items-center justify-center w-full aspect-video border-2 border-dashed rounded-md hover:border-primary"
        >
          {(selectedImage || faceImage) ? (
            <div className="relative w-full h-full">
              <Image
                src={(selectedImage && URL.createObjectURL(selectedImage)) || faceImage}
                alt="Uploaded class image"
                layout="fill"
                objectFit="contain"
                className="rounded-md"
                id='profile-image'
              />
            </div>
          ) : (
            <div className="text-center">
              <Upload className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-sm text-gray-500">Imagem de perfil</p>
            </div>
          )}
        </Label>
      </div>
    </div>
  );
};

export default ImageUploader;