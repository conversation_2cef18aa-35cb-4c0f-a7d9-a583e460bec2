import { createClient } from '@/utils/supabase/server';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/shard/accordion';
import Link from 'next/link';

export default async function CouseContent({ courseId, isLink }: { courseId: number, isLink: boolean }) {
  const supabase = createClient()
  const { data: modules } = await supabase.from('module').select(`*, lesson (*)`).match({ courseId });

  return (
    <div>
        {modules && modules.length && modules.map(module => (
          <Accordion key={module.id} type='single' collapsible className='w-full'>
            <AccordionItem value='item-1'>
              <AccordionTrigger>{module.name}</AccordionTrigger>
              {module?.lesson.length > 0 && module.lesson.map(lesson => (
                <AccordionContent key={lesson.slug}>{isLink ? <Link href={lesson.slug}>{lesson.name}</Link> : lesson.name}</AccordionContent>
              ))}
            </AccordionItem>
          </Accordion>
        ))}
    </div>
  )
}
