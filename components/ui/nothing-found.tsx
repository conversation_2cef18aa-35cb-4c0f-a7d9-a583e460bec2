'use client';

import React from 'react';
import { Card, } from '../index';

const NothingFound = ({ children, label, }: { children?: React.ReactNode, label?: string }) => {
  return (
    <Card className="flex flex-col p-8 gap-4 justify-center items-center w-full h-full">
      {label && (
        <h2>{label}</h2>
      )}
      {children && children}
    </Card>
  );
};

export default NothingFound;