'use client';

import Link from 'next/link';
import { usePathname, } from 'next/navigation';
import { LucideProps, } from 'lucide-react';
import { cn, } from '@/lib/utils';
import { ForwardRefExoticComponent, RefAttributes, } from 'react';
import { linksAdmin, linksStudent, linksTeacher, } from '@/utils/constants';
import { User, } from '@supabase/supabase-js';

const LinkComponent = ({ Icon, label, href, }: { Icon: ForwardRefExoticComponent<Omit<LucideProps, 'ref'> & RefAttributes<SVGSVGElement>>, label: string, href: string }) => {
  const pathname = usePathname();
  return (
    <Link
      href={href}
      className={cn('flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary', pathname.indexOf(href) !== -1 && 'bg-slate-200 text-slate-900')}
    >
      <Icon className="h-4" />
      {label}
    </Link>
  );
};

export default function NavDesktop({ user,  }: { user: User }) {
  if (!user) return null;

  return (
    <nav className="grid items-start px-2 text-sm font-medium lg:px-4">
      {user.user_metadata.role === 'admin' && linksAdmin.map(link => (
        <LinkComponent key={link.href} Icon={link.icon} href={link.href} label={link.label} />
      ))}
      {user.user_metadata.role === 'teacher' && linksTeacher.map(link => (
        <LinkComponent key={link.href} Icon={link.icon} href={link.href} label={link.label} />
      ))}
      {user.user_metadata.role === 'student' && linksStudent.map(link => (
        <LinkComponent key={link.href} Icon={link.icon} href={link.href} label={link.label} />
      ))}
    </nav>
  );
}
