'use client';

import { ChangeEvent, useEffect, useState, } from 'react';
import { ChevronLeft, ChevronRight, User as UserIcon, } from 'lucide-react';
import * as faceapi from 'face-api.js';
import { Avatar, AvatarFallback, AvatarImage, Button, Input, Label, } from '@/components/index';
import { URLS, } from '@/utils/supabase/constants';
import toBuffer from 'typedarray-to-buffer';
import { toast, } from './shard/use-toast';
import { createClient, } from '@/utils/supabase/client';

type Props = {
  email?: string,
  domain: string,
  folder: 'avatars' | 'faces',
  faceImage?: string[] | null,
  profileImage?: string | null,
  type: 'face' | 'profile'
}

export default function UserProfile({ domain, folder, email, faceImage, profileImage, type, }: Props) {
  const [selectedImageIndex, setSelectedImageIndex,] = useState<number>(0);
  const [images, setImages,] = useState<string[]>([...(profileImage ? [profileImage,] : []), ...(faceImage || []),]);

  useEffect(() => {
    (async () => {
      // load models
      await Promise.all([
        faceapi.loadSsdMobilenetv1Model(URLS.FACE_API),
        faceapi.loadFaceLandmarkModel(URLS.FACE_API),
        faceapi.loadFaceRecognitionModel(URLS.FACE_API),
      ]);
    })();
  }, []);

  const handleImageChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const fileExt = file.name.split('.').pop();
      const fileName = `${email}${Math.random()}.${fileExt}`;
      const filePath = `${fileName}`;
      const supabase = createClient();
      const { data, error: uploadError, } = await supabase.storage.from(`${domain}/${folder}`).upload(filePath, file);
      if (data && !uploadError) {
        const fullPath = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${data.fullPath}`;
        setImages((prevImages) => [fullPath, ...prevImages,]);
        setSelectedImageIndex(0);
        await saveProfileImage(fullPath);
        const img = new Image();
        img.src = URL.createObjectURL(file);
        img.onload = async () => {
          await saveProfileFace(img);
        };
      }
      console.log('🚀 ~ uploadError ~ uploadError:', uploadError);
      console.log('🚀 ~ handleImageChange ~ data:', data);
    }
  };

  const saveProfileImage = async (path: string) => {
    const response = await fetch(`${URLS.USER.UPDATE}`, {
      method: 'PATCH',
      body: JSON.stringify({
        email,
        [`${type}Image`]: type === 'profile' ? path : [...(faceImage || []), path,],
      }),
    });
    console.log('🚀 ~ saveProfileImage ~ response:', response);
    if (response.status  === 200) {
      toast({
        title: 'Imagem salva com sucesso.',
      });
    }
  };

  const saveProfileFace = async (img: HTMLImageElement) => {
    const detection = await faceapi.detectSingleFace(img).withFaceLandmarks().withFaceDescriptor();
    if (detection) {
      const buf = toBuffer(detection.descriptor);
      await fetch(URLS.USER.FACE, {
        method: 'POST',
        body: JSON.stringify({
          email,
          newFace: buf.toString('base64'),
        }),
      });
    }
  };

  const nextImage = () => {
    setSelectedImageIndex((prevIndex) => (prevIndex + 1) % (faceImage?.length || 0));
  };

  const prevImage = () => {
    setSelectedImageIndex((prevIndex) => (prevIndex - 1 + (faceImage?.length || 0)) % (faceImage?.length || 0));
  };

  return (
    <div className="m-4">
      <div className="flex flex-col items-center space-y-4">
        <div className='flex items-center justify-center w-48 h-32 rounded-full'>
          {faceImage && faceImage.length > 1 && (
            <Button
              variant="outline"
              size="icon"
              onClick={prevImage}
              className='h-8'
              type='button'
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          )}
          <Avatar className="w-32 h-32">
            <AvatarImage src={images[selectedImageIndex]} alt="Foto de perfil" id='profile-image' className='object-cover' />
            {images.length && <AvatarFallback><UserIcon className="w-16 h-16" /></AvatarFallback>}
          </Avatar>
          {faceImage && faceImage.length > 1 && (
            <Button
              variant="outline"
              size="icon"
              onClick={nextImage}
              className='h-8'
              type='button'
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}
        </div>
        <Label htmlFor="profile-image-input" className="cursor-pointer text-slate-900 underline">
          Adicionar Imagem
        </Label>
        <Input
          id="profile-image-input"
          type="file"
          accept="image/*"
          className="hidden"
          onChange={handleImageChange}
        />
      </div>
    </div>
  );
}