'use server';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle, } from '@/components/ui/shard/card';
import { Badge, } from '@/components/ui/shard/badge';
import { Pack, PackProfile, Payment, SaleRoot, } from '@/utils/constants/types';
import { ptBR, } from 'date-fns/locale';
import { format, } from 'date-fns/format';
import { formatToBrazilianReal, } from '@/lib/utils';
import SeeInvoice from '../../app/[domain]/(shell)/compras/components/see-invoice';
import { createClientAdmin, } from '@/utils/supabase/server';
import { accountedForBookingStatus, } from '@/utils/supabase/constants';
import DeletePackProfile from '../../app/[domain]/(shell)/compras/components/delete-pack-profile';

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-green-500';
    case 'used':
      return 'bg-blue-500';
    case 'pending':
      return 'bg-yellow-500';
    case 'expired':
      return 'bg-gray-500';
  }
};

const getStatusName = (status: string) => {
  switch (status) {
    case 'active':
      return 'Ativo';
    case 'used':
      return 'Usado';
    case 'pending':
      return 'Pendente';
    case 'expired':
      return 'Vencido';
    default:
      return '';
  }
};

const getPaymentStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-500';
    case 'received':
    case 'confirmed':
      return 'bg-green-500';
    case 'overdue':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
};

const getPaymentStatusName = (status: string) => {
  switch (status) {
    case 'pending':
      return 'Pendente';
    case 'received':
    case 'confirmed':
      return 'Confirmado';
    case 'overdue':
      return 'Atrasado';
    default:
      return '';
  }
};

const PackDetails = async ({ packProfile, userId, isAdmin, }: { packProfile: PackProfile & { pack: Pack, payment: Payment, sale_root: SaleRoot }, userId: string, isAdmin: true }) => {
  const { pack, payment, sale_root: saleRoot, paymentStatus, purchaseStatus, } = packProfile;

  if (!packProfile) return null;

  const supabaseAdmin = createClientAdmin();
  const { count, } = await supabaseAdmin
    .from('booking')
    .select('*', { count: 'exact', head: true, })
    .eq('userId', userId)
    .eq('packProfileId', packProfile.id)
    .in('status', accountedForBookingStatus);


  return (
    <Card className='w-full bg-white shadow-lg rounded-lg overflow-hidden'>
      <CardHeader className='bg-gray-50 border-b border-gray-200'>
        <CardTitle className='flex flex-col sm:flex-row items-start sm:justify-between sm:items-center gap-4'>
          <div className='flex gap-4 justify-between sm:justify-start w-full sm:w-auto'>
            <span className='text-xl font-bold text-gray-800'>{pack.name}</span>
            <Badge className={`text-sm font-medium ${getStatusColor(purchaseStatus)}`}>
              {getStatusName(purchaseStatus)}
            </Badge>
            {payment && (
              <div className='w-full sm:w-40'>
                {payment.invoiceUrl && <SeeInvoice url={payment.invoiceUrl} />}
              </div>
            )}
          </div>
          {isAdmin && <DeletePackProfile packProfileId={packProfile.id} />}
        </CardTitle>
      </CardHeader>
      <CardContent className='flex flex-col sm:grid sm:grid-cols-2 gap-4 p-6'>
        <div className='flex items-end justify-between gap-2'>
          <span className='text-base font-semibold'>Pagamento:</span>
          <Badge className={`${getPaymentStatusColor(paymentStatus)} text-primary-foreground w-24 justify-center`}>
            {getPaymentStatusName(paymentStatus)}
          </Badge>
        </div>
        {saleRoot && packProfile.createdAt && (
          <div className='flex items-end justify-between gap-2'>
            <span className='text-base font-semibold'>Início:</span>
            <span className='text-muted-foreground'>{format(packProfile.createdAt, 'dd MMM yyyy', { locale: ptBR, })}</span>
          </div>
        )}
        {payment && packProfile.createdAt && (
          <div className='flex items-end justify-between gap-2'>
            <span className='text-base font-semibold'>Início:</span>
            <span className='text-muted-foreground'>{payment && payment.status === 'pending' ? 'pendente' : format(packProfile.createdAt, 'dd MMM yyyy', { locale: ptBR, })}</span>
          </div>
        )}
        <div className='flex items-end justify-between gap-2'>
          <span className='text-base font-semibold'>Preço:</span>
          <span className='text-muted-foreground'>{formatToBrazilianReal(pack.price)}</span>
        </div>
        {payment && packProfile.expireDate && (
          <div className='flex items-end justify-between gap-2'>
            <span className='text-base font-semibold'>Vencimento:</span>
            <span className='text-muted-foreground'>{payment.status === 'pending' ? 'pendente' : format(packProfile.expireDate, 'dd MMM yyyy', { locale: ptBR, })}</span>
          </div>
        )}
        {saleRoot && packProfile.expireDate && (
          <div className='flex items-end justify-between gap-2'>
            <span className='text-base font-semibold'>Vencimento:</span>
            <span className='text-muted-foreground'>{format(packProfile.expireDate, 'dd MMM yyyy', { locale: ptBR, })}</span>
          </div>
        )}
        <div className='flex items-end justify-between gap-2'>
          <span className='text-base font-semibold'>Direito a uso:</span>
          <span className='text-muted-foreground'>{pack.use}</span>
        </div>
        {packProfile && packProfile.used !== null && (
          <div className='flex items-end justify-between gap-2'>
            <span className='text-base font-semibold'>Usados:</span>
            <span className='text-muted-foreground'>{(count || 0) + packProfile.used}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PackDetails;