import * as React from 'react';
import { cva, type VariantProps, } from 'class-variance-authority';

import { cn, } from '@/lib/utils';

const badgeVariants = cva(
  'inline-flex items-center rounded border-transparent px-2 py-1 text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        blue:
          'bg-blue-200 text-blue-800',
        red:
          'bg-red-200 text-red-800',
        green:
          'bg-green-200 text-green-800',
        purple:
          'bg-purple-200 text-purple-800',
        yellow:
          'bg-yellow-200 text-yellow-800',
        default:
          'bg-primary text-primary-foreground hover:bg-primary/80',
        secondary:
          'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        destructive:
          'bg-destructive text-destructive-foreground hover:bg-destructive/80',
        outline: 'text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant, }), className)} {...props} />
  );
}

export { Badge, badgeVariants, };
