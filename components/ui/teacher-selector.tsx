'use client';

import {
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/index';
import { Profile, Service, } from '@/utils/constants/types';

type Props = {
  teachers: Profile[],
  onSelect: (value: string | Profile) => void,
}

const TeacherSelector = ({ teachers, onSelect, }: Props) => {

  return (
    <div className="space-y-2">
      <Label htmlFor="service">Professores</Label>
      <Select onValueChange={onSelect} required>
        <SelectTrigger id="service">
          <SelectValue placeholder="Selecione um professor" />
        </SelectTrigger>
        <SelectContent>
          {teachers.map((teacher) => (<SelectItem key={teacher.id} value={teacher.id} className="cursor-pointer">{teacher.name}</SelectItem>))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default TeacherSelector;