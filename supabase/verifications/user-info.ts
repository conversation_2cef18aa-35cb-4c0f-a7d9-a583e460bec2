'use server';

import { calculateTimeDifferenceInHours, } from '@/lib/utils';
import { Booking, Profile, Schedule, } from '@/utils/constants/types';
import { createClient, createClientAdmin, } from '@/utils/supabase/server';
import { PostgrestSingleResponse, User, } from '@supabase/supabase-js';
import { addHours, addMinutes, } from 'date-fns';

export const userInfo = async (): Promise<User | null> => {
  const supabase = createClient();
  const { data: { user, }, } = await supabase.auth.getUser();
  if (!user) return null;
  return user;
};

export const getProfile = async (): Promise<{ data: Profile, status: 200 } | { message: string, status: 400 }> => {
  const user = await userInfo();
  if (!user) return { message: 'Erro ao buscar usuário.', status: 400, };

  const supabase = createClient();
  const { data, }: PostgrestSingleResponse<Profile | null> = await supabase
    .from('profile')
    .select()
    .match({ id: user.id, })
    .maybeSingle();

  if (!data) return { message: 'Erro ao buscar usuário.', status: 400, };
  return { data, status: 200, };
};

type IsPastCancelationWindowReturn = { data: boolean, status: 200 } | { message: string, status: 400 };
type IsPastCancelationWindowParams = { booking: Booking & { schedule: Schedule } };
export const getIsPastRefundCancelationWindow = async ({ booking, }: IsPastCancelationWindowParams): Promise<IsPastCancelationWindowReturn> => {
  const supabaseAdmin = createClientAdmin();
  const { data: service, } = await supabaseAdmin
  .from('service')
  .select()
  .match({ id: booking.serviceId, })
  .maybeSingle();

  console.log('🚀 ~ getIsPastRefundCancelationWindow ~ booking:', booking);
  console.log('🚀 ~ getIsPastRefundCancelationWindow ~ service:', service);
  if (!service) return {
    message: 'Erro ao buscar política de cancelamento.',
    status: 400,
  };
  const hoursAndMinutes = booking.schedule.hour.split(':');
  console.log('🚀 ~ getIsPastRefundCancelationWindow ~ hoursAndMinutes:', hoursAndMinutes);
  let date = addHours(booking.day, parseInt(hoursAndMinutes[0]));
  date = addMinutes(date, parseInt(hoursAndMinutes[1]));
  console.log('🚀 ~ getIsPastRefundCancelationWindow ~ date:', date);
  const timeDifference = calculateTimeDifferenceInHours(date);
  console.log('🚀 ~ getIsPastRefundCancelationWindow ~ timeDifference:', timeDifference);
  console.log('🚀 ~ getIsPastRefundCancelationWindow ~ data:', service.policy > Math.abs(timeDifference));
  return {
    data: service.policy > Math.abs(timeDifference),
    status: 200,
  };
};