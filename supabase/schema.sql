

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pg_cron" WITH SCHEMA "pg_catalog";






CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "extensions";



COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "http" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "hypopg" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "index_advisor" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgaudit" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."app_role" AS ENUM (
    'admin',
    'student',
    'master'
);


ALTER TYPE "public"."app_role" OWNER TO "postgres";


CREATE TYPE "public"."billing_type" AS ENUM (
    'UNDEFINED',
    'BOLETO',
    'CREDIT_CARD',
    'PIX'
);


ALTER TYPE "public"."billing_type" OWNER TO "postgres";


CREATE TYPE "public"."booking_status" AS ENUM (
    'pending',
    'approved',
    'canceled',
    'expired',
    'used',
    'missed',
    'bailed'
);


ALTER TYPE "public"."booking_status" OWNER TO "postgres";


CREATE TYPE "public"."cycle" AS ENUM (
    'WEEKLY',
    'BIWEEKLY',
    'MONTHLY',
    'BIMONTHLY',
    'QUARTERLY',
    'SEMIANNUALLY',
    'YEARLY'
);


ALTER TYPE "public"."cycle" OWNER TO "postgres";


CREATE TYPE "public"."equipment_status" AS ENUM (
    'available',
    'unavailable'
);


ALTER TYPE "public"."equipment_status" OWNER TO "postgres";


CREATE TYPE "public"."fine_type" AS ENUM (
    'FIXED',
    'PERCENTAGE'
);


ALTER TYPE "public"."fine_type" OWNER TO "postgres";


CREATE TYPE "public"."frequency_type" AS ENUM (
    'months',
    'days',
    'years'
);


ALTER TYPE "public"."frequency_type" OWNER TO "postgres";


CREATE TYPE "public"."lesson_progress" AS ENUM (
    'complete',
    'in_progress',
    'not_started'
);


ALTER TYPE "public"."lesson_progress" OWNER TO "postgres";


COMMENT ON TYPE "public"."lesson_progress" IS 'Lesson progress status';



CREATE TYPE "public"."pack_profile_status" AS ENUM (
    'active',
    'used',
    'expired',
    'pending'
);


ALTER TYPE "public"."pack_profile_status" OWNER TO "postgres";


CREATE TYPE "public"."payment_method" AS ENUM (
    'CREDIT_CARD',
    'PIX',
    'BOLETO'
);


ALTER TYPE "public"."payment_method" OWNER TO "postgres";


CREATE TYPE "public"."payment_status" AS ENUM (
    'pending',
    'confirmed',
    'overdue',
    'received'
);


ALTER TYPE "public"."payment_status" OWNER TO "postgres";


CREATE TYPE "public"."semaphore" AS ENUM (
    'green',
    'yellow',
    'red',
    'blank'
);


ALTER TYPE "public"."semaphore" OWNER TO "postgres";


CREATE TYPE "public"."sports" AS ENUM (
    'jiu-jitsu',
    'muay-thai',
    'karate',
    'judo',
    'surf'
);


ALTER TYPE "public"."sports" OWNER TO "postgres";


CREATE TYPE "public"."status" AS ENUM (
    'ACTIVE',
    'EXPIRED',
    'INACTIVE'
);


ALTER TYPE "public"."status" OWNER TO "postgres";


CREATE TYPE "public"."status_type" AS ENUM (
    'pending',
    'active',
    'inactive'
);


ALTER TYPE "public"."status_type" OWNER TO "postgres";


CREATE TYPE "public"."subscription_periodicity" AS ENUM (
    'monthly'
);


ALTER TYPE "public"."subscription_periodicity" OWNER TO "postgres";


COMMENT ON TYPE "public"."subscription_periodicity" IS 'De quanto em quanto tempo a assinatura deve ser cobrada';



CREATE TYPE "public"."subscription_status" AS ENUM (
    'authorized',
    'pending',
    'paused'
);


ALTER TYPE "public"."subscription_status" OWNER TO "postgres";


CREATE TYPE "public"."subscription_type" AS ENUM (
    'free',
    'payed'
);


ALTER TYPE "public"."subscription_type" OWNER TO "postgres";


CREATE TYPE "public"."user_type" AS ENUM (
    'admin',
    'student',
    'teacher',
    'master'
);


ALTER TYPE "public"."user_type" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."friends_attendance"("service" "uuid", "exclude" "uuid", "schedule" "uuid") RETURNS TABLE("id" "uuid", "name" character varying, "profileImage" character varying)
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
      SELECT p.id, p.name, p."profileImage"
    FROM public.attendance a
    JOIN public.profile p ON a."userId" = p.id
    WHERE a."serviceId" = service
      AND a.confirmed = TRUE
      AND a.date BETWEEN (CURRENT_DATE - INTERVAL '10 days') AND (CURRENT_DATE - INTERVAL '1 day')
      AND a."userId" NOT IN (
        SELECT "userId"
        FROM public.attendance
        WHERE "serviceId" = service
          AND "scheduleId" = schedule
          AND confirmed = TRUE
          AND date = CURRENT_DATE
      )
      AND a."userId" != exclude; -- Added condition to exclude the specified user
    END;
$$;


ALTER FUNCTION "public"."friends_attendance"("service" "uuid", "exclude" "uuid", "schedule" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_all_bookings"("school" "uuid") RETURNS TABLE("id" "uuid", "code" "text", "day" "date", "status" "public"."booking_status", "equipment" "jsonb", "service" "jsonb", "schedule" "jsonb", "lead" "jsonb", "profile" "jsonb")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
    b.id,
    b.code,
    b.day,
    b.status,
    COALESCE((
        SELECT jsonb_agg(jsonb_build_object('name', e.name))
        FROM public.booking_equipment be
        JOIN public.equipment e ON be."equipmentId" = e.id
        WHERE be."bookingId" = b.id
    ), '[]'::jsonb) AS equipment,
    jsonb_build_object(
        'title', s.title
    ) AS service,
    jsonb_build_object(
        'hour', sch.hour
    ) AS schedule,
    jsonb_build_object(
        'name', l.name
    ) AS lead,
    jsonb_build_object(
        'name', p.name,
        'id', p.id
    ) as profile
FROM 
    public.booking b
LEFT JOIN
    public.service s ON b."serviceId" = s.id
LEFT JOIN
    public.lead l ON b."leadId" = l.id
LEFT JOIN
    public.profile p ON b."userId" = p.id
LEFT JOIN
    public.schedule sch ON b."scheduleId" = sch.id
WHERE s."schoolId" = "school"
ORDER BY 
    CASE WHEN b.day < CURRENT_DATE THEN 1 ELSE 0 END,  -- Future bookings first, then past
    b.day,                                           -- Order by day within each group
    sch.hour,                                        -- Then order by schedule.hour
    p.name;                                         -- Finally, order by profile name
END;
$$;


ALTER FUNCTION "public"."get_all_bookings"("school" "uuid") OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."schedule" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "serviceId" "uuid" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "hour" "text" NOT NULL,
    "limit" smallint,
    "number" smallint NOT NULL,
    "active" boolean DEFAULT true
);


ALTER TABLE "public"."schedule" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_unique_teacher_schedules_by_service"("service" "uuid") RETURNS TABLE("id" "uuid", "day" "date", "serviceId" "uuid", "scheduleId" "uuid", "profileId" "uuid", "limit" smallint, "schedule" "public"."schedule")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT
        ts.id,
        ts.day,
        ts."serviceId",
        ts."scheduleId",
        ts."profileId",
        ts.limit,
        s  -- Entire schedule object
    FROM
        public.teacher_schedule AS ts
    INNER JOIN
        public.schedule AS s ON ts."scheduleId" = s.id
    WHERE
        ts.day >= CURRENT_DATE AND
        ts."serviceId" = service AND
        s.active = TRUE;
END;
$$;


ALTER FUNCTION "public"."get_unique_teacher_schedules_by_service"("service" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_upcoming_birthdays"() RETURNS TABLE("id" "uuid", "name" character varying, "email" character varying, "birthdate" "date")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    SELECT p.id, p.name, p.email, p.birthdate
    FROM public.profile AS p
    WHERE extract(month from p.birthdate) = extract(month from CURRENT_DATE)
      AND extract(day from p.birthdate) BETWEEN extract(day from CURRENT_DATE) AND extract(day from CURRENT_DATE + 5)
      ORDER BY extract(day from p.birthdate);
END;
$$;


ALTER FUNCTION "public"."get_upcoming_birthdays"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_upcoming_birthdays"("school" "uuid") RETURNS TABLE("id" "uuid", "name" character varying, "email" character varying, "birthdate" "date")
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    SELECT p.id, p.name, p.email, p.birthdate
    FROM public.profile AS p
    WHERE p."schoolId" = school  -- Filter by schoolId
      AND extract(month from p.birthdate) = extract(month from CURRENT_DATE)
      AND extract(day from p.birthdate) BETWEEN extract(day from CURRENT_DATE) AND extract(day from CURRENT_DATE + 5)
    ORDER BY 
        CASE WHEN extract(month from p.birthdate) = extract(month from CURRENT_DATE) THEN 0 ELSE 1 END,
        extract(day from p.birthdate);
END;
$$;


ALTER FUNCTION "public"."get_upcoming_birthdays"("school" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."mark_expired_bookings"() RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$DECLARE
  expired_booking RECORD;
BEGIN
  FOR expired_booking IN
    SELECT b.id, b.status  -- Only select the necessary columns
    FROM public.booking b
    JOIN public.schedule s ON b."scheduleId" = s.id
    WHERE b.day + s.hour::INTERVAL < NOW()
      AND b.status IN ('pending', 'approved') -- Check the status
  LOOP
    UPDATE public.booking
    SET status = 'expired'
    WHERE id = expired_booking.id;
  END LOOP;
END;$$;


ALTER FUNCTION "public"."mark_expired_bookings"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."verify_pack_expiration"() RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$DECLARE
  expired_profile RECORD;
BEGIN
  FOR expired_profile IN
    SELECT *
    FROM public.pack_profile pp
    JOIN public.pack p ON pp."packId" = p.id
    WHERE (pp."createdAt" + INTERVAL '1 day' * p.expires) < NOW()
  LOOP
    UPDATE public.pack_profile
    SET active = false
    WHERE id = expired_profile.id;
  END LOOP;
END;$$;


ALTER FUNCTION "public"."verify_pack_expiration"() OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."attendance" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "date" "date" NOT NULL,
    "userId" "uuid" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "scheduleId" "uuid" NOT NULL,
    "serviceId" "uuid" NOT NULL,
    "confirmed" boolean DEFAULT true
);


ALTER TABLE "public"."attendance" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."profile" (
    "id" "uuid" DEFAULT "auth"."uid"() NOT NULL,
    "email" character varying(100) NOT NULL,
    "name" character varying(100) NOT NULL,
    "schoolId" "uuid" NOT NULL,
    "type" "public"."user_type" DEFAULT 'student'::"public"."user_type" NOT NULL,
    "birthdate" "date",
    "phone" character varying(100),
    "street" character varying(100),
    "neighborhood" character varying(100),
    "city" character varying(100),
    "state" character varying(100),
    "cep" character varying(100),
    "faceImage" "text"[],
    "profileImage" character varying,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "active" boolean DEFAULT false NOT NULL,
    "cpf" character varying DEFAULT ''::character varying NOT NULL,
    "asId" character varying,
    "status" "public"."status_type" DEFAULT 'pending'::"public"."status_type" NOT NULL
);


ALTER TABLE "public"."profile" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."attendance_monthly" WITH ("security_invoker"='true') AS
 SELECT "a"."userId",
    "a"."serviceId",
    "u"."name",
    EXTRACT(year FROM "a"."date") AS "year",
    EXTRACT(month FROM "a"."date") AS "month",
    "count"(*) AS "total"
   FROM ("public"."attendance" "a"
     JOIN "public"."profile" "u" ON (("a"."userId" = "u"."id")))
  GROUP BY "a"."userId", "a"."serviceId", "u"."name", (EXTRACT(year FROM "a"."date")), (EXTRACT(month FROM "a"."date"))
  ORDER BY ("count"(*)) DESC;


ALTER TABLE "public"."attendance_monthly" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."attendance_yearly" WITH ("security_invoker"='true') AS
 SELECT "a"."userId",
    "a"."serviceId",
    "u"."name",
    EXTRACT(year FROM "a"."date") AS "year",
    "count"(*) AS "total"
   FROM ("public"."attendance" "a"
     JOIN "public"."profile" "u" ON (("a"."userId" = "u"."id")))
  GROUP BY "a"."userId", "a"."serviceId", "u"."name", (EXTRACT(year FROM "a"."date"))
  ORDER BY ("count"(*)) DESC;


ALTER TABLE "public"."attendance_yearly" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."base_rank" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" character varying,
    "sportId" "uuid",
    "color" character varying,
    "stripes" smallint
);


ALTER TABLE "public"."base_rank" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."booking" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "serviceId" "uuid" NOT NULL,
    "userId" "uuid",
    "cancelable" boolean DEFAULT true,
    "status" "public"."booking_status" DEFAULT 'pending'::"public"."booking_status" NOT NULL,
    "details" character varying(255),
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "scheduleId" "uuid" NOT NULL,
    "day" "date" NOT NULL,
    "code" "text" NOT NULL,
    "teacherId" "uuid",
    "packId" "uuid",
    "leadId" "uuid",
    "packProfileId" bigint,
    "teacherScheduleId" "uuid",
    CONSTRAINT "booking_code_check" CHECK (("char_length"("code") = 5))
);


ALTER TABLE "public"."booking" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."booking_equipment" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "bookingId" "uuid" NOT NULL,
    "equipmentId" "uuid" NOT NULL
);


ALTER TABLE "public"."booking_equipment" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."cancelation_policy" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "hour" smallint NOT NULL,
    "percentage" smallint DEFAULT '100'::smallint NOT NULL,
    "serviceId" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "packId" "uuid"
);


ALTER TABLE "public"."cancelation_policy" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."category" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "schoolId" "uuid",
    "name" character varying NOT NULL
);


ALTER TABLE "public"."category" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."course" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(100) NOT NULL,
    "description" character varying(255),
    "instructorId" "uuid",
    "published" boolean DEFAULT false NOT NULL,
    "price" numeric,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "schoolId" "uuid",
    "slug" character varying(100) NOT NULL,
    "featureImg" character varying(100)
);


ALTER TABLE "public"."course" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."debt" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" character varying NOT NULL,
    "description" character varying,
    "type" character varying,
    "value" numeric NOT NULL,
    "billingDate" "date",
    "paid" boolean DEFAULT false,
    "paidDate" timestamp with time zone,
    "schoolId" "uuid",
    "method" "public"."payment_method",
    "debtRootId" "uuid"
);


ALTER TABLE "public"."debt" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."debt_category" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "categoryId" "uuid",
    "schoolId" "uuid",
    "debtRootId" "uuid"
);


ALTER TABLE "public"."debt_category" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."debt_root" (
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "value" numeric,
    "name" character varying,
    "type" character varying,
    "description" character varying,
    "paid" boolean,
    "fixed" boolean,
    "repetitions" smallint,
    "method" "public"."payment_method",
    "billingDay" character varying,
    "schoolId" "uuid",
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "billingDate" "date"
);


ALTER TABLE "public"."debt_root" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."equipment" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(100) NOT NULL,
    "schoolId" "uuid" NOT NULL,
    "type" character varying(100),
    "status" "public"."equipment_status" DEFAULT 'available'::"public"."equipment_status" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "quantity" smallint NOT NULL
);


ALTER TABLE "public"."equipment" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."error" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "value" "text" NOT NULL
);


ALTER TABLE "public"."error" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."face" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "email" character varying(100) NOT NULL,
    "description" "text"[] NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL
);


ALTER TABLE "public"."face" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."lead" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" character varying NOT NULL,
    "email" character varying,
    "phone" character varying,
    "schoolId" "uuid" NOT NULL
);


ALTER TABLE "public"."lead" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."lesson" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(100) NOT NULL,
    "description" character varying(255) NOT NULL,
    "slug" character varying(100) NOT NULL,
    "sequence" smallint NOT NULL,
    "courseId" "uuid" NOT NULL,
    "moduleId" "uuid" NOT NULL,
    "videoId" "uuid",
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL
);


ALTER TABLE "public"."lesson" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."log" (
    "id" bigint NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "value" character varying NOT NULL
);


ALTER TABLE "public"."log" OWNER TO "postgres";


ALTER TABLE "public"."log" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."log_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."module" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(100) NOT NULL,
    "description" character varying(255) NOT NULL,
    "courseId" "uuid" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL
);


ALTER TABLE "public"."module" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pack" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" character varying NOT NULL,
    "schoolId" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "price" numeric NOT NULL,
    "active" boolean DEFAULT true NOT NULL,
    "expires" smallint,
    "use" smallint,
    "installments" smallint
);


ALTER TABLE "public"."pack" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pack_profile" (
    "id" bigint NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "profileId" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "active" boolean DEFAULT true NOT NULL,
    "paymentId" character varying,
    "packId" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "paymentStatus" "public"."payment_status" DEFAULT 'pending'::"public"."payment_status",
    "purchaseStatus" "public"."pack_profile_status" DEFAULT 'pending'::"public"."pack_profile_status",
    "saleId" "uuid",
    "used" smallint DEFAULT '0'::smallint NOT NULL
);


ALTER TABLE "public"."pack_profile" OWNER TO "postgres";


ALTER TABLE "public"."pack_profile" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."pack_profile_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."payment" (
    "id" character varying NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "profileAsId" character varying,
    "value" numeric NOT NULL,
    "netValue" numeric,
    "description" character varying,
    "paymentType" "public"."payment_method",
    "dueDate" "date",
    "paymentDate" "date",
    "installmentNumber" smallint,
    "invoiceUrl" character varying,
    "externalReference" character varying,
    "planId" "uuid",
    "packId" "uuid",
    "dateCreated" "date",
    "subscriptionId" character varying,
    "status" "public"."payment_status"
);


ALTER TABLE "public"."payment" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."plan" (
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "name" character varying(100) NOT NULL,
    "schoolId" "uuid" NOT NULL,
    "price" numeric NOT NULL,
    "active" boolean DEFAULT true,
    "expires" "date",
    "frequencyType" "public"."frequency_type" NOT NULL,
    "frequency" smallint NOT NULL,
    "billingDay" smallint,
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "fine" numeric,
    "fineType" "public"."fine_type",
    "interest" numeric,
    "display" boolean DEFAULT true NOT NULL
);


ALTER TABLE "public"."plan" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."progress" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "courseId" "uuid",
    "lessonId" "uuid",
    "userId" "uuid" DEFAULT "gen_random_uuid"(),
    "status" "public"."lesson_progress",
    "lessonSequence" smallint
);


ALTER TABLE "public"."progress" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."rank" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" character varying NOT NULL,
    "color" character varying,
    "nextRank" "uuid",
    "sportId" "uuid",
    "stripe" smallint,
    "baseRankId" "uuid"
);


ALTER TABLE "public"."rank" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."rank_point" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "userId" "uuid" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "sportId" "uuid"
);


ALTER TABLE "public"."rank_point" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."rank_school" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "schoolId" "uuid" NOT NULL,
    "classes" smallint DEFAULT '40'::smallint NOT NULL,
    "rankId" "uuid" NOT NULL
);


ALTER TABLE "public"."rank_school" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."rank_student" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "rankId" "uuid" NOT NULL,
    "profileId" "uuid" NOT NULL,
    "sportId" "uuid"
);


ALTER TABLE "public"."rank_student" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."sale" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" character varying NOT NULL,
    "description" character varying,
    "value" numeric NOT NULL,
    "schoolId" "uuid" NOT NULL,
    "clientId" "uuid",
    "packId" "uuid",
    "billingDate" "date" NOT NULL,
    "method" "public"."payment_method",
    "saleRootId" "uuid" NOT NULL
);


ALTER TABLE "public"."sale" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."sale_root" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "value" numeric NOT NULL,
    "installments" smallint DEFAULT '1'::smallint,
    "description" character varying,
    "name" character varying NOT NULL,
    "packId" "uuid",
    "billingDay" smallint,
    "schoolId" "uuid" NOT NULL,
    "clientId" "uuid",
    "method" "public"."payment_method",
    "billingDate" "date" NOT NULL
);


ALTER TABLE "public"."sale_root" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."school" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "name" character varying(100) NOT NULL,
    "domain" character varying(100) NOT NULL,
    "cnpj" character varying
);


ALTER TABLE "public"."school" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."school_settings" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "closed" "date"[],
    "schoolId" "uuid" NOT NULL,
    "closedWeekDays" integer[]
);


ALTER TABLE "public"."school_settings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."service" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "title" character varying(100) NOT NULL,
    "description" "text",
    "schoolId" "uuid" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "limit" integer,
    "randomTeacher" boolean DEFAULT false,
    "sportId" "uuid",
    "policy" integer
);


ALTER TABLE "public"."service" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."service_equipment" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "serviceId" "uuid" NOT NULL,
    "equipmentId" "uuid" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "active" boolean DEFAULT true NOT NULL
);


ALTER TABLE "public"."service_equipment" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."service_pack" (
    "id" bigint NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "packId" "uuid" NOT NULL,
    "serviceId" "uuid" NOT NULL
);


ALTER TABLE "public"."service_pack" OWNER TO "postgres";


ALTER TABLE "public"."service_pack" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."service_pack_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."service_plan" (
    "id" bigint NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "planId" "uuid" NOT NULL,
    "serviceId" "uuid" NOT NULL
);


ALTER TABLE "public"."service_plan" OWNER TO "postgres";


ALTER TABLE "public"."service_plan" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."service_plan_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."service_student" (
    "id" bigint NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "profileId" "uuid" NOT NULL,
    "serviceId" "uuid" NOT NULL
);


ALTER TABLE "public"."service_student" OWNER TO "postgres";


ALTER TABLE "public"."service_student" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."service_student_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."service_teacher" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "serviceId" "uuid" NOT NULL,
    "profileId" "uuid" NOT NULL
);


ALTER TABLE "public"."service_teacher" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."sport" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" character varying NOT NULL
);


ALTER TABLE "public"."sport" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."sport_profile" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "sportId" "uuid" NOT NULL,
    "profileId" "uuid" NOT NULL
);


ALTER TABLE "public"."sport_profile" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."sport_school" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "sportId" "uuid" NOT NULL,
    "schoolId" "uuid" NOT NULL
);


ALTER TABLE "public"."sport_school" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."subscription" (
    "id" character varying NOT NULL,
    "createdAt" "date" DEFAULT "now"() NOT NULL,
    "object" character varying,
    "customer" character varying NOT NULL,
    "paymentLink" character varying,
    "billingType" "public"."billing_type",
    "cycle" "public"."cycle" NOT NULL,
    "value" numeric NOT NULL,
    "nextDueDate" "date",
    "endDate" "date",
    "description" character varying,
    "fineValue" numeric,
    "interestValue" numeric,
    "deleted" boolean,
    "maxPayments" smallint,
    "planId" "uuid",
    "profileId" "uuid",
    "status" "public"."status" DEFAULT 'ACTIVE'::"public"."status" NOT NULL,
    "cardNumber" character varying,
    "cardBrand" character varying,
    "paymentStatus" "public"."payment_status",
    "billingDay" smallint
);


ALTER TABLE "public"."subscription" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."teacher_schedule" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "scheduleId" "uuid" NOT NULL,
    "profileId" "uuid" NOT NULL,
    "day" "date" NOT NULL,
    "serviceId" "uuid" NOT NULL,
    "limit" smallint
);


ALTER TABLE "public"."teacher_schedule" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_pwa" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "deviceId" character varying,
    "sub" "json",
    "schoolId" "uuid",
    "profileId" "uuid"
);


ALTER TABLE "public"."user_pwa" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."video" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "publicPlaybackId" character varying(100),
    "duration" double precision,
    "videoId" character varying(100),
    "name" character varying(100),
    "schoolId" "uuid",
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL
);


ALTER TABLE "public"."video" OWNER TO "postgres";


ALTER TABLE ONLY "public"."attendance"
    ADD CONSTRAINT "attendance_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."base_rank"
    ADD CONSTRAINT "base_rank_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."booking_equipment"
    ADD CONSTRAINT "booking_equipment_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."booking_equipment"
    ADD CONSTRAINT "booking_id_equipment_id" UNIQUE ("bookingId", "equipmentId");



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "booking_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."cancelation_policy"
    ADD CONSTRAINT "cancelation_policy_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "code_unique" UNIQUE ("code");



ALTER TABLE ONLY "public"."course"
    ADD CONSTRAINT "course_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."course"
    ADD CONSTRAINT "course_slug" UNIQUE ("slug", "schoolId");



ALTER TABLE ONLY "public"."category"
    ADD CONSTRAINT "debt_category_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."debt_category"
    ADD CONSTRAINT "debt_category_pkey1" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."debt"
    ADD CONSTRAINT "debt_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."debt_root"
    ADD CONSTRAINT "debt_root_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."equipment"
    ADD CONSTRAINT "equipment_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."error"
    ADD CONSTRAINT "error_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."face"
    ADD CONSTRAINT "face_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."face"
    ADD CONSTRAINT "face_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."lead"
    ADD CONSTRAINT "lead_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."lesson"
    ADD CONSTRAINT "lesson_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."lesson"
    ADD CONSTRAINT "lesson_slug" UNIQUE ("slug", "courseId");



ALTER TABLE ONLY "public"."log"
    ADD CONSTRAINT "log_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."module"
    ADD CONSTRAINT "module_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."service_pack"
    ADD CONSTRAINT "pack_service_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pack"
    ADD CONSTRAINT "package_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payment"
    ADD CONSTRAINT "payment_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pack_profile"
    ADD CONSTRAINT "pkg_profile_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."plan"
    ADD CONSTRAINT "plan_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."service_plan"
    ADD CONSTRAINT "plan_service_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."profile"
    ADD CONSTRAINT "profile_asId_key" UNIQUE ("asId");



ALTER TABLE ONLY "public"."profile"
    ADD CONSTRAINT "profile_email_type" UNIQUE ("email", "type");



ALTER TABLE ONLY "public"."progress"
    ADD CONSTRAINT "progress_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."rank"
    ADD CONSTRAINT "rank_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."rank_point"
    ADD CONSTRAINT "rank_point_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."rank_school"
    ADD CONSTRAINT "rank_school_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."rank_student"
    ADD CONSTRAINT "rank_student_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."sale"
    ADD CONSTRAINT "sale_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."sale_root"
    ADD CONSTRAINT "sale_root_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."schedule"
    ADD CONSTRAINT "schedule_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."school"
    ADD CONSTRAINT "school_domain_key" UNIQUE ("domain");



ALTER TABLE ONLY "public"."school"
    ADD CONSTRAINT "school_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."school_settings"
    ADD CONSTRAINT "school_settings_pkey" PRIMARY KEY ("id", "schoolId");



ALTER TABLE ONLY "public"."school_settings"
    ADD CONSTRAINT "school_settings_school_id_key" UNIQUE ("schoolId");



ALTER TABLE ONLY "public"."service_equipment"
    ADD CONSTRAINT "service_equipment_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."service_teacher"
    ADD CONSTRAINT "service_instructor_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."service"
    ADD CONSTRAINT "service_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."service_student"
    ADD CONSTRAINT "service_profile_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."sport"
    ADD CONSTRAINT "sport_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."sport_profile"
    ADD CONSTRAINT "sport_profile_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."sport_school"
    ADD CONSTRAINT "sport_school_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."subscription"
    ADD CONSTRAINT "subscription_asaas_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."teacher_schedule"
    ADD CONSTRAINT "teacher_schedule_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."cancelation_policy"
    ADD CONSTRAINT "unique_hour_serviceid" UNIQUE ("hour", "serviceId");



ALTER TABLE ONLY "public"."category"
    ADD CONSTRAINT "unique_name_schoolid" UNIQUE ("name", "schoolId");



ALTER TABLE ONLY "public"."rank_school"
    ADD CONSTRAINT "unique_school_rank" UNIQUE ("schoolId", "rankId");



ALTER TABLE ONLY "public"."schedule"
    ADD CONSTRAINT "unique_service_hour_number_active" UNIQUE ("serviceId", "hour", "number", "active");



ALTER TABLE ONLY "public"."attendance"
    ADD CONSTRAINT "user_date" UNIQUE ("userId", "date", "scheduleId");



ALTER TABLE ONLY "public"."profile"
    ADD CONSTRAINT "user_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."profile"
    ADD CONSTRAINT "user_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_pwa"
    ADD CONSTRAINT "user_pwa_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."video"
    ADD CONSTRAINT "video_id_key" UNIQUE ("id");



ALTER TABLE ONLY "public"."video"
    ADD CONSTRAINT "video_pkey" PRIMARY KEY ("id");



CREATE INDEX "booking_id_idx" ON "public"."booking" USING "btree" ("id");



CREATE INDEX "profile_id_idx" ON "public"."profile" USING "btree" ("id");



CREATE OR REPLACE TRIGGER "new_booking" AFTER INSERT OR UPDATE ON "public"."booking" FOR EACH ROW EXECUTE FUNCTION "supabase_functions"."http_request"('https://jstsplzaniofsvrjlhiu.supabase.co/functions/v1/new_booking', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpzdHNwbHphbmlvZnN2cmpsaGl1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyMTgyMjc5NSwiZXhwIjoyMDM3Mzk4Nzk1fQ.lOJUUi019vtZWcvW2aAXRBX3WtgLViyaG8j3qMrrYNg"}', '{}', '5000');



CREATE OR REPLACE TRIGGER "new_debt_root" AFTER INSERT ON "public"."debt_root" FOR EACH ROW EXECUTE FUNCTION "supabase_functions"."http_request"('https://jstsplzaniofsvrjlhiu.supabase.co/functions/v1/new_debt_root', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpzdHNwbHphbmlvZnN2cmpsaGl1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyMTgyMjc5NSwiZXhwIjoyMDM3Mzk4Nzk1fQ.lOJUUi019vtZWcvW2aAXRBX3WtgLViyaG8j3qMrrYNg"}', '{}', '5000');



CREATE OR REPLACE TRIGGER "new_payment" AFTER INSERT OR UPDATE ON "public"."payment" FOR EACH ROW EXECUTE FUNCTION "supabase_functions"."http_request"('https://jstsplzaniofsvrjlhiu.supabase.co/functions/v1/new_payment', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpzdHNwbHphbmlvZnN2cmpsaGl1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyMTgyMjc5NSwiZXhwIjoyMDM3Mzk4Nzk1fQ.lOJUUi019vtZWcvW2aAXRBX3WtgLViyaG8j3qMrrYNg"}', '{}', '5000');



CREATE OR REPLACE TRIGGER "new_sale_root" AFTER INSERT ON "public"."sale_root" FOR EACH ROW EXECUTE FUNCTION "supabase_functions"."http_request"('https://jstsplzaniofsvrjlhiu.supabase.co/functions/v1/new_sale_root', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpzdHNwbHphbmlvZnN2cmpsaGl1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyMTgyMjc5NSwiZXhwIjoyMDM3Mzk4Nzk1fQ.lOJUUi019vtZWcvW2aAXRBX3WtgLViyaG8j3qMrrYNg"}', '{}', '5000');



CREATE OR REPLACE TRIGGER "new_subscription" AFTER INSERT ON "public"."subscription" FOR EACH ROW EXECUTE FUNCTION "supabase_functions"."http_request"('https://jstsplzaniofsvrjlhiu.supabase.co/functions/v1/new_subscription', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpzdHNwbHphbmlvZnN2cmpsaGl1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyMTgyMjc5NSwiZXhwIjoyMDM3Mzk4Nzk1fQ.lOJUUi019vtZWcvW2aAXRBX3WtgLViyaG8j3qMrrYNg"}', '{}', '5000');



ALTER TABLE ONLY "public"."attendance"
    ADD CONSTRAINT "attendance_scheduleId_fkey" FOREIGN KEY ("scheduleId") REFERENCES "public"."schedule"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."attendance"
    ADD CONSTRAINT "attendance_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."attendance"
    ADD CONSTRAINT "attendance_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."base_rank"
    ADD CONSTRAINT "base_rank_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES "public"."sport"("id");



ALTER TABLE ONLY "public"."booking_equipment"
    ADD CONSTRAINT "booking_equipment_bookingId_fkey" FOREIGN KEY ("bookingId") REFERENCES "public"."booking"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."booking_equipment"
    ADD CONSTRAINT "booking_equipment_equipmentId_fkey" FOREIGN KEY ("equipmentId") REFERENCES "public"."equipment"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "booking_leadId_fkey" FOREIGN KEY ("leadId") REFERENCES "public"."lead"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "booking_packProfileId_fkey" FOREIGN KEY ("packProfileId") REFERENCES "public"."pack_profile"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "booking_packageId_fkey" FOREIGN KEY ("packId") REFERENCES "public"."pack"("id");



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "booking_scheduleId_fkey" FOREIGN KEY ("scheduleId") REFERENCES "public"."schedule"("id");



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "booking_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "booking_teacherId_fkey" FOREIGN KEY ("teacherId") REFERENCES "public"."profile"("id");



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "booking_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."cancelation_policy"
    ADD CONSTRAINT "cancelation_policy_packId_fkey" FOREIGN KEY ("packId") REFERENCES "public"."pack"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."cancelation_policy"
    ADD CONSTRAINT "cancelation_policy_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."course"
    ADD CONSTRAINT "course_instructorId_fkey" FOREIGN KEY ("instructorId") REFERENCES "public"."profile"("id");



ALTER TABLE ONLY "public"."course"
    ADD CONSTRAINT "course_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id");



ALTER TABLE ONLY "public"."debt_category"
    ADD CONSTRAINT "debt_category_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "public"."category"("id");



ALTER TABLE ONLY "public"."debt_category"
    ADD CONSTRAINT "debt_category_debtRootId_fkey" FOREIGN KEY ("debtRootId") REFERENCES "public"."debt_root"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."category"
    ADD CONSTRAINT "debt_category_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id");



ALTER TABLE ONLY "public"."debt_category"
    ADD CONSTRAINT "debt_category_schoolId_fkey1" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."debt"
    ADD CONSTRAINT "debt_debtRootId_fkey" FOREIGN KEY ("debtRootId") REFERENCES "public"."debt_root"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."debt_root"
    ADD CONSTRAINT "debt_root_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."debt"
    ADD CONSTRAINT "debt_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id");



ALTER TABLE ONLY "public"."equipment"
    ADD CONSTRAINT "equipment_school_fk" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id");



ALTER TABLE ONLY "public"."face"
    ADD CONSTRAINT "face_email_fkey" FOREIGN KEY ("email") REFERENCES "public"."profile"("email") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "fk_teacher_schedule" FOREIGN KEY ("teacherScheduleId") REFERENCES "public"."teacher_schedule"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."lead"
    ADD CONSTRAINT "lead_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."lesson"
    ADD CONSTRAINT "lesson_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES "public"."course"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."lesson"
    ADD CONSTRAINT "lesson_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES "public"."module"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."lesson"
    ADD CONSTRAINT "lesson_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "public"."video"("id");



ALTER TABLE ONLY "public"."module"
    ADD CONSTRAINT "module_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES "public"."course"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pack_profile"
    ADD CONSTRAINT "pack_profile_packId_fkey" FOREIGN KEY ("packId") REFERENCES "public"."pack"("id");



ALTER TABLE ONLY "public"."pack_profile"
    ADD CONSTRAINT "pack_profile_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "public"."payment"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pack_profile"
    ADD CONSTRAINT "pack_profile_saleId_fkey" FOREIGN KEY ("saleId") REFERENCES "public"."sale_root"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."service_pack"
    ADD CONSTRAINT "pack_service_packId_fkey" FOREIGN KEY ("packId") REFERENCES "public"."pack"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."service_pack"
    ADD CONSTRAINT "pack_service_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payment"
    ADD CONSTRAINT "payment_packId_fkey" FOREIGN KEY ("packId") REFERENCES "public"."pack"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."payment"
    ADD CONSTRAINT "payment_planId_fkey" FOREIGN KEY ("planId") REFERENCES "public"."plan"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."payment"
    ADD CONSTRAINT "payment_profileAsId_fkey" FOREIGN KEY ("profileAsId") REFERENCES "public"."profile"("asId") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pack_profile"
    ADD CONSTRAINT "pkg_profile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "public"."profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pack"
    ADD CONSTRAINT "pkg_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."plan"
    ADD CONSTRAINT "plan_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."service_plan"
    ADD CONSTRAINT "plan_service_planId_fkey" FOREIGN KEY ("planId") REFERENCES "public"."plan"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."service_plan"
    ADD CONSTRAINT "plan_service_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."profile"
    ADD CONSTRAINT "profile_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."profile"
    ADD CONSTRAINT "profile_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."progress"
    ADD CONSTRAINT "progress_course_id_fkey" FOREIGN KEY ("courseId") REFERENCES "public"."course"("id");



ALTER TABLE ONLY "public"."progress"
    ADD CONSTRAINT "progress_lesson_id_fkey" FOREIGN KEY ("lessonId") REFERENCES "public"."lesson"("id");



ALTER TABLE ONLY "public"."progress"
    ADD CONSTRAINT "progress_user_id_fkey" FOREIGN KEY ("userId") REFERENCES "public"."profile"("id");



ALTER TABLE ONLY "public"."rank"
    ADD CONSTRAINT "rank_baseRankId_fkey" FOREIGN KEY ("baseRankId") REFERENCES "public"."base_rank"("id");



ALTER TABLE ONLY "public"."rank"
    ADD CONSTRAINT "rank_nextRank_fkey" FOREIGN KEY ("nextRank") REFERENCES "public"."rank"("id");



ALTER TABLE ONLY "public"."rank_point"
    ADD CONSTRAINT "rank_point_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES "public"."sport"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."rank_point"
    ADD CONSTRAINT "rank_point_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."rank_school"
    ADD CONSTRAINT "rank_school_rankId_fkey" FOREIGN KEY ("rankId") REFERENCES "public"."rank"("id");



ALTER TABLE ONLY "public"."rank_school"
    ADD CONSTRAINT "rank_school_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id");



ALTER TABLE ONLY "public"."rank"
    ADD CONSTRAINT "rank_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES "public"."sport"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."rank_student"
    ADD CONSTRAINT "rank_student_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "public"."profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."rank_student"
    ADD CONSTRAINT "rank_student_rankId_fkey" FOREIGN KEY ("rankId") REFERENCES "public"."rank"("id");



ALTER TABLE ONLY "public"."rank_student"
    ADD CONSTRAINT "rank_student_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES "public"."sport"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."sale"
    ADD CONSTRAINT "sale_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "public"."profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."sale"
    ADD CONSTRAINT "sale_packId_fkey" FOREIGN KEY ("packId") REFERENCES "public"."pack"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."sale_root"
    ADD CONSTRAINT "sale_root_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "public"."profile"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."sale_root"
    ADD CONSTRAINT "sale_root_packId_fkey" FOREIGN KEY ("packId") REFERENCES "public"."pack"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."sale_root"
    ADD CONSTRAINT "sale_root_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."sale"
    ADD CONSTRAINT "sale_saleRootId_fkey" FOREIGN KEY ("saleRootId") REFERENCES "public"."sale_root"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."sale"
    ADD CONSTRAINT "sale_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."schedule"
    ADD CONSTRAINT "schedule_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."school_settings"
    ADD CONSTRAINT "school_settings_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."service_equipment"
    ADD CONSTRAINT "service_equipment_equipmentId_fk" FOREIGN KEY ("equipmentId") REFERENCES "public"."equipment"("id");



ALTER TABLE ONLY "public"."service_equipment"
    ADD CONSTRAINT "service_equipment_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."service_teacher"
    ADD CONSTRAINT "service_instructor_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "public"."profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."service_teacher"
    ADD CONSTRAINT "service_instructor_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."service_student"
    ADD CONSTRAINT "service_profile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "public"."profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."service_student"
    ADD CONSTRAINT "service_profile_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."service"
    ADD CONSTRAINT "service_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."service"
    ADD CONSTRAINT "service_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES "public"."sport"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."sport_profile"
    ADD CONSTRAINT "sport_profile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "public"."profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."sport_profile"
    ADD CONSTRAINT "sport_profile_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES "public"."sport"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."sport_school"
    ADD CONSTRAINT "sport_school_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."sport_school"
    ADD CONSTRAINT "sport_school_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES "public"."sport"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."subscription"
    ADD CONSTRAINT "subscription_asaas_planId_fkey" FOREIGN KEY ("planId") REFERENCES "public"."plan"("id");



ALTER TABLE ONLY "public"."subscription"
    ADD CONSTRAINT "subscription_asaas_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "public"."profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."teacher_schedule"
    ADD CONSTRAINT "teacher_schedule_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "public"."profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."teacher_schedule"
    ADD CONSTRAINT "teacher_schedule_scheduleId_fkey" FOREIGN KEY ("scheduleId") REFERENCES "public"."schedule"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."teacher_schedule"
    ADD CONSTRAINT "teacher_schedule_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_pwa"
    ADD CONSTRAINT "user_pwa_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "public"."profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_pwa"
    ADD CONSTRAINT "user_pwa_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."video"
    ADD CONSTRAINT "video_schoolId_fkey1" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id");



ALTER TABLE "public"."attendance" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."base_rank" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."booking" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."booking_equipment" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."cancelation_policy" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."category" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."course" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."debt" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."debt_category" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."debt_root" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."equipment" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."error" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."face" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."lead" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."lesson" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."log" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."module" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."pack" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."pack_profile" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."payment" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."plan" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."profile" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."progress" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."rank" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."rank_point" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."rank_school" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."rank_student" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."sale" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."sale_root" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."schedule" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."school" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."school_settings" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."service" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."service_equipment" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."service_pack" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."service_plan" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."service_student" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."service_teacher" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."sport" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."sport_profile" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."sport_school" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."subscription" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."teacher_schedule" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_pwa" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."video" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";








GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";
GRANT USAGE ON SCHEMA "public" TO "supabase_auth_admin";


















































































































































































































































































































GRANT ALL ON FUNCTION "public"."friends_attendance"("service" "uuid", "exclude" "uuid", "schedule" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."friends_attendance"("service" "uuid", "exclude" "uuid", "schedule" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."friends_attendance"("service" "uuid", "exclude" "uuid", "schedule" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_all_bookings"("school" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_all_bookings"("school" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_all_bookings"("school" "uuid") TO "service_role";



GRANT ALL ON TABLE "public"."schedule" TO "anon";
GRANT ALL ON TABLE "public"."schedule" TO "authenticated";
GRANT ALL ON TABLE "public"."schedule" TO "service_role";



GRANT ALL ON FUNCTION "public"."get_unique_teacher_schedules_by_service"("service" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_unique_teacher_schedules_by_service"("service" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_unique_teacher_schedules_by_service"("service" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_upcoming_birthdays"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_upcoming_birthdays"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_upcoming_birthdays"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_upcoming_birthdays"("school" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_upcoming_birthdays"("school" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_upcoming_birthdays"("school" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."mark_expired_bookings"() TO "anon";
GRANT ALL ON FUNCTION "public"."mark_expired_bookings"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."mark_expired_bookings"() TO "service_role";



GRANT ALL ON FUNCTION "public"."verify_pack_expiration"() TO "anon";
GRANT ALL ON FUNCTION "public"."verify_pack_expiration"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."verify_pack_expiration"() TO "service_role";






























GRANT ALL ON TABLE "public"."attendance" TO "anon";
GRANT ALL ON TABLE "public"."attendance" TO "authenticated";
GRANT ALL ON TABLE "public"."attendance" TO "service_role";



GRANT ALL ON TABLE "public"."profile" TO "anon";
GRANT ALL ON TABLE "public"."profile" TO "authenticated";
GRANT ALL ON TABLE "public"."profile" TO "service_role";



GRANT ALL ON TABLE "public"."attendance_monthly" TO "anon";
GRANT ALL ON TABLE "public"."attendance_monthly" TO "authenticated";
GRANT ALL ON TABLE "public"."attendance_monthly" TO "service_role";



GRANT ALL ON TABLE "public"."attendance_yearly" TO "anon";
GRANT ALL ON TABLE "public"."attendance_yearly" TO "authenticated";
GRANT ALL ON TABLE "public"."attendance_yearly" TO "service_role";



GRANT ALL ON TABLE "public"."base_rank" TO "anon";
GRANT ALL ON TABLE "public"."base_rank" TO "authenticated";
GRANT ALL ON TABLE "public"."base_rank" TO "service_role";



GRANT ALL ON TABLE "public"."booking" TO "anon";
GRANT ALL ON TABLE "public"."booking" TO "authenticated";
GRANT ALL ON TABLE "public"."booking" TO "service_role";



GRANT ALL ON TABLE "public"."booking_equipment" TO "anon";
GRANT ALL ON TABLE "public"."booking_equipment" TO "authenticated";
GRANT ALL ON TABLE "public"."booking_equipment" TO "service_role";



GRANT ALL ON TABLE "public"."cancelation_policy" TO "anon";
GRANT ALL ON TABLE "public"."cancelation_policy" TO "authenticated";
GRANT ALL ON TABLE "public"."cancelation_policy" TO "service_role";



GRANT ALL ON TABLE "public"."category" TO "anon";
GRANT ALL ON TABLE "public"."category" TO "authenticated";
GRANT ALL ON TABLE "public"."category" TO "service_role";



GRANT ALL ON TABLE "public"."course" TO "anon";
GRANT ALL ON TABLE "public"."course" TO "authenticated";
GRANT ALL ON TABLE "public"."course" TO "service_role";



GRANT ALL ON TABLE "public"."debt" TO "anon";
GRANT ALL ON TABLE "public"."debt" TO "authenticated";
GRANT ALL ON TABLE "public"."debt" TO "service_role";



GRANT ALL ON TABLE "public"."debt_category" TO "anon";
GRANT ALL ON TABLE "public"."debt_category" TO "authenticated";
GRANT ALL ON TABLE "public"."debt_category" TO "service_role";



GRANT ALL ON TABLE "public"."debt_root" TO "anon";
GRANT ALL ON TABLE "public"."debt_root" TO "authenticated";
GRANT ALL ON TABLE "public"."debt_root" TO "service_role";



GRANT ALL ON TABLE "public"."equipment" TO "anon";
GRANT ALL ON TABLE "public"."equipment" TO "authenticated";
GRANT ALL ON TABLE "public"."equipment" TO "service_role";



GRANT ALL ON TABLE "public"."error" TO "anon";
GRANT ALL ON TABLE "public"."error" TO "authenticated";
GRANT ALL ON TABLE "public"."error" TO "service_role";



GRANT ALL ON TABLE "public"."face" TO "anon";
GRANT ALL ON TABLE "public"."face" TO "authenticated";
GRANT ALL ON TABLE "public"."face" TO "service_role";



GRANT ALL ON TABLE "public"."lead" TO "anon";
GRANT ALL ON TABLE "public"."lead" TO "authenticated";
GRANT ALL ON TABLE "public"."lead" TO "service_role";



GRANT ALL ON TABLE "public"."lesson" TO "anon";
GRANT ALL ON TABLE "public"."lesson" TO "authenticated";
GRANT ALL ON TABLE "public"."lesson" TO "service_role";



GRANT ALL ON TABLE "public"."log" TO "anon";
GRANT ALL ON TABLE "public"."log" TO "authenticated";
GRANT ALL ON TABLE "public"."log" TO "service_role";



GRANT ALL ON SEQUENCE "public"."log_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."log_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."log_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."module" TO "anon";
GRANT ALL ON TABLE "public"."module" TO "authenticated";
GRANT ALL ON TABLE "public"."module" TO "service_role";



GRANT ALL ON TABLE "public"."pack" TO "anon";
GRANT ALL ON TABLE "public"."pack" TO "authenticated";
GRANT ALL ON TABLE "public"."pack" TO "service_role";



GRANT ALL ON TABLE "public"."pack_profile" TO "anon";
GRANT ALL ON TABLE "public"."pack_profile" TO "authenticated";
GRANT ALL ON TABLE "public"."pack_profile" TO "service_role";



GRANT ALL ON SEQUENCE "public"."pack_profile_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."pack_profile_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."pack_profile_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."payment" TO "anon";
GRANT ALL ON TABLE "public"."payment" TO "authenticated";
GRANT ALL ON TABLE "public"."payment" TO "service_role";



GRANT ALL ON TABLE "public"."plan" TO "anon";
GRANT ALL ON TABLE "public"."plan" TO "authenticated";
GRANT ALL ON TABLE "public"."plan" TO "service_role";



GRANT ALL ON TABLE "public"."progress" TO "anon";
GRANT ALL ON TABLE "public"."progress" TO "authenticated";
GRANT ALL ON TABLE "public"."progress" TO "service_role";



GRANT ALL ON TABLE "public"."rank" TO "anon";
GRANT ALL ON TABLE "public"."rank" TO "authenticated";
GRANT ALL ON TABLE "public"."rank" TO "service_role";



GRANT ALL ON TABLE "public"."rank_point" TO "anon";
GRANT ALL ON TABLE "public"."rank_point" TO "authenticated";
GRANT ALL ON TABLE "public"."rank_point" TO "service_role";



GRANT ALL ON TABLE "public"."rank_school" TO "anon";
GRANT ALL ON TABLE "public"."rank_school" TO "authenticated";
GRANT ALL ON TABLE "public"."rank_school" TO "service_role";



GRANT ALL ON TABLE "public"."rank_student" TO "anon";
GRANT ALL ON TABLE "public"."rank_student" TO "authenticated";
GRANT ALL ON TABLE "public"."rank_student" TO "service_role";



GRANT ALL ON TABLE "public"."sale" TO "anon";
GRANT ALL ON TABLE "public"."sale" TO "authenticated";
GRANT ALL ON TABLE "public"."sale" TO "service_role";



GRANT ALL ON TABLE "public"."sale_root" TO "anon";
GRANT ALL ON TABLE "public"."sale_root" TO "authenticated";
GRANT ALL ON TABLE "public"."sale_root" TO "service_role";



GRANT ALL ON TABLE "public"."school" TO "anon";
GRANT ALL ON TABLE "public"."school" TO "authenticated";
GRANT ALL ON TABLE "public"."school" TO "service_role";



GRANT ALL ON TABLE "public"."school_settings" TO "anon";
GRANT ALL ON TABLE "public"."school_settings" TO "authenticated";
GRANT ALL ON TABLE "public"."school_settings" TO "service_role";



GRANT ALL ON TABLE "public"."service" TO "anon";
GRANT ALL ON TABLE "public"."service" TO "authenticated";
GRANT ALL ON TABLE "public"."service" TO "service_role";



GRANT ALL ON TABLE "public"."service_equipment" TO "anon";
GRANT ALL ON TABLE "public"."service_equipment" TO "authenticated";
GRANT ALL ON TABLE "public"."service_equipment" TO "service_role";



GRANT ALL ON TABLE "public"."service_pack" TO "anon";
GRANT ALL ON TABLE "public"."service_pack" TO "authenticated";
GRANT ALL ON TABLE "public"."service_pack" TO "service_role";



GRANT ALL ON SEQUENCE "public"."service_pack_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."service_pack_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."service_pack_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."service_plan" TO "anon";
GRANT ALL ON TABLE "public"."service_plan" TO "authenticated";
GRANT ALL ON TABLE "public"."service_plan" TO "service_role";



GRANT ALL ON SEQUENCE "public"."service_plan_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."service_plan_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."service_plan_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."service_student" TO "anon";
GRANT ALL ON TABLE "public"."service_student" TO "authenticated";
GRANT ALL ON TABLE "public"."service_student" TO "service_role";



GRANT ALL ON SEQUENCE "public"."service_student_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."service_student_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."service_student_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."service_teacher" TO "anon";
GRANT ALL ON TABLE "public"."service_teacher" TO "authenticated";
GRANT ALL ON TABLE "public"."service_teacher" TO "service_role";



GRANT ALL ON TABLE "public"."sport" TO "anon";
GRANT ALL ON TABLE "public"."sport" TO "authenticated";
GRANT ALL ON TABLE "public"."sport" TO "service_role";



GRANT ALL ON TABLE "public"."sport_profile" TO "anon";
GRANT ALL ON TABLE "public"."sport_profile" TO "authenticated";
GRANT ALL ON TABLE "public"."sport_profile" TO "service_role";



GRANT ALL ON TABLE "public"."sport_school" TO "anon";
GRANT ALL ON TABLE "public"."sport_school" TO "authenticated";
GRANT ALL ON TABLE "public"."sport_school" TO "service_role";



GRANT ALL ON TABLE "public"."subscription" TO "anon";
GRANT ALL ON TABLE "public"."subscription" TO "authenticated";
GRANT ALL ON TABLE "public"."subscription" TO "service_role";



GRANT ALL ON TABLE "public"."teacher_schedule" TO "anon";
GRANT ALL ON TABLE "public"."teacher_schedule" TO "authenticated";
GRANT ALL ON TABLE "public"."teacher_schedule" TO "service_role";



GRANT ALL ON TABLE "public"."user_pwa" TO "anon";
GRANT ALL ON TABLE "public"."user_pwa" TO "authenticated";
GRANT ALL ON TABLE "public"."user_pwa" TO "service_role";



GRANT ALL ON TABLE "public"."video" TO "anon";
GRANT ALL ON TABLE "public"."video" TO "authenticated";
GRANT ALL ON TABLE "public"."video" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
