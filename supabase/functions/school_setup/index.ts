import { qrcode, } from 'https://deno.land/x/qrcode@v2.0.0/mod.ts';
import { createClient, } from 'jsr:@supabase/supabase-js@2';

// This function will be triggered by a database webhook when a new school is added
const handler =async (_request: Request): Promise<Response> => {
  try {
    // Create a Supabase client with the project URL and service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Parse the webhook payload
    const payload = await _request.json();

    // Verify this is an INSERT event on the school table
    if (payload.type !== 'INSERT' || payload.table !== 'school') {
      return new Response(
        JSON.stringify({ message: 'Not a school insert event', }),
        { status: 400, headers: { 'Content-Type': 'application/json', }, }
      );
    }

    // Extract the school data from the payload
    const schoolData = payload.record;

    if (!schoolData || !schoolData.domain) {
      return new Response(
        JSON.stringify({ error: 'Missing school domain', }),
        { status: 400, headers: { 'Content-Type': 'application/json', }, }
      );
    }

    // Sanitize the domain to ensure it's a valid bucket name
    // Bucket names must be lowercase, between 3-63 characters, and can contain only letters, numbers, hyphens, and underscores
    const bucketName = schoolData.domain.toLowerCase().replace(/[^a-z0-9-_]/g, '');

    // Check if the bucket already exists
    const { data: buckets, error: bucketsError, } = await supabase.storage.listBuckets();

    if (bucketsError) {
      throw new Error(`Failed to list buckets: ${bucketsError.message}`);
    }

    const bucketExists = buckets.some(bucket => bucket.name === bucketName);

    // Create the bucket if it doesn't exist
    if (!bucketExists) {
      const { error: createBucketError, } = await supabase
        .storage
        .createBucket(bucketName, {
          public: true, // Make the bucket publicly accessible
        });

      if (createBucketError) {
        throw new Error(`Failed to create bucket: ${createBucketError.message}`);
      }

      // Create the required folders in the bucket
      // Note: In Supabase Storage, folders are virtual and are created by uploading a file with a path
      // We'll upload empty placeholder files to create the folders
      const folders = ['avatars', 'courses', 'faces', 'products',];

      for (const folder of folders) {
        const { error: folderError, } = await supabase
          .storage
          .from(bucketName)
          .upload(`${folder}/.placeholder`, new Uint8Array([]), {
            contentType: 'text/plain',
          });

        if (folderError && folderError.message !== 'The resource already exists') {
          console.error(`Failed to create folder ${folder}: ${folderError.message}`);
          // Continue even if folder creation fails
        }
      }
    }


    // Generate the URL for the QR code
    const qrCodeUrl = `https://${schoolData.domain}.meumestre.com.br/qrcode`;

    // Generate QR code as a data URL
    const qrCodeDataUrl = await qrcode(qrCodeUrl);

    // Extract the base64 data from the data URL
    const base64Data = qrCodeDataUrl.split(',')[1];

    // Decode the base64 data to binary
    const qrCodeBinary = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0));

    // Define the filename for the QR code using the school ID or domain
    const filename = 'qr-code.png';

    // Upload the QR code to Supabase Storage
    const { error, } = await supabase
      .storage
      .from(schoolData.domain)
      .upload(filename, qrCodeBinary, {
        contentType: 'image/png',
        cacheControl: '3600',
        upsert: true, // Overwrite if exists
      });

    if (error) {
      throw new Error(`Failed to upload to storage: ${error.message}`);
    }

    return new Response(null, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Error processing school webhook:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to process school webhook', }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', },
      }
    );
  }
};

Deno.serve(handler);
