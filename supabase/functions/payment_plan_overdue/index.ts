import React from 'npm:react@18.3.1';
import { renderAsync, } from 'npm:@react-email/components@0.0.22';
import { Email, } from './_templates/email.tsx';
import { EmailAdmin, } from './_templates/email-admin.tsx';
import { createClient, } from 'jsr:@supabase/supabase-js@2';

const handler = async (_request: Request): Promise<Response> => {
  const supabase = createClient(
    Deno.env.get('NEXT_PUBLIC_SUPABASE_URL') ?? '',
    Deno.env.get('ROLE_KEY') ?? '',
  );

  const json = await _request.json();
  const { payment, planId, profile, } = json;


  console.log('🚀 ~ handler ~ payment:', payment);
  console.log('🚀 ~ handler ~ planId:', planId);
  console.log('🚀 ~ handler ~ profile:', profile);

  const { data: plan, error: planError, } = await supabase.from('plan').select().match({ id: planId, }).maybeSingle();
  console.log('🚀 ~ handler ~ plan:', plan);
  if (planError) {
    await supabase.from('error').insert({ value: JSON.stringify({ planError, }), });
    return new Response(JSON.stringify({ message: 'Erro na busca do plano.', }),  { status: 400, });
  }
  console.log('🚀 ~ handler ~ plan:', plan);

  const { data: school, } = await supabase.from('school').select().match({ id: profile.schoolId, }).maybeSingle();
  console.log('🚀 ~ handler ~ school:', school);

  const html = await renderAsync(
    React.createElement(Email, { payment, plan, school, profile, })
  );

  await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Deno.env.get('RESEND_API_KEY')}`,
    },
    body: JSON.stringify({
      from: 'Meu Mestre <<EMAIL>>',
      to: profile.email,
      subject: 'Notificação de pagamento.',
      html: html,
    }),
  });

  const { data: schoolAdmin, } = await supabase.from('profile').select().match({ type: 'admin', schoolId: profile.schoolId, }).maybeSingle();
  console.log('🚀 ~ handler ~ schoolAdmin:', schoolAdmin);

  const htmlAdmin = await renderAsync(
    React.createElement(EmailAdmin, { payment, plan, school, profile, })
  );

  await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Deno.env.get('RESEND_API_KEY')}`,
    },
    body: JSON.stringify({
      from: 'Meu Mestre <<EMAIL>>',
      to: schoolAdmin.email,
      subject: 'Notificação de pagamento.',
      html: htmlAdmin,
    }),
  });

  return new Response(JSON.stringify(payment));
};

Deno.serve(handler);