import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Link,
  Preview,
  Text,
} from 'npm:@react-email/components@0.0.22';
import * as React from 'npm:react@18.3.1';
import { format, } from 'npm:date-fns/format';

const formatToBrazilianReal = (numericValue: number | string) => {
  const formattedValue = numericValue.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL', });
  return formattedValue;
};

const billingTypes = {
  BOLETO: 'Boleto',
  PIX: 'Pix',
  CREDIT_CARD: 'Cartão',
};

export const EmailAdmin = ({ payment, plan, school, profile, }: any) => (
  <Html>
    <Head />
    <Preview>{school.name}</Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>{school.name} - Meu Mestre</Heading>
        <Text style={{ ...text, }}>
          <PERSON><PERSON>(a) {profile.name} ainda não realizou o pagamento recorrente do plano {plan.name}.
        </Text>
        <Text style={{ ...text, fontSize: '18px', }}>
          <strong>Detalhes do pagamento: </strong><br/>
          Produto: {plan.name}<br/>
          Preço: {formatToBrazilianReal(payment.value)}<br/>
          Vencimento: {format(payment.dueDate, 'dd-MM-yyyy')}<br/>
          Método de pagamento: {billingTypes[payment.billingType]}<br/>
        </Text>
        <Link
          href={`https://${school.domain}.meumestre.com.br/alunos/${profile.id}`}
          target="_blank"
          style={{ ...link, }}
        >
          Ver perfil do aluno
        </Link>
        <div style={{ marginTop: '100px', width: '100%', textAlign: 'center', }}>
          <Text style={{ ...text, fontSize: '12px', lineHeight: '12px', margin: '2px 0', }}>
            Meu Mestre - Tecnologia gerencial
          </Text>
          <Link
            target="_blank"
            href='https://meumestre.com.br/politica-de-privacidade' style={{ ...text, fontSize: '12px', lineHeight: '12px', margin: '2px 0', }}>
            Acesse nossa política de privacidade.
          </Link><br/>
          <Link
            target="_blank"
            href='https://meumestre.com.br/termos-de-uso' style={{ ...text, fontSize: '12px', lineHeight: '12px', margin: '2px 0', }}>
            Acesse nossos termos de uso.
          </Link><br/>
          <Text style={{ ...text, fontSize: '12px', lineHeight: '12px', margin: '2px 0', }}>
            <EMAIL> (85) 997309676
          </Text>
        </div>
      </Container>
    </Body>
  </Html>
);

export default EmailAdmin;

const main = {
  backgroundColor: '#f4f4f5',
};

const container = {
  padding: '2vw 10vw 10vw',
  maxWidth: '100%',
  backgroundColor: '#f4f4f5',
  textAlign: 'justify',
  fontSize: '16px',
  fontFamily: 'sans-serif',
  color: '#09090b',
};

const h1 = {
  fontSize: '34px',
  marginBottom: '60px',
  textAlign: 'center',
};

const link = {
  width: '100%',
  display: 'block',
  border: 'none',
  backgroundColor: '#09090b',
  borderRadius: '6px',
  color: 'white',
  textTransform: 'uppercase',
  textDecoration: 'none',
  padding: '14px 0',
  marginTop: '50px',
  textAlign: 'center',
};

const text = {
  margin: '30px 0',
  lineHeight: '28px',
  fontFamily: 'sans-serif',
  fontSize: '16px',
};
