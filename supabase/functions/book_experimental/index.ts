import React from 'npm:react@18.3.1';
import { renderAsync, } from 'npm:@react-email/components@0.0.22';
import { Email, } from './_templates/email.tsx';
import { EmailAdmin, } from './_templates/email-admin.tsx';
import { createClient, } from 'jsr:@supabase/supabase-js@2';

const handler = async (_request: Request): Promise<Response> => {
  const supabase = createClient(
    Deno.env.get('NEXT_PUBLIC_SUPABASE_URL') ?? '',
    Deno.env.get('ROLE_KEY') ?? '',
  );

  const json = await _request.json();
  const { day, schedule, service, school, email, name, } = json;

    if (!day || !schedule || !service || !school || !email || !name) {
      return new Response(
        JSON.stringify({ message: 'Dados faltando para o envio do e-mail.', }),
        { status: 400, }
      );
    }

  const { data: admin, } = await supabase.from('profile').select().match({ type: 'admin', schoolId: school.id, }).maybeSingle();

  let html = '';
  let htmlAdmin = '';

  html = await renderAsync(
      React.createElement(Email, { day, schedule, service, school, name, })
    );
  htmlAdmin = await renderAsync(
    React.createElement(EmailAdmin, { day, schedule, service, school, name, }));

      const response = await fetch('https://api.brevo.com/v3/smtp/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'api-key': Deno.env.get('BREVO_API_KEY'),
        },
        body: JSON.stringify({
          sender: {
            name: 'Meu Mestre',
            email: '<EMAIL>',
          },
          to: [{ name, email, },],
          subject: 'Seu agendamento foi realizado!',
          htmlContent: html,
        }),
      });

      await fetch('https://api.brevo.com/v3/smtp/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'api-key': Deno.env.get('BREVO_API_KEY'),
        },
        body: JSON.stringify({
          sender: {
            name: 'Meu Mestre',
            email: '<EMAIL>',
          },
          to: [{ name: admin.name, email: admin.email, },],
          subject: 'Um cliente realizou um agendamento!',
          htmlContent: htmlAdmin,
        }),
      });
  const data = await response.json();

  return new Response(JSON.stringify(data), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

Deno.serve(handler);