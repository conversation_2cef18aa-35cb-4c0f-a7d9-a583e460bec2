import { createClient, } from 'jsr:@supabase/supabase-js@2';
import { addMonths, format, } from 'npm:date-fns';


const handler = async (_request: Request): Promise<Response> => {
  const { record: saleRoot, } = await _request.json();

  const supabase = createClient(
    Deno.env.get('NEXT_PUBLIC_SUPABASE_URL') ?? '',
    Deno.env.get('ROLE_KEY') ?? '',
  );

  Array.from({ length: saleRoot.installments, }, (_, i) => i).map(async (_: any, index: number) => {
    let date = addMonths(saleRoot.billingDate, index);

    const { data: savedSale, error, } = await supabase
      .from('sale')
      .insert({
        name: saleRoot.name,
        description: saleRoot.description,
        value: saleRoot.value,
        schoolId: saleRoot.schoolId,
        method: saleRoot.method,
        saleRootId: saleRoot.id,
        clientId: saleRoot.clientId,
        packId: saleRoot.packId,
        billingDate: format(date, 'yyyy-MM-dd'),
      });
    console.log('🚀 ~ Array.from ~ error:', error);
    console.log('🚀 ~ Array.from ~ savedSale:', savedSale);
  });

  return new Response(null, { status: 200, });
};

Deno.serve(handler);