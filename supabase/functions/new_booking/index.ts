import React from 'npm:react@18.3.1';
import { renderAsync, } from 'npm:@react-email/components@0.0.22';
import { Email, } from './_templates/email.tsx';
import { EmailAdmin, } from './_templates/email-admin.tsx';
import { createClient, } from 'jsr:@supabase/supabase-js@2';
import { EmailCancel, } from './_templates/email-cancel.tsx';
import { EmailAdminCancel, } from './_templates/email-admin-cancel.tsx';

const handler = async (_request: Request): Promise<Response> => {
  const supabase = createClient(
    Deno.env.get('NEXT_PUBLIC_SUPABASE_URL') ?? '',
    Deno.env.get('ROLE_KEY') ?? '',
  );
  const { type, record, } = await _request.json();

  const isCanceled = type === 'UPDATE' && record.status === 'canceled';

  const { day, scheduleId, serviceId, userId, } = record;

  const { data: schedule, } = await supabase.from('schedule').select().match({ id: scheduleId, }).maybeSingle();
  const { data: user, } = await supabase.from('profile').select().match({ id: userId, }).maybeSingle();
  const { data: admin, } = await supabase.from('profile').select().match({ type: 'admin', schoolId: user.schoolId, }).maybeSingle();
  const { data: service, } = await supabase.from('service').select().match({ id: serviceId, }).maybeSingle();
  const { data: school, } = await supabase.from('school').select().match({ id: user.schoolId, }).maybeSingle();

  if (isCanceled) {
    // mark pack_profile purchaseStatus as active if is marked as used
    await supabase
      .from('pack_profile')
      .update({ purchaseStatus: 'active', })
      .select()
      .match({ id: record.packProfileId, purchaseStatus: 'used', })
      .maybeSingle();
  }

  if (record.status === 'used' || record.status === 'expired' || (type === 'INSERT' && record.status === 'approved')) {
    const { count, } = await supabase
      .from('booking')
      .select('*', { count: 'exact', head: true, })
      .eq('packProfileId', record.packProfileId)
      .in('status', ['approved', 'bailed', 'expired', 'missed', 'pending', 'used', ]);

    if (count !== null) {
      const { data: packProfile, } = await supabase
        .from('pack_profile')
        .select('*, pack(id, use)')
        .match({ id: record.packProfileId, })
        .maybeSingle();

      if ((count + packProfile.used) >= packProfile.pack.use) {
        await supabase
          .from('pack_profile')
          .update({ purchaseStatus: 'used', })
          .match({ id: record.packProfileId, });
      }
    }
  }

  // ENVIO DE EMAIL
  const ignoredStatuses = type === 'UPDATE' && (record.status === 'expired' || record.status === 'used' || record.status === 'bailed' || record.status === 'missed');
  if (ignoredStatuses) return new Response(null, {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  let html = '';
  let htmlAdmin = '';
  console.log('🚀 ~ handler ~ record:', record);
  console.log('🚀 ~ handler ~ type:', type);


  html = await renderAsync(
    React.createElement(isCanceled ? EmailCancel : Email, { day, schedule, service, school, user, })
  );
  htmlAdmin = await renderAsync(
    React.createElement(isCanceled ? EmailAdminCancel : EmailAdmin, { day, schedule, service, school, user, })
  );

  const subject = isCanceled ? 'Cancelamento de agendamento.' : 'Confirmação de agendamento.';

  const response = await fetch('https://api.brevo.com/v3/smtp/email', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'api-key': Deno.env.get('BREVO_API_KEY'),
    },
    body: JSON.stringify({
      sender: {
        name: 'Meu Mestre',
        email: '<EMAIL>',
      },
      to: [{ name: user.name, email: user.email, },],
      subject,
      htmlContent: html,
    }),
  });
  console.log('🚀 ~ handler ~ response:', response);

  await fetch('https://api.brevo.com/v3/smtp/email', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'api-key': Deno.env.get('BREVO_API_KEY'),
    },
    body: JSON.stringify({
      sender: {
        name: 'Meu Mestre',
        email: '<EMAIL>',
      },
      to: [{ name: admin.name, email: admin.email, },],
      subject,
      htmlContent: htmlAdmin,
    }),
  });


  return new Response(null, {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

Deno.serve(handler);