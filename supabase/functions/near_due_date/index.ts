import React from 'npm:react@18.3.1';
import { createClient, } from 'jsr:@supabase/supabase-js@2';
import { addDays, getDate, } from 'npm:date-fns';
import { renderAsync, } from 'npm:@react-email/components@0.0.22';
import { Email, } from './_templates/email.tsx';

const handler = async (): Promise<Response> => {
  const supabase = createClient(
    Deno.env.get('NEXT_PUBLIC_SUPABASE_URL') ?? '',
    Deno.env.get('ROLE_KEY') ?? '',
  );

  let today = new Date();
  today.setUTCHours(today.getUTCHours() - 3);
  let inThreeDays = addDays(today, 3);

  const { data: subscription, } = await supabase
    .from('subscription')
    .select('*, plan(*), profile(*, school(name, domain))')
    .eq('status', 'ACTIVE')
    .or(`billingDay.eq.${getDate(today)},billingDay.eq.${getDate(inThreeDays)}`);

    if (subscription && subscription.length > 0) {
      await subscription.map(async subscription => {
        console.log('🚀 ~ handler ~ subscription:', subscription);
        const schoolName = subscription?.profile?.school?.name;
        const schoolDomain = subscription?.profile?.school?.domain;
        const dueDay = getDate(subscription.nextDueDate);
        const planName = subscription?.plan?.name;
        const profileName = subscription.profile.name;
        const html = await renderAsync(
        React.createElement(Email, {
          isToday: subscription.nextDueDate === today,
          schoolName,
          schoolDomain,
          dueDay,
          planName,
          profileName,
        })
      );

      await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${Deno.env.get('RESEND_API_KEY')}`,
        },
        body: JSON.stringify({
          from: 'Meu Mestre <<EMAIL>>',
          to: subscription?.profile?.email,
          subject: 'Lembrete de mensalidade.',
          html: html,
        }),
      });

      const response = await fetch(`https://${schoolDomain}.meumestre.com.br/api/push-notifications/due-date`, {
        method: 'POST',
        body: JSON.stringify({
          isToday: subscription.nextDueDate === today,
          schoolName,
          schoolDomain,
          dueDay,
          planName,
          profileId: subscription?.profile?.id,
        }),
        headers: {
          'api-access-token': `${Deno.env.get('API_ACCESS_TOKEN')}`,
        },
      });
      console.log('🚀 ~ handler ~ response:', await response.json());
    });
  }

  return new Response(null, {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

Deno.serve(handler);