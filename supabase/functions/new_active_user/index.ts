import React from 'npm:react@18.3.1';
import { renderAsync, } from 'npm:@react-email/components@0.0.22';
import { EmailAdmin, } from './_templates/email-admin.tsx';
import { createClient, } from 'jsr:@supabase/supabase-js@2';

const handler = async (_request: Request): Promise<Response> => {
  const supabase = createClient(
    Deno.env.get('NEXT_PUBLIC_SUPABASE_URL') ?? '',
    Deno.env.get('ROLE_KEY') ?? ''
  );

  const json = await _request.json();
  const { userId, } = json;

  const { data: user, } = await supabase
    .from('profile')
    .select('id, email, name, schoolId')
    .eq('id', userId)
    .maybeSingle();

  const { data: admin, } = await supabase
    .from('profile')
    .select('email')
    .match({ type: 'admin', schoolId: user.schoolId, })
    .maybeSingle();

  const { data: school, } = await supabase
    .from('school')
    .select()
    .match({ id: user.schoolId, })
    .maybeSingle();

  const htmlAdmin = await renderAsync(
    React.createElement(EmailAdmin, { user, school, })
  );

  const emailResponse = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Deno.env.get('RESEND_API_KEY')}`,
    },
    body: JSON.stringify({
      from: 'Meu Mestre <<EMAIL>>',
      to: admin.email,
      subject: 'Novo usuário confirmado!',
      html: htmlAdmin,
    }),
  });

    const data = await emailResponse.json();

  return new Response(JSON.stringify(data), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

Deno.serve(handler);
