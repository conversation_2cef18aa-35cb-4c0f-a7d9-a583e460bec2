import React from 'npm:react@18.3.1';
import { createClient, } from 'https://esm.sh/@supabase/supabase-js';
import { Email, } from './_templates/email.tsx';
import { EmailAdmin, } from './_templates/email-admin.tsx';
import { renderAsync, } from 'npm:@react-email/components@0.0.22';

const handler = async (_request: Request): Promise<Response> => {
  const { record: subscription, } = await _request.json();

  const supabase = createClient(
    Deno.env.get('NEXT_PUBLIC_SUPABASE_URL') ?? '',
    Deno.env.get('ROLE_KEY') ?? '',
  );

  // Fetch the user's data from the auth.users table
  const { data: { user, }, } = await supabase.auth.admin.getUserById(
    subscription.profileId
  );

  const { data: admin, } = await supabase
    .from('profile')
    .select()
    .match({ type: 'admin', schoolId: user.user_metadata.schoolId, })
    .maybeSingle();
  console.log('🚀 ~ handler ~ admin:', admin);

  const { data: school, } = await supabase
    .from('school')
    .select()
    .match({ id: user.user_metadata.schoolId, })
    .maybeSingle();
  console.log('🚀 ~ handler ~ school:', school);

  console.log('🚀 ~ handler ~ subscription:', subscription);
  const html = await renderAsync(
    React.createElement(Email, { subscription, school, user, })
  );

  const res = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Deno.env.get('RESEND_API_KEY')}`,
    },
    body: JSON.stringify({
      from: 'Meu Mestre <<EMAIL>>',
      to: user?.email,
      subject: 'Assinatura Meu Mestre',
      html,
    }),
  });

  const data = await res.json();

  const htmlAdmin = await renderAsync(
    React.createElement(EmailAdmin, { subscription, school, user, })
  );

  await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Deno.env.get('RESEND_API_KEY')}`,
    },
    body: JSON.stringify({
      from: 'Meu Mestre <<EMAIL>>',
      to: admin?.email,
      subject: 'Assinatura Meu Mestre',
      html: htmlAdmin,
    }),
  });

  return new Response(JSON.stringify(data), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

Deno.serve(handler);