import React from 'npm:react@18.3.1';
import { renderAsync, } from 'npm:@react-email/components@0.0.22';
import { Email, } from './_templates/email.tsx';
import { EmailAdmin, } from './_templates/email-admin.tsx';
import { createClient, } from 'jsr:@supabase/supabase-js@2';

const handler = async (_request: Request): Promise<Response> => {
  const supabase = createClient(
    Deno.env.get('NEXT_PUBLIC_SUPABASE_URL') ?? '',
    Deno.env.get('ROLE_KEY') ?? '',
  );

  const json = await _request.json();
  const { pack, payment, profile, } = json;

  const { data: school, } = await supabase
    .from('school')
    .select()
    .match({ id: profile.schoolId, })
    .maybeSingle();
  console.log('🚀 ~ handler ~ school:', school);

  const html = await renderAsync(
    React.createElement(Email, { payment, product: pack, school, profile, })
  );

  await fetch('https://api.brevo.com/v3/smtp/email', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'api-key': Deno.env.get('BREVO_API_KEY'),
    },
    body: JSON.stringify({
      sender: {
        name: 'Meu Mestre',
        email: '<EMAIL>'
      },
      to: [{ name: profile.name, email: profile.email }],
      subject: 'Notificação de pagamento.',
      htmlContent: html,
    }),
  });

  const { data: schoolAdmin, } = await supabase
    .from('profile')
    .select()
    .match({ type: 'admin', schoolId: profile.schoolId, })
    .maybeSingle();
  console.log('🚀 ~ handler ~ schoolAdmin:', schoolAdmin);

  const htmlAdmin = await renderAsync(
    React.createElement(EmailAdmin, { payment, product: pack, school, profile, })
  );

  await fetch('https://api.brevo.com/v3/smtp/email', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'api-key': Deno.env.get('BREVO_API_KEY'),
    },
    body: JSON.stringify({
      sender: {
        name: 'Meu Mestre',
        email: '<EMAIL>',
      },
      to: [{ name: schoolAdmin.name, email: schoolAdmin.email }],
      subject: 'Notificação de pagamento.',
      htmlContent: htmlAdmin,
    }),
  });

  return new Response(JSON.stringify(payment));
};

Deno.serve(handler);