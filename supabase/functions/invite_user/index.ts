import React from 'npm:react@18.3.1';
import { renderAsync, } from 'npm:@react-email/components@0.0.22';
import { Email, } from './_templates/email.tsx';

const handler = async (_request: Request): Promise<Response> => {
  const json = await _request.json();
  const { email, name, school, type, } = json;
  console.log('🚀 ~ handler ~ json:', json);

  const html = await renderAsync(
    React.createElement(Email, { email, name, school, type, })
  );

  const emailResponse = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${Deno.env.get('RESEND_API_KEY')}`,
    },
    body: JSON.stringify({
      from: 'Meu Mestre <<EMAIL>>',
      to: email,
      subject: '<PERSON><PERSON><PERSON><PERSON>, seu convite chegou!',
      html: html,
    }),
  });

    const data = await emailResponse.json();

  return new Response(JSON.stringify(data), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

Deno.serve(handler);
