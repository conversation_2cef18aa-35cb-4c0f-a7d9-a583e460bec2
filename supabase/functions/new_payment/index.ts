import { createClient, } from 'jsr:@supabase/supabase-js@2';
import React from 'npm:react@18.3.1';
import { renderAsync, } from 'npm:@react-email/components@0.0.22';
import { Email, } from './_templates/email.tsx';
import { EmailPending, } from './_templates/email-pending.tsx';
import { EmailAdmin, } from './_templates/email-admin.tsx';
import { EmailAdminPending, } from './_templates/email-admin-pending.tsx';
import { addDays, } from 'npm:date-fns';

const handler = async (_request: Request): Promise<Response> => {
  const supabase = createClient(
    Deno.env.get('NEXT_PUBLIC_SUPABASE_URL') ?? '',
    Deno.env.get('ROLE_KEY') ?? '',
  );

  const { type, record: payment, } = await _request.json();
  console.log('🚀 ~ handler ~ payment:', payment);
  if (payment.subscription) return new Response(null, { status: 200, });

  const { data: profile, } = await supabase
    .from('profile')
    .select()
    .match({ asId: payment.profileAsId, })
    .maybeSingle();

  if (!profile) return new Response(null, { status: 400, });

  if (type === 'INSERT') {
    const { data: pack, } = await supabase
      .from('pack')
      .select()
      .match({ id: payment.packId, })
      .maybeSingle();

    if (!pack) return new Response(null, { status: 400, });

    const today = new Date();
    today.setUTCHours(today.getUTCHours() - 3);
    const { data, } = await supabase
      .from('pack_profile')
      .insert({
        packId: payment.packId,
        profileId: profile.id,
        paymentId: payment.id,
        ...((payment.status === 'confirmed' || payment.status === 'received') && { purchaseStatus: 'active', }),
        paymentStatus: payment.status,
        expireDate: addDays(today, pack.expires || 30),
      })
      .select();

    if (!data) return new Response(null, { status: 400, });
  }

  if (type === 'UPDATE') {
    const { data: pack, } = await supabase
      .from('pack')
      .select()
      .match({ id: payment.packId, })
      .maybeSingle();

    if (!pack) return new Response(null, { status: 400, });

    const today = new Date();
    today.setUTCHours(today.getUTCHours() - 3);
    const { data, error, } = await supabase
    .from('pack_profile')
    .update({
      ...((payment.status === 'confirmed' || payment.status === 'received') && { purchaseStatus: 'active', }),
      paymentStatus: payment.status,
      expireDate: addDays(today, pack.expires || 30),
    })
    .match({ paymentId: payment.id, })
    .select();
    console.log('🚀 ~ handler ~ error:', error);
    console.log('🚀 ~ handler ~ data:', data);

    if (!data) return new Response(null, { status: 400, });
  }


  if ((payment.paymentType === 'CREDIT_CARD' && payment.status === 'confirmed') || ((payment.paymentType === 'BOLETO' || payment.paymentType === 'PIX') && payment.status === 'received')) {
    const { data: school, } = await supabase
      .from('school')
      .select()
      .match({ id: profile.schoolId, })
      .maybeSingle();

    const { data: admin, } = await supabase
      .from('profile')
      .select()
      .match({ type: 'admin', schoolId: profile.schoolId, })
      .maybeSingle();

    const html = await renderAsync(
      React.createElement(Email, { payment, profile, school, })
    );

    await fetch('https://api.brevo.com/v3/smtp/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': Deno.env.get('BREVO_API_KEY'),
      },
      body: JSON.stringify({
        sender: {
          name: 'Meu Mestre',
          email: '<EMAIL>',
        },
        to: [{ name: profile.name, email: profile.email, },],
        subject: 'Confirmação de compra.',
        htmlContent: html,
      }),
    });

    const htmlAdmin = await renderAsync(
      React.createElement(EmailAdmin, { payment, profile, school, })
    );

    await fetch('https://api.brevo.com/v3/smtp/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': Deno.env.get('BREVO_API_KEY'),
      },
      body: JSON.stringify({
        sender: {
          name: 'Meu Mestre',
          email: '<EMAIL>',
        },
        to: [{ name: admin.name, email: admin.email, },],
        subject: 'Confirmação de compra.',
        htmlContent: htmlAdmin,
      }),
    });
  }

  if (payment.status === 'pending') {
    const { data: school, } = await supabase
      .from('school')
      .select()
      .match({ id: profile.schoolId, })
      .maybeSingle();

    const { data: admin, } = await supabase
      .from('profile')
      .select()
      .match({ type: 'admin', schoolId: profile.schoolId, })
      .maybeSingle();

    const html = await renderAsync(
      React.createElement(EmailPending, { payment, profile, school, })
    );

    await fetch('https://api.brevo.com/v3/smtp/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': Deno.env.get('BREVO_API_KEY'),
      },
      body: JSON.stringify({
        sender: {
          name: 'Meu Mestre',
          email: '<EMAIL>',
        },
        to: [{ name: profile.name, email: profile.email, },],
        subject: 'Cobrança pronta.',
        htmlContent: html,
      }),
    });

    const htmlAdmin = await renderAsync(
      React.createElement(EmailAdminPending, { payment, profile, school, })
    );

    await fetch('https://api.brevo.com/v3/smtp/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': Deno.env.get('BREVO_API_KEY'),
      },
      body: JSON.stringify({
        sender: {
          name: 'Meu Mestre',
          email: '<EMAIL>',
        },
        to: [{ name: admin.name, email: admin.email, },],
        subject: 'Cobrança pronta.',
        htmlContent: htmlAdmin,
      }),
    });
  }

  return new Response(null, { status: 200, });
};

Deno.serve(handler);