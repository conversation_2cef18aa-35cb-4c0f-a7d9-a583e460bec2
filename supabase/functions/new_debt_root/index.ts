import { createClient, } from 'jsr:@supabase/supabase-js@2';
import { addMonths, format, } from 'npm:date-fns';

const handler = async (_request: Request): Promise<Response> => {
  const { record: debtRoot, } = await _request.json();

  const supabase = createClient(
    Deno.env.get('NEXT_PUBLIC_SUPABASE_URL') ?? '',
    Deno.env.get('ROLE_KEY') ?? '',
  );

  Array.from({ length: debtRoot.repetitions || (debtRoot.fixed && 36) || 1, }, (_, i) => i).map(async (_: any, index: number) => {
    let date = addMonths(debtRoot.billingDate, index);

    await supabase
      .from('debt')
      .insert({
        name: debtRoot.name,
        description: debtRoot.description,
        value: debtRoot.value,
        paid: false,
        schoolId: debtRoot.schoolId,
        method: debtRoot.method,
        debtRootId: debtRoot.id,
        billingDate: format(date, 'yyyy-MM-dd'),
      });
  });
  return new Response(null, { status: 200, });
};

Deno.serve(handler);