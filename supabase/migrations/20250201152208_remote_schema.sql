create table "public"."lead" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "name" character varying not null,
    "email" character varying,
    "phone" character varying,
    "schoolId" uuid not null
);


alter table "public"."lead" enable row level security;

alter table "public"."booking" add column "leadId" uuid;

alter table "public"."booking" alter column "userId" drop not null;

CREATE UNIQUE INDEX lead_pkey ON public.lead USING btree (id);

alter table "public"."lead" add constraint "lead_pkey" PRIMARY KEY using index "lead_pkey";

alter table "public"."booking" add constraint "booking_leadId_fkey" FOREIGN KEY ("leadId") REFERENCES lead(id) ON DELETE SET NULL not valid;

alter table "public"."booking" validate constraint "booking_leadId_fkey";

alter table "public"."lead" add constraint "lead_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) ON DELETE SET NULL not valid;

alter table "public"."lead" validate constraint "lead_schoolId_fkey";

grant delete on table "public"."lead" to "anon";

grant insert on table "public"."lead" to "anon";

grant references on table "public"."lead" to "anon";

grant select on table "public"."lead" to "anon";

grant trigger on table "public"."lead" to "anon";

grant truncate on table "public"."lead" to "anon";

grant update on table "public"."lead" to "anon";

grant delete on table "public"."lead" to "authenticated";

grant insert on table "public"."lead" to "authenticated";

grant references on table "public"."lead" to "authenticated";

grant select on table "public"."lead" to "authenticated";

grant trigger on table "public"."lead" to "authenticated";

grant truncate on table "public"."lead" to "authenticated";

grant update on table "public"."lead" to "authenticated";

grant delete on table "public"."lead" to "service_role";

grant insert on table "public"."lead" to "service_role";

grant references on table "public"."lead" to "service_role";

grant select on table "public"."lead" to "service_role";

grant trigger on table "public"."lead" to "service_role";

grant truncate on table "public"."lead" to "service_role";

grant update on table "public"."lead" to "service_role";
