alter table "public"."subscription" drop constraint "subscription_pkey";

drop index if exists "public"."subscription_pkey";

alter table "public"."subscription" drop column "subscriptionId";

alter table "public"."subscription" add column "id" character varying not null;

CREATE UNIQUE INDEX subscription_pkey ON public.subscription USING btree (id);

alter table "public"."subscription" add constraint "subscription_pkey" PRIMARY KEY using index "subscription_pkey";


