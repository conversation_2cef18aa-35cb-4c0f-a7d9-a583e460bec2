create table "public"."package" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "name" character varying not null,
    "schoolId" uuid not null default gen_random_uuid(),
    "price" numeric not null,
    "active" boolean not null default true,
    "expires" smallint,
    "use" smallint
);


alter table "public"."package" enable row level security;

CREATE UNIQUE INDEX package_pkey ON public.package USING btree (id);

alter table "public"."package" add constraint "package_pkey" PRIMARY KEY using index "package_pkey";

alter table "public"."package" add constraint "package_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) not valid;

alter table "public"."package" validate constraint "package_schoolId_fkey";

grant delete on table "public"."package" to "anon";

grant insert on table "public"."package" to "anon";

grant references on table "public"."package" to "anon";

grant select on table "public"."package" to "anon";

grant trigger on table "public"."package" to "anon";

grant truncate on table "public"."package" to "anon";

grant update on table "public"."package" to "anon";

grant delete on table "public"."package" to "authenticated";

grant insert on table "public"."package" to "authenticated";

grant references on table "public"."package" to "authenticated";

grant select on table "public"."package" to "authenticated";

grant trigger on table "public"."package" to "authenticated";

grant truncate on table "public"."package" to "authenticated";

grant update on table "public"."package" to "authenticated";

grant delete on table "public"."package" to "service_role";

grant insert on table "public"."package" to "service_role";

grant references on table "public"."package" to "service_role";

grant select on table "public"."package" to "service_role";

grant trigger on table "public"."package" to "service_role";

grant truncate on table "public"."package" to "service_role";

grant update on table "public"."package" to "service_role";


