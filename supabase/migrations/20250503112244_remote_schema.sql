create table "public"."school_profile" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "profileId" uuid not null,
    "schoolId" uuid not null
);

alter table "public"."school_profile" enable row level security;

CREATE UNIQUE INDEX school_profile_pkey ON public.school_profile USING btree (id);

alter table "public"."school_profile" add constraint "school_profile_pkey" PRIMARY KEY using index "school_profile_pkey";

alter table "public"."school_profile" add constraint "school_profile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) not valid;

alter table "public"."school_profile" validate constraint "school_profile_profileId_fkey";

alter table "public"."school_profile" add constraint "school_profile_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) not valid;

alter table "public"."school_profile" validate constraint "school_profile_schoolId_fkey";

grant delete on table "public"."school_profile" to "anon";

grant insert on table "public"."school_profile" to "anon";

grant references on table "public"."school_profile" to "anon";

grant select on table "public"."school_profile" to "anon";

grant trigger on table "public"."school_profile" to "anon";

grant truncate on table "public"."school_profile" to "anon";

grant update on table "public"."school_profile" to "anon";

grant delete on table "public"."school_profile" to "authenticated";

grant insert on table "public"."school_profile" to "authenticated";

grant references on table "public"."school_profile" to "authenticated";

grant select on table "public"."school_profile" to "authenticated";

grant trigger on table "public"."school_profile" to "authenticated";

grant truncate on table "public"."school_profile" to "authenticated";

grant update on table "public"."school_profile" to "authenticated";

grant delete on table "public"."school_profile" to "service_role";

grant insert on table "public"."school_profile" to "service_role";

grant references on table "public"."school_profile" to "service_role";

grant select on table "public"."school_profile" to "service_role";

grant trigger on table "public"."school_profile" to "service_role";

grant truncate on table "public"."school_profile" to "service_role";

grant update on table "public"."school_profile" to "service_role";
