alter table "public"."authorized_payment_notification" drop constraint "authorized_payment_notification_pkey";

drop index if exists "public"."authorized_payment_notification_pkey";

alter table "public"."authorized_payment_notification" drop column "new_id";

alter table "public"."authorized_payment_notification" alter column "id" drop identity;

alter table "public"."authorized_payment_notification" alter column "id" set data type character varying using "id"::character varying;

CREATE UNIQUE INDEX authorized_payment_notification_pkey ON public.authorized_payment_notification USING btree (id);

alter table "public"."authorized_payment_notification" add constraint "authorized_payment_notification_pkey" PRIMARY KEY using index "authorized_payment_notification_pkey";


