alter table "public"."subscription" drop constraint "subscription_planId_fkey";

alter table "public"."subscription_asaas" drop constraint "subscription_asaas_externalReference_fkey";

alter table "public"."plan" drop constraint "plan_pkey";

drop index if exists "public"."plan_pkey";

alter table "public"."plan" alter column "new_id" set not null;

CREATE UNIQUE INDEX plan_pkey ON public.plan USING btree (new_id);

alter table "public"."plan" add constraint "plan_pkey" PRIMARY KEY using index "plan_pkey";


