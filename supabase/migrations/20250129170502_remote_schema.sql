revoke delete on table "public"."pkg" from "anon";

revoke insert on table "public"."pkg" from "anon";

revoke references on table "public"."pkg" from "anon";

revoke select on table "public"."pkg" from "anon";

revoke trigger on table "public"."pkg" from "anon";

revoke truncate on table "public"."pkg" from "anon";

revoke update on table "public"."pkg" from "anon";

revoke delete on table "public"."pkg" from "authenticated";

revoke insert on table "public"."pkg" from "authenticated";

revoke references on table "public"."pkg" from "authenticated";

revoke select on table "public"."pkg" from "authenticated";

revoke trigger on table "public"."pkg" from "authenticated";

revoke truncate on table "public"."pkg" from "authenticated";

revoke update on table "public"."pkg" from "authenticated";

revoke delete on table "public"."pkg" from "service_role";

revoke insert on table "public"."pkg" from "service_role";

revoke references on table "public"."pkg" from "service_role";

revoke select on table "public"."pkg" from "service_role";

revoke trigger on table "public"."pkg" from "service_role";

revoke truncate on table "public"."pkg" from "service_role";

revoke update on table "public"."pkg" from "service_role";

alter table "public"."pkg" drop constraint "pkg_schoolId_fkey";

alter table "public"."booking" drop constraint "booking_packageId_fkey";

alter table "public"."pack_profile" drop constraint "pkg_profile_pkgId_fkey";

alter table "public"."pack_profile" drop constraint "pkg_profile_profileid_pkgid_active_unique";

alter table "public"."sale" drop constraint "sale_packId_fkey";

alter table "public"."sale_root" drop constraint "sale_root_packId_fkey";

alter table "public"."service_pack" drop constraint "pack_service_packId_fkey";

alter table "public"."pkg" drop constraint "package_pkey";

drop index if exists "public"."package_pkey";

drop index if exists "public"."pkg_profile_profileid_pkgid_active_unique";

drop table "public"."pkg";

create table "public"."pack" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "name" character varying not null,
    "schoolId" uuid not null default gen_random_uuid(),
    "price" numeric not null,
    "active" boolean not null default true,
    "expires" smallint,
    "use" smallint,
    "installments" smallint,
    "policy" smallint not null default '24'::smallint
);

alter table "public"."pack" enable row level security;

alter table "public"."booking" drop column "pkgId";

alter table "public"."booking" add column "packId" uuid;

alter table "public"."pack_profile" drop column "pkgId";

alter table "public"."pack_profile" add column "packId" uuid not null default gen_random_uuid();

CREATE UNIQUE INDEX package_pkey ON public.pack USING btree (id);

CREATE UNIQUE INDEX pkg_profile_profileid_pkgid_active_unique ON public.pack_profile USING btree ("profileId", "packId", active);

alter table "public"."pack" add constraint "package_pkey" PRIMARY KEY using index "package_pkey";

alter table "public"."attendance" add constraint "attendance_scheduleId_fkey" FOREIGN KEY ("scheduleId") REFERENCES schedule(id) ON DELETE SET NULL not valid;

alter table "public"."attendance" validate constraint "attendance_scheduleId_fkey";

alter table "public"."pack" add constraint "pkg_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) ON DELETE CASCADE not valid;

alter table "public"."pack" validate constraint "pkg_schoolId_fkey";

alter table "public"."booking" add constraint "booking_packageId_fkey" FOREIGN KEY ("packId") REFERENCES pack(id) not valid;

alter table "public"."booking" validate constraint "booking_packageId_fkey";

alter table "public"."pack_profile" add constraint "pkg_profile_pkgId_fkey" FOREIGN KEY ("packId") REFERENCES pack(id) not valid;

alter table "public"."pack_profile" validate constraint "pkg_profile_pkgId_fkey";

alter table "public"."pack_profile" add constraint "pkg_profile_profileid_pkgid_active_unique" UNIQUE using index "pkg_profile_profileid_pkgid_active_unique";

alter table "public"."sale" add constraint "sale_packId_fkey" FOREIGN KEY ("packId") REFERENCES pack(id) ON DELETE SET NULL not valid;

alter table "public"."sale" validate constraint "sale_packId_fkey";

alter table "public"."sale_root" add constraint "sale_root_packId_fkey" FOREIGN KEY ("packId") REFERENCES pack(id) ON DELETE CASCADE not valid;

alter table "public"."service_pack" add constraint "pack_service_packId_fkey" FOREIGN KEY ("packId") REFERENCES pack(id) ON DELETE CASCADE not valid;

grant delete on table "public"."pack" to "anon";

grant insert on table "public"."pack" to "anon";

grant references on table "public"."pack" to "anon";

grant select on table "public"."pack" to "anon";

grant trigger on table "public"."pack" to "anon";

grant truncate on table "public"."pack" to "anon";

grant update on table "public"."pack" to "anon";

grant delete on table "public"."pack" to "authenticated";

grant insert on table "public"."pack" to "authenticated";

grant references on table "public"."pack" to "authenticated";

grant select on table "public"."pack" to "authenticated";

grant trigger on table "public"."pack" to "authenticated";

grant truncate on table "public"."pack" to "authenticated";

grant update on table "public"."pack" to "authenticated";

grant delete on table "public"."pack" to "service_role";

grant insert on table "public"."pack" to "service_role";

grant references on table "public"."pack" to "service_role";

grant select on table "public"."pack" to "service_role";

grant trigger on table "public"."pack" to "service_role";

grant truncate on table "public"."pack" to "service_role";

grant update on table "public"."pack" to "service_role";

