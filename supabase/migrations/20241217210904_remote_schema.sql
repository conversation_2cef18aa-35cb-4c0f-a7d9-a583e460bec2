alter table "public"."service_equipment" drop constraint "service_equipment_service_fk";

alter table "public"."schedule" drop constraint "schedule_serviceId_fkey";

create table "public"."service_instructor" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "serviceId" uuid not null,
    "profileId" uuid not null
);


alter table "public"."service_instructor" enable row level security;

CREATE UNIQUE INDEX service_instructor_pkey ON public.service_instructor USING btree (id);

alter table "public"."service_instructor" add constraint "service_instructor_pkey" PRIMARY KEY using index "service_instructor_pkey";

alter table "public"."service_equipment" add constraint "service_equipment_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) ON DELETE CASCADE not valid;

alter table "public"."service_equipment" validate constraint "service_equipment_serviceId_fkey";

alter table "public"."service_instructor" add constraint "service_instructor_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."service_instructor" validate constraint "service_instructor_profileId_fkey";

alter table "public"."service_instructor" add constraint "service_instructor_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) ON DELETE CASCADE not valid;

alter table "public"."service_instructor" validate constraint "service_instructor_serviceId_fkey";

alter table "public"."schedule" add constraint "schedule_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) ON DELETE CASCADE not valid;

alter table "public"."schedule" validate constraint "schedule_serviceId_fkey";

grant delete on table "public"."service_instructor" to "anon";

grant insert on table "public"."service_instructor" to "anon";

grant references on table "public"."service_instructor" to "anon";

grant select on table "public"."service_instructor" to "anon";

grant trigger on table "public"."service_instructor" to "anon";

grant truncate on table "public"."service_instructor" to "anon";

grant update on table "public"."service_instructor" to "anon";

grant delete on table "public"."service_instructor" to "authenticated";

grant insert on table "public"."service_instructor" to "authenticated";

grant references on table "public"."service_instructor" to "authenticated";

grant select on table "public"."service_instructor" to "authenticated";

grant trigger on table "public"."service_instructor" to "authenticated";

grant truncate on table "public"."service_instructor" to "authenticated";

grant update on table "public"."service_instructor" to "authenticated";

grant delete on table "public"."service_instructor" to "service_role";

grant insert on table "public"."service_instructor" to "service_role";

grant references on table "public"."service_instructor" to "service_role";

grant select on table "public"."service_instructor" to "service_role";

grant trigger on table "public"."service_instructor" to "service_role";

grant truncate on table "public"."service_instructor" to "service_role";

grant update on table "public"."service_instructor" to "service_role";


