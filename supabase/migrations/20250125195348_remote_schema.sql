create table "public"."rank_point" (
    "id" uuid not null default gen_random_uuid(),
    "userId" uuid not null,
    "createdAt" timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
    "sportId" uuid
);


alter table "public"."rank_point" enable row level security;

alter table "public"."rank_student" add column "sportId" uuid;

CREATE UNIQUE INDEX rank_point_pkey ON public.rank_point USING btree (id);

CREATE UNIQUE INDEX unique_school_rank ON public.rank_school USING btree ("schoolId", "rankId");

alter table "public"."rank_point" add constraint "rank_point_pkey" PRIMARY KEY using index "rank_point_pkey";

alter table "public"."attendance" add constraint "attendance_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) ON DELETE SET NULL not valid;

alter table "public"."attendance" validate constraint "attendance_serviceId_fkey";

alter table "public"."rank_point" add constraint "rank_point_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES sport(id) ON DELETE CASCADE not valid;

alter table "public"."rank_point" validate constraint "rank_point_sportId_fkey";

alter table "public"."rank_point" add constraint "rank_point_userId_fkey" FOREIGN KEY ("userId") REFERENCES profile(id) not valid;

alter table "public"."rank_point" validate constraint "rank_point_userId_fkey";

alter table "public"."rank_school" add constraint "unique_school_rank" UNIQUE using index "unique_school_rank";

alter table "public"."rank_student" add constraint "rank_student_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES sport(id) ON DELETE SET NULL not valid;

alter table "public"."rank_student" validate constraint "rank_student_sportId_fkey";

grant delete on table "public"."rank_point" to "anon";

grant insert on table "public"."rank_point" to "anon";

grant references on table "public"."rank_point" to "anon";

grant select on table "public"."rank_point" to "anon";

grant trigger on table "public"."rank_point" to "anon";

grant truncate on table "public"."rank_point" to "anon";

grant update on table "public"."rank_point" to "anon";

grant delete on table "public"."rank_point" to "authenticated";

grant insert on table "public"."rank_point" to "authenticated";

grant references on table "public"."rank_point" to "authenticated";

grant select on table "public"."rank_point" to "authenticated";

grant trigger on table "public"."rank_point" to "authenticated";

grant truncate on table "public"."rank_point" to "authenticated";

grant update on table "public"."rank_point" to "authenticated";

grant delete on table "public"."rank_point" to "service_role";

grant insert on table "public"."rank_point" to "service_role";

grant references on table "public"."rank_point" to "service_role";

grant select on table "public"."rank_point" to "service_role";

grant trigger on table "public"."rank_point" to "service_role";

grant truncate on table "public"."rank_point" to "service_role";

grant update on table "public"."rank_point" to "service_role";

