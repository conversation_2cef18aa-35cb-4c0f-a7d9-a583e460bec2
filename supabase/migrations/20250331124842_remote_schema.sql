CREATE OR REPLACE FUNCTION public.generate_next_month_teacher_schedule()
  RETURNS void
  LANGUAGE plpgsql
AS $function$
DECLARE
    next_month_start date;
    next_month_end date;
    service_record record;
    schedule_record record;
    teacher_record record;
    current_day date;
    day_of_week integer;
    i integer;
BEGIN
    -- Calculate the date range for next month
    next_month_start := date_trunc('month', CURRENT_DATE + INTERVAL '1 month');
    next_month_end := date_trunc('month', next_month_start + INTERVAL '1 month') - INTERVAL '1 day';

    RAISE NOTICE 'Generating schedule from % to %', next_month_start, next_month_end;

    -- Loop through all services with active schedules
    FOR service_record IN
        SELECT s.* FROM service s
        WHERE EXISTS (
            SELECT 1 FROM schedule sch
            WHERE sch."serviceId" = s.id AND sch.active = true
        )
    LOOP
        RAISE NOTICE 'Processing service: %', service_record.id;

        -- Loop through all active schedules for this service
        FOR schedule_record IN
            SELECT * FROM schedule
            WHERE "serviceId" = service_record.id AND active = true
        LOOP
            RAISE NOTICE 'Processing schedule: % (weekday: %)', schedule_record.id, schedule_record.number;

            -- Loop through all active teachers assigned to this service
            FOR teacher_record IN
                SELECT st."profileId"
                FROM service_teacher st
                JOIN profile p ON st."profileId" = p.id
                WHERE st."serviceId" = service_record.id
                  AND p.type = 'teacher'::user_type
                  AND p.status = 'active'
            LOOP
                RAISE NOTICE 'Processing teacher: %', teacher_record."profileId";

                -- Loop through all days in next month
                current_day := next_month_start;
                WHILE current_day <= next_month_end LOOP
                    -- Get day of week (0=Sunday to 6=Saturday)
                    day_of_week := EXTRACT(DOW FROM current_day)::integer;

                    -- Check if this day matches the schedule's weekday
                    IF day_of_week = schedule_record.number THEN
                        -- Create the needed slots (1 if no limit, or 'limit' number of slots)
                        FOR i IN 1..COALESCE(service_record."limit", 1) LOOP
                            INSERT INTO teacher_schedule (
                                "scheduleId",
                                "profileId",
                                day,
                                "serviceId",
                                "createdAt"
                            ) VALUES (
                                schedule_record.id,
                                teacher_record."profileId",
                                current_day,
                                service_record.id,
                                NOW()
                            );
                            RAISE NOTICE 'Created schedule for teacher % on %', teacher_record."profileId", current_day;
                        END LOOP;
                    END IF;

                    current_day := current_day + INTERVAL '1 day';
                END LOOP;
            END LOOP;
        END LOOP;
    END LOOP;

    RAISE NOTICE 'Schedule generation completed';
END;
$function$
;