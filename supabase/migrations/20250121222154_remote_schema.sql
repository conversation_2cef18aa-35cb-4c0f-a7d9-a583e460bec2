
revoke delete on table "public"."payment_pkg" from "anon";

revoke insert on table "public"."payment_pkg" from "anon";

revoke references on table "public"."payment_pkg" from "anon";

revoke select on table "public"."payment_pkg" from "anon";

revoke trigger on table "public"."payment_pkg" from "anon";

revoke truncate on table "public"."payment_pkg" from "anon";

revoke update on table "public"."payment_pkg" from "anon";

revoke delete on table "public"."payment_pkg" from "authenticated";

revoke insert on table "public"."payment_pkg" from "authenticated";

revoke references on table "public"."payment_pkg" from "authenticated";

revoke select on table "public"."payment_pkg" from "authenticated";

revoke trigger on table "public"."payment_pkg" from "authenticated";

revoke truncate on table "public"."payment_pkg" from "authenticated";

revoke update on table "public"."payment_pkg" from "authenticated";

revoke delete on table "public"."payment_pkg" from "service_role";

revoke insert on table "public"."payment_pkg" from "service_role";

revoke references on table "public"."payment_pkg" from "service_role";

revoke select on table "public"."payment_pkg" from "service_role";

revoke trigger on table "public"."payment_pkg" from "service_role";

revoke truncate on table "public"."payment_pkg" from "service_role";

revoke update on table "public"."payment_pkg" from "service_role";

revoke delete on table "public"."payment_plan" from "anon";

revoke insert on table "public"."payment_plan" from "anon";

revoke references on table "public"."payment_plan" from "anon";

revoke select on table "public"."payment_plan" from "anon";

revoke trigger on table "public"."payment_plan" from "anon";

revoke truncate on table "public"."payment_plan" from "anon";

revoke update on table "public"."payment_plan" from "anon";

revoke delete on table "public"."payment_plan" from "authenticated";

revoke insert on table "public"."payment_plan" from "authenticated";

revoke references on table "public"."payment_plan" from "authenticated";

revoke select on table "public"."payment_plan" from "authenticated";

revoke trigger on table "public"."payment_plan" from "authenticated";

revoke truncate on table "public"."payment_plan" from "authenticated";

revoke update on table "public"."payment_plan" from "authenticated";

revoke delete on table "public"."payment_plan" from "service_role";

revoke insert on table "public"."payment_plan" from "service_role";

revoke references on table "public"."payment_plan" from "service_role";

revoke select on table "public"."payment_plan" from "service_role";

revoke trigger on table "public"."payment_plan" from "service_role";

revoke truncate on table "public"."payment_plan" from "service_role";

revoke update on table "public"."payment_plan" from "service_role";

alter table "public"."payment_pkg" drop constraint "payment_pkg_pkg_fkey";

alter table "public"."payment_pkg" drop constraint "payment_pkg_profileid_fkey";

alter table "public"."payment_plan" drop constraint "payment_profileId_fkey";

alter table "public"."rank" drop constraint "rank_sportid_fkey";

alter table "public"."profile" drop constraint "profile_sportId_fkey";

alter table "public"."payment_pkg" drop constraint "payment_pkg_pkey";

alter table "public"."payment_plan" drop constraint "payment_pkey";

drop index if exists "public"."payment_pkey";

drop index if exists "public"."payment_pkg_pkey";

drop table "public"."payment_pkg";

drop table "public"."payment_plan";

create table "public"."plan_service" (
    "id" bigint generated by default as identity not null,
    "createdAt" timestamp with time zone not null default now(),
    "planId" uuid not null,
    "serviceId" uuid not null
);


alter table "public"."plan_service" enable row level security;

alter table "public"."rank" add column "stripe" smallint;

CREATE UNIQUE INDEX plan_service_pkey ON public.plan_service USING btree (id);

alter table "public"."plan_service" add constraint "plan_service_pkey" PRIMARY KEY using index "plan_service_pkey";

alter table "public"."plan_service" add constraint "plan_service_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) ON DELETE CASCADE not valid;

alter table "public"."plan_service" validate constraint "plan_service_planId_fkey";

alter table "public"."plan_service" add constraint "plan_service_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) ON DELETE CASCADE not valid;

alter table "public"."plan_service" validate constraint "plan_service_serviceId_fkey";

alter table "public"."rank" add constraint "rank_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES sport(id) ON DELETE CASCADE not valid;

alter table "public"."rank" validate constraint "rank_sportId_fkey";

alter table "public"."profile" add constraint "profile_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES sport(id) ON DELETE SET NULL not valid;

alter table "public"."profile" validate constraint "profile_sportId_fkey";

grant delete on table "public"."plan_service" to "anon";

grant insert on table "public"."plan_service" to "anon";

grant references on table "public"."plan_service" to "anon";

grant select on table "public"."plan_service" to "anon";

grant trigger on table "public"."plan_service" to "anon";

grant truncate on table "public"."plan_service" to "anon";

grant update on table "public"."plan_service" to "anon";

grant delete on table "public"."plan_service" to "authenticated";

grant insert on table "public"."plan_service" to "authenticated";

grant references on table "public"."plan_service" to "authenticated";

grant select on table "public"."plan_service" to "authenticated";

grant trigger on table "public"."plan_service" to "authenticated";

grant truncate on table "public"."plan_service" to "authenticated";

grant update on table "public"."plan_service" to "authenticated";

grant delete on table "public"."plan_service" to "service_role";

grant insert on table "public"."plan_service" to "service_role";

grant references on table "public"."plan_service" to "service_role";

grant select on table "public"."plan_service" to "service_role";

grant trigger on table "public"."plan_service" to "service_role";

grant truncate on table "public"."plan_service" to "service_role";

grant update on table "public"."plan_service" to "service_role";
