revoke delete on table "public"."service_sport" from "anon";

revoke insert on table "public"."service_sport" from "anon";

revoke references on table "public"."service_sport" from "anon";

revoke select on table "public"."service_sport" from "anon";

revoke trigger on table "public"."service_sport" from "anon";

revoke truncate on table "public"."service_sport" from "anon";

revoke update on table "public"."service_sport" from "anon";

revoke delete on table "public"."service_sport" from "authenticated";

revoke insert on table "public"."service_sport" from "authenticated";

revoke references on table "public"."service_sport" from "authenticated";

revoke select on table "public"."service_sport" from "authenticated";

revoke trigger on table "public"."service_sport" from "authenticated";

revoke truncate on table "public"."service_sport" from "authenticated";

revoke update on table "public"."service_sport" from "authenticated";

revoke delete on table "public"."service_sport" from "service_role";

revoke insert on table "public"."service_sport" from "service_role";

revoke references on table "public"."service_sport" from "service_role";

revoke select on table "public"."service_sport" from "service_role";

revoke trigger on table "public"."service_sport" from "service_role";

revoke truncate on table "public"."service_sport" from "service_role";

revoke update on table "public"."service_sport" from "service_role";

alter table "public"."service_sport" drop constraint "service_sport_serviceId_fkey";

alter table "public"."service_sport" drop constraint "service_sport_sportId_fkey";

alter table "public"."service_sport" drop constraint "service_sport_pkey";

drop index if exists "public"."service_sport_pkey";

drop table "public"."service_sport";

alter table "public"."service" add column "sportId" uuid;

alter table "public"."service" add constraint "service_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES sport(id) ON DELETE CASCADE not valid;

alter table "public"."service" validate constraint "service_sportId_fkey";
