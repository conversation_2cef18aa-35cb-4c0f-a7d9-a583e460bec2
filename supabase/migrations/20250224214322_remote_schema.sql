CREATE OR REPLACE FUNCTION public.get_user_info(userid uuid, sportid uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
DECLARE
    result jsonb;
BEGIN
    WITH recent_rank AS (
        SELECT 
            rs."profileId",
            rs."rankId",
            rs."createdAt" AS rank_created_at,  -- Explicitly select createdAt from rank_student
            r.name,
            r.color,
            r.stripe,
            r."nextRank",
            ROW_NUMBER() OVER (PARTITION BY rs."profileId" ORDER BY rs."createdAt" DESC) AS rn
        FROM 
            public.rank_student rs
        JOIN 
            public.rank r ON rs."rankId" = r.id
    )
    
    SELECT 
        jsonb_build_object(
            'id', p.id,
            'rank', jsonb_build_object(
                'id', rr."rankId",
                'name', rr.name,
                'color', rr.color,
                'stripe', rr.stripe,
                'nextRank', (
                    SELECT jsonb_build_object(
                        'id', nr.id,
                        'name', nr.name,
                        'color', nr.color,
                        'stripe', nr.stripe,
                        'classes', (
                            SELECT rs.classes
                            FROM public.rank_school rs
                            WHERE rs."rankId" = rr."nextRank"
                        )
                    )
                    FROM public.rank nr
                    WHERE nr.id = rr."nextRank"
                ),
                'points', (
                    SELECT COUNT(*)
                    FROM public.rank_point rp
                    WHERE rp."userId" = p.id 
                      AND rp."sportId" = sportId
                      AND rp."createdAt" > rr.rank_created_at  -- Compare with rank_student createdAt
                )
            ),
            'attendances', json_agg(DISTINCT jsonb_build_object(
                'date', a.date,
                'confirmed', a.confirmed,
                'serviceId', a."serviceId",
                'scheduleId', sch.id,
                'hour', sch.hour
            ))
        ) INTO result
    FROM 
        public.profile p
    LEFT JOIN 
        public.sport_profile sp ON p.id = sp."profileId"
    LEFT JOIN 
        public.sport s ON sp."sportId" = s.id
    LEFT JOIN 
        recent_rank rr ON p.id = rr."profileId" AND rr.rn = 1  -- Get the most recent rank
    LEFT JOIN 
        public.attendance a ON p.id = a."userId" AND a."serviceId" IN (
            SELECT service.id
            FROM public.service service
            WHERE service."sportId" = sp."sportId"
        )
    LEFT JOIN 
        public.schedule sch ON a."scheduleId" = sch.id
    WHERE 
        p.id = userId  -- Use the userId parameter
        AND sp."sportId" = sportId  -- Use the sportId parameter
    GROUP BY 
        p.id, p.name, rr."rankId", rr.name, rr.color, rr.stripe, rr."nextRank", rr.rank_created_at, sp."sportId";  -- Include rank_created_at in GROUP BY

    RETURN result;
END;
$function$
;