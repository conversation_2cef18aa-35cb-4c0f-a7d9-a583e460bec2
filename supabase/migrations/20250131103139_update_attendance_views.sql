drop view
  public.attendance_monthly;
create view
  public.attendance_monthly
  with (security_invoker=true)
  as
select
  a."userId",
  a."serviceId",
  u.name,
  extract(
    year
    from
      a.date
  ) as year,
  extract(
    month
    from
      a.date
  ) as month,
  count(*) as total
from
  attendance a
  join profile u on a."userId" = u.id
group by
  a."userId",
  a."serviceId",
  u.name,
  (
    extract(
      year
      from
        a.date
    )
  ),
  (
    extract(
      month
      from
        a.date
    )
  )
order by
  (count(*)) desc;


drop view
  public.attendance_yearly;
create view
  public.attendance_yearly
  with (security_invoker=true)
  as
select
  a."userId",
  a."serviceId",
  u.name,
  extract(
    year
    from
      a.date
  ) as year,
  count(*) as total
from
  attendance a
  join profile u on a."userId" = u.id
group by
  a."userId",
  a."serviceId",
  u.name,
  (
    extract(
      year
      from
        a.date
    )
  )
order by
  (count(*)) desc;
