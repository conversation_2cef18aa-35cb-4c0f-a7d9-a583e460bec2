create type "public"."payment_method" as enum ('credit_card', 'pix', 'boleto');

create table "public"."category" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "schoolId" uuid,
    "name" character varying not null
);


alter table "public"."category" enable row level security;

create table "public"."debt" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "name" character varying not null,
    "description" character varying,
    "type" character varying,
    "value" numeric not null,
    "repetitions" smallint,
    "billingDate" timestamp with time zone not null,
    "paid" boolean default false,
    "paidDate" timestamp with time zone,
    "isFixed" boolean default false,
    "schoolId" uuid,
    "method" payment_method
);


alter table "public"."debt" enable row level security;

create table "public"."debt_category" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "debtId" uuid,
    "categoryId" uuid
);


alter table "public"."debt_category" enable row level security;

CREATE UNIQUE INDEX debt_category_pkey ON public.category USING btree (id);

CREATE UNIQUE INDEX debt_category_pkey1 ON public.debt_category USING btree (id);

CREATE UNIQUE INDEX debt_pkey ON public.debt USING btree (id);

alter table "public"."category" add constraint "debt_category_pkey" PRIMARY KEY using index "debt_category_pkey";

alter table "public"."debt" add constraint "debt_pkey" PRIMARY KEY using index "debt_pkey";

alter table "public"."debt_category" add constraint "debt_category_pkey1" PRIMARY KEY using index "debt_category_pkey1";

alter table "public"."category" add constraint "debt_category_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) not valid;

alter table "public"."category" validate constraint "debt_category_schoolId_fkey";

alter table "public"."debt" add constraint "debt_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) not valid;

alter table "public"."debt" validate constraint "debt_schoolId_fkey";

alter table "public"."debt_category" add constraint "debt_category_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES category(id) not valid;

alter table "public"."debt_category" validate constraint "debt_category_categoryId_fkey";

alter table "public"."debt_category" add constraint "debt_category_debtId_fkey" FOREIGN KEY ("debtId") REFERENCES debt(id) not valid;

alter table "public"."debt_category" validate constraint "debt_category_debtId_fkey";

grant delete on table "public"."category" to "anon";

grant insert on table "public"."category" to "anon";

grant references on table "public"."category" to "anon";

grant select on table "public"."category" to "anon";

grant trigger on table "public"."category" to "anon";

grant truncate on table "public"."category" to "anon";

grant update on table "public"."category" to "anon";

grant delete on table "public"."category" to "authenticated";

grant insert on table "public"."category" to "authenticated";

grant references on table "public"."category" to "authenticated";

grant select on table "public"."category" to "authenticated";

grant trigger on table "public"."category" to "authenticated";

grant truncate on table "public"."category" to "authenticated";

grant update on table "public"."category" to "authenticated";

grant delete on table "public"."category" to "service_role";

grant insert on table "public"."category" to "service_role";

grant references on table "public"."category" to "service_role";

grant select on table "public"."category" to "service_role";

grant trigger on table "public"."category" to "service_role";

grant truncate on table "public"."category" to "service_role";

grant update on table "public"."category" to "service_role";

grant delete on table "public"."debt" to "anon";

grant insert on table "public"."debt" to "anon";

grant references on table "public"."debt" to "anon";

grant select on table "public"."debt" to "anon";

grant trigger on table "public"."debt" to "anon";

grant truncate on table "public"."debt" to "anon";

grant update on table "public"."debt" to "anon";

grant delete on table "public"."debt" to "authenticated";

grant insert on table "public"."debt" to "authenticated";

grant references on table "public"."debt" to "authenticated";

grant select on table "public"."debt" to "authenticated";

grant trigger on table "public"."debt" to "authenticated";

grant truncate on table "public"."debt" to "authenticated";

grant update on table "public"."debt" to "authenticated";

grant delete on table "public"."debt" to "service_role";

grant insert on table "public"."debt" to "service_role";

grant references on table "public"."debt" to "service_role";

grant select on table "public"."debt" to "service_role";

grant trigger on table "public"."debt" to "service_role";

grant truncate on table "public"."debt" to "service_role";

grant update on table "public"."debt" to "service_role";

grant delete on table "public"."debt_category" to "anon";

grant insert on table "public"."debt_category" to "anon";

grant references on table "public"."debt_category" to "anon";

grant select on table "public"."debt_category" to "anon";

grant trigger on table "public"."debt_category" to "anon";

grant truncate on table "public"."debt_category" to "anon";

grant update on table "public"."debt_category" to "anon";

grant delete on table "public"."debt_category" to "authenticated";

grant insert on table "public"."debt_category" to "authenticated";

grant references on table "public"."debt_category" to "authenticated";

grant select on table "public"."debt_category" to "authenticated";

grant trigger on table "public"."debt_category" to "authenticated";

grant truncate on table "public"."debt_category" to "authenticated";

grant update on table "public"."debt_category" to "authenticated";

grant delete on table "public"."debt_category" to "service_role";

grant insert on table "public"."debt_category" to "service_role";

grant references on table "public"."debt_category" to "service_role";

grant select on table "public"."debt_category" to "service_role";

grant trigger on table "public"."debt_category" to "service_role";

grant truncate on table "public"."debt_category" to "service_role";

grant update on table "public"."debt_category" to "service_role";


