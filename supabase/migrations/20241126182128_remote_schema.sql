alter table "public"."booking" alter column "planId" set data type character varying using "planId"::character varying;

alter table "public"."payment" add column "planId" character varying;

alter table "public"."plan_profile" alter column "planId" drop default;

alter table "public"."plan_profile" alter column "planId" set data type character varying using "planId"::character varying;

alter table "public"."booking" add constraint "booking_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) not valid;

alter table "public"."booking" validate constraint "booking_planId_fkey";

alter table "public"."payment" add constraint "payment_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) not valid;

alter table "public"."payment" validate constraint "payment_planId_fkey";

alter table "public"."plan_profile" add constraint "plan_profile_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) not valid;

alter table "public"."plan_profile" validate constraint "plan_profile_planId_fkey";

alter table "public"."subscription" add constraint "subscription_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) not valid;

alter table "public"."subscription" validate constraint "subscription_planId_fkey";


