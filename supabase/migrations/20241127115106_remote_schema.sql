alter table "public"."plan_profile" drop constraint "plan_profile_pkey";

drop index if exists "public"."plan_profile_pkey";

alter table "public"."plan_profile" drop column "new_id";

alter table "public"."plan_profile" add column "id" uuid not null default gen_random_uuid();

CREATE UNIQUE INDEX plan_profile_pkey ON public.plan_profile USING btree (id);

alter table "public"."plan_profile" add constraint "plan_profile_pkey" PRIMARY KEY using index "plan_profile_pkey";


