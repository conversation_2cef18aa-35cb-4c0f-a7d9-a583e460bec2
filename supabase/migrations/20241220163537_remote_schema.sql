create extension if not exists "hypopg" with schema "extensions";

create extension if not exists "index_advisor" with schema "extensions";


drop trigger if exists "new_subscription" on "public"."subscription";

alter table "public"."booking" add column "teacherId" uuid;

alter table "public"."payment_pkg_notification" add column "profileId" uuid;

CREATE INDEX booking_id_idx ON public.booking USING btree (id);

CREATE INDEX profile_id_idx ON public.profile USING btree (id);

CREATE INDEX subscription_id_idx ON public.subscription USING btree (id);

CREATE INDEX "subscription_userId_idx" ON public.subscription USING btree ("userId");

alter table "public"."booking" add constraint "booking_teacherId_fkey" FOREIGN KEY ("teacherId") REFERENCES profile(id) not valid;

alter table "public"."booking" validate constraint "booking_teacherId_fkey";

alter table "public"."payment_pkg_notification" add constraint "payment_pkg_notification_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."payment_pkg_notification" validate constraint "payment_pkg_notification_profileId_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.new_payment_pkg()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_pkg?paymentId=%s&domain=%s',
            NEW.id,
            NEW.domain
        ),
        ARRAY[http_header('Authorization', ''),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_pkg_notification()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_pkg_notification?paymentId=%s&domain=%s',
            NEW.id,
            NEW.domain
        ),
        ARRAY[http_header('Authorization', ''),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_plan()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_plan?paymentId=%s&domain=%s',
            NEW.id,
            NEW.domain
        ),
        ARRAY[http_header('Authorization', ''),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_plan_notification()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_plan_notification?paymentId=%s&profileId=%s&domain=%s',
            NEW.id,
            NEW."profileId",
            NEW.domain
        ),
        ARRAY[http_header('Authorization', ''),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_subscription()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response json;
BEGIN
    -- Make the HTTP request to the specified URL with the Bearer token
    SELECT * INTO response
    FROM http_post(
        'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_subscription',
        json_build_object('data', NEW.*)::text,
        'Authorization='
    );

    RETURN NEW;
END;$function$
;

CREATE TRIGGER new_subscription AFTER INSERT OR UPDATE ON public.subscription FOR EACH ROW EXECUTE FUNCTION new_subscription();


