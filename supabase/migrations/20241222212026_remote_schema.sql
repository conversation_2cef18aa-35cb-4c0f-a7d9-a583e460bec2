drop index if exists "public"."unique_pkg_profile_active_idx";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.new_payment_pkg()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_pkg?paymentId=%s&domain=%s',
            NEW.id,
            NEW.domain
        ),
        ARRAY[http_header('Authorization', 'Bearer'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_pkg_notification()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_pkg_notification?paymentId=%s&domain=%s',
            NEW.id,
            NEW.domain
        ),
        ARRAY[http_header('Authorization', 'Bearer'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_plan()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_plan?paymentId=%s&domain=%s',
            NEW.id,
            NEW.domain
        ),
        ARRAY[http_header('Authorization', 'Bearer'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_plan_notification()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_plan_notification?paymentId=%s&profileId=%s&domain=%s',
            NEW.id,
            NEW."profileId",
            NEW.domain
        ),
        ARRAY[http_header('Authorization', 'Bearer'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_subscription()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response json;
BEGIN
    -- Make the HTTP request to the specified URL with the Bearer token
    SELECT * INTO response
    FROM http_post(
        'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_subscription',
        json_build_object('data', NEW.*)::text,
        'Authorization=Bearer'
    );

    RETURN NEW;
END;$function$
;


