revoke delete on table "public"."pkg_profile" from "anon";

revoke insert on table "public"."pkg_profile" from "anon";

revoke references on table "public"."pkg_profile" from "anon";

revoke select on table "public"."pkg_profile" from "anon";

revoke trigger on table "public"."pkg_profile" from "anon";

revoke truncate on table "public"."pkg_profile" from "anon";

revoke update on table "public"."pkg_profile" from "anon";

revoke delete on table "public"."pkg_profile" from "authenticated";

revoke insert on table "public"."pkg_profile" from "authenticated";

revoke references on table "public"."pkg_profile" from "authenticated";

revoke select on table "public"."pkg_profile" from "authenticated";

revoke trigger on table "public"."pkg_profile" from "authenticated";

revoke truncate on table "public"."pkg_profile" from "authenticated";

revoke update on table "public"."pkg_profile" from "authenticated";

revoke delete on table "public"."pkg_profile" from "service_role";

revoke insert on table "public"."pkg_profile" from "service_role";

revoke references on table "public"."pkg_profile" from "service_role";

revoke select on table "public"."pkg_profile" from "service_role";

revoke trigger on table "public"."pkg_profile" from "service_role";

revoke truncate on table "public"."pkg_profile" from "service_role";

revoke update on table "public"."pkg_profile" from "service_role";

alter table "public"."pkg_profile" drop constraint "pkg_profile_pkgId_fkey";

alter table "public"."pkg_profile" drop constraint "pkg_profile_profileId_fkey";

alter table "public"."pkg_profile" drop constraint "pkg_profile_profileid_pkgid_active_unique";

alter table "public"."pkg_profile" drop constraint "pkg_profile_pkey";

drop index if exists "public"."pkg_profile_pkey";

drop index if exists "public"."pkg_profile_profileid_pkgid_active_unique";

drop table "public"."pkg_profile";

create table "public"."pack_profile" (
    "id" bigint generated by default as identity not null,
    "createdAt" timestamp with time zone not null default now(),
    "pkgId" uuid not null default gen_random_uuid(),
    "profileId" uuid not null default gen_random_uuid(),
    "active" boolean not null default true,
    "status" payment_status not null default 'pending'::payment_status
);


alter table "public"."pack_profile" enable row level security;

CREATE UNIQUE INDEX pkg_profile_pkey ON public.pack_profile USING btree (id);

CREATE UNIQUE INDEX pkg_profile_profileid_pkgid_active_unique ON public.pack_profile USING btree ("profileId", "pkgId", active);

alter table "public"."pack_profile" add constraint "pkg_profile_pkey" PRIMARY KEY using index "pkg_profile_pkey";

alter table "public"."pack_profile" add constraint "pkg_profile_pkgId_fkey" FOREIGN KEY ("pkgId") REFERENCES pkg(id) not valid;

alter table "public"."pack_profile" validate constraint "pkg_profile_pkgId_fkey";

alter table "public"."pack_profile" add constraint "pkg_profile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."pack_profile" validate constraint "pkg_profile_profileId_fkey";

alter table "public"."pack_profile" add constraint "pkg_profile_profileid_pkgid_active_unique" UNIQUE using index "pkg_profile_profileid_pkgid_active_unique";

grant delete on table "public"."pack_profile" to "anon";

grant insert on table "public"."pack_profile" to "anon";

grant references on table "public"."pack_profile" to "anon";

grant select on table "public"."pack_profile" to "anon";

grant trigger on table "public"."pack_profile" to "anon";

grant truncate on table "public"."pack_profile" to "anon";

grant update on table "public"."pack_profile" to "anon";

grant delete on table "public"."pack_profile" to "authenticated";

grant insert on table "public"."pack_profile" to "authenticated";

grant references on table "public"."pack_profile" to "authenticated";

grant select on table "public"."pack_profile" to "authenticated";

grant trigger on table "public"."pack_profile" to "authenticated";

grant truncate on table "public"."pack_profile" to "authenticated";

grant update on table "public"."pack_profile" to "authenticated";

grant delete on table "public"."pack_profile" to "service_role";

grant insert on table "public"."pack_profile" to "service_role";

grant references on table "public"."pack_profile" to "service_role";

grant select on table "public"."pack_profile" to "service_role";

grant trigger on table "public"."pack_profile" to "service_role";

grant truncate on table "public"."pack_profile" to "service_role";

grant update on table "public"."pack_profile" to "service_role";

