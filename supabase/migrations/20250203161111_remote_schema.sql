alter table "public"."pack_profile" drop column "status";

drop type "public"."payment_method__old_version_to_be_dropped";

drop type "public"."payment_status__old_version_to_be_dropped";

alter table "public"."booking" add constraint "booking_packProfileId_fkey" FOREIGN KEY ("packProfileId") REFERENCES pack_profile(id) ON DELETE SET NULL not valid;

alter table "public"."booking" validate constraint "booking_packProfileId_fkey";

alter table "public"."pack_profile" add constraint "pack_profile_packId_fkey" FOREIGN KEY ("packId") REFERENCES pack(id) not valid;

alter table "public"."pack_profile" validate constraint "pack_profile_packId_fkey";
