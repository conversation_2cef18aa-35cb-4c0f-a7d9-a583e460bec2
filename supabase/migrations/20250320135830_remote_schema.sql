CREATE OR REPLACE FUNCTION public.get_all_bookings(school uuid)
 RETURNS TABLE(id uuid, code text, day date, status booking_status, equipment jsonb, service jsonb, schedule jsonb, lead jsonb, profile jsonb)
 LANGUAGE plpgsql
AS $function$BEGIN
    RETURN QUERY
    SELECT 
        b.id,
        b.code,
        b.day,
        b.status,
        COALESCE((
            SELECT jsonb_agg(jsonb_build_object('name', e.name))
            FROM public.booking_equipment be
            JOIN public.equipment e ON be."equipmentId" = e.id
            WHERE be."bookingId" = b.id
        ), '[]'::jsonb) AS equipment,
        jsonb_build_object(
            'title', s.title
        ) AS service,
        jsonb_build_object(
            'hour', sch.hour
        ) AS schedule,
        jsonb_build_object(
            'name', l.name
        ) AS lead,
        jsonb_build_object(
            'name', p.name,
            'id', p.id
        ) as profile
    FROM 
        public.booking b
    LEFT JOIN
        public.service s ON b."serviceId" = s.id
    LEFT JOIN
        public.lead l ON b."leadId" = l.id
    LEFT JOIN
        public.profile p ON b."userId" = p.id
    LEFT JOIN
        public.schedule sch ON b."scheduleId" = sch.id
    WHERE s."schoolId" = "school"
    ORDER BY 
        CASE WHEN b.day < CURRENT_DATE THEN 1 ELSE 0 END,  -- Future bookings first, then past
        CASE 
            WHEN b.day < CURRENT_DATE THEN b.day END DESC,  -- Past bookings in descending order
        CASE 
            WHEN b.day >= CURRENT_DATE THEN b.day END ASC,  -- Future bookings in ascending order
        sch.hour,  -- Then order by schedule.hour
        p.name;  -- Finally, order by profile name
END;$function$
;