alter table "public"."debt_root" drop constraint "debt_root_pkey";

drop index if exists "public"."debt_root_pkey";

alter table "public"."debt_root" drop column "id";

alter table "public"."debt_root" alter column "newId" set not null;

CREATE UNIQUE INDEX debt_root_pkey ON public.debt_root USING btree ("newId");

alter table "public"."debt_root" add constraint "debt_root_pkey" PRIMARY KEY using index "debt_root_pkey";

