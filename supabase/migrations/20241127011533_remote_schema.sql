alter table "public"."payment_notification" drop constraint "payment_notification_pkey";

drop index if exists "public"."payment_notification_pkey";

alter table "public"."payment_notification" alter column "action" set not null;

CREATE UNIQUE INDEX payment_notification_pkey ON public.payment_notification USING btree (id, action);

alter table "public"."payment_notification" add constraint "payment_notification_pkey" PRIMARY KEY using index "payment_notification_pkey";


