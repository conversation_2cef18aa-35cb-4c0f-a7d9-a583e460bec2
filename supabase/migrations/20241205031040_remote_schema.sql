drop trigger if exists "new_subscription" on "public"."subscription";

alter table "public"."subscription" drop column "pendingChargedQty";

alter table "public"."subscription" add column "pendingChargeQty" smallint;

CREATE TRIGGER new_subscription AFTER INSERT OR UPDATE ON public.subscription FOR EACH ROW EXECUTE FUNCTION supabase_functions.http_request('https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_subscription', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg"}', '{}', '5000');
ALTER TABLE "public"."subscription" DISABLE TRIGGER "new_subscription";


