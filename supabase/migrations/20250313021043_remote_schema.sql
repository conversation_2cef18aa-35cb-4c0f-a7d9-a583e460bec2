CREATE OR REPLACE FUNCTION public.verify_pack_expiration()
  R<PERSON><PERSON>NS void
  LANGUAGE plpgsql
AS $function$DECLARE
  expired_profile RECORD;
BEGIN
  FOR expired_profile IN
    SELECT *
    FROM public.pack_profile pp
    WHERE pp."expireDate" < NOW()::DATE -- Compare expiredDate with current date
  LOOP
    UPDATE public.pack_profile
    SET active = false
    WHERE id = expired_profile.id;
  END LOOP;
END;$function$
;