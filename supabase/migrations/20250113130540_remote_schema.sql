drop trigger if exists "payment_pkg_notification_trigger" on "public"."payment_pkg_notification";

drop trigger if exists "payment_plan_notification" on "public"."payment_plan_notification";

revoke delete on table "public"."mp_application" from "anon";

revoke insert on table "public"."mp_application" from "anon";

revoke references on table "public"."mp_application" from "anon";

revoke select on table "public"."mp_application" from "anon";

revoke trigger on table "public"."mp_application" from "anon";

revoke truncate on table "public"."mp_application" from "anon";

revoke update on table "public"."mp_application" from "anon";

revoke delete on table "public"."mp_application" from "authenticated";

revoke insert on table "public"."mp_application" from "authenticated";

revoke references on table "public"."mp_application" from "authenticated";

revoke select on table "public"."mp_application" from "authenticated";

revoke trigger on table "public"."mp_application" from "authenticated";

revoke truncate on table "public"."mp_application" from "authenticated";

revoke update on table "public"."mp_application" from "authenticated";

revoke delete on table "public"."mp_application" from "service_role";

revoke insert on table "public"."mp_application" from "service_role";

revoke references on table "public"."mp_application" from "service_role";

revoke select on table "public"."mp_application" from "service_role";

revoke trigger on table "public"."mp_application" from "service_role";

revoke truncate on table "public"."mp_application" from "service_role";

revoke update on table "public"."mp_application" from "service_role";

revoke delete on table "public"."payment_pkg_notification" from "anon";

revoke insert on table "public"."payment_pkg_notification" from "anon";

revoke references on table "public"."payment_pkg_notification" from "anon";

revoke select on table "public"."payment_pkg_notification" from "anon";

revoke trigger on table "public"."payment_pkg_notification" from "anon";

revoke truncate on table "public"."payment_pkg_notification" from "anon";

revoke update on table "public"."payment_pkg_notification" from "anon";

revoke delete on table "public"."payment_pkg_notification" from "authenticated";

revoke insert on table "public"."payment_pkg_notification" from "authenticated";

revoke references on table "public"."payment_pkg_notification" from "authenticated";

revoke select on table "public"."payment_pkg_notification" from "authenticated";

revoke trigger on table "public"."payment_pkg_notification" from "authenticated";

revoke truncate on table "public"."payment_pkg_notification" from "authenticated";

revoke update on table "public"."payment_pkg_notification" from "authenticated";

revoke delete on table "public"."payment_pkg_notification" from "service_role";

revoke insert on table "public"."payment_pkg_notification" from "service_role";

revoke references on table "public"."payment_pkg_notification" from "service_role";

revoke select on table "public"."payment_pkg_notification" from "service_role";

revoke trigger on table "public"."payment_pkg_notification" from "service_role";

revoke truncate on table "public"."payment_pkg_notification" from "service_role";

revoke update on table "public"."payment_pkg_notification" from "service_role";

revoke delete on table "public"."payment_plan_notification" from "anon";

revoke insert on table "public"."payment_plan_notification" from "anon";

revoke references on table "public"."payment_plan_notification" from "anon";

revoke select on table "public"."payment_plan_notification" from "anon";

revoke trigger on table "public"."payment_plan_notification" from "anon";

revoke truncate on table "public"."payment_plan_notification" from "anon";

revoke update on table "public"."payment_plan_notification" from "anon";

revoke delete on table "public"."payment_plan_notification" from "authenticated";

revoke insert on table "public"."payment_plan_notification" from "authenticated";

revoke references on table "public"."payment_plan_notification" from "authenticated";

revoke select on table "public"."payment_plan_notification" from "authenticated";

revoke trigger on table "public"."payment_plan_notification" from "authenticated";

revoke truncate on table "public"."payment_plan_notification" from "authenticated";

revoke update on table "public"."payment_plan_notification" from "authenticated";

revoke delete on table "public"."payment_plan_notification" from "service_role";

revoke insert on table "public"."payment_plan_notification" from "service_role";

revoke references on table "public"."payment_plan_notification" from "service_role";

revoke select on table "public"."payment_plan_notification" from "service_role";

revoke trigger on table "public"."payment_plan_notification" from "service_role";

revoke truncate on table "public"."payment_plan_notification" from "service_role";

revoke update on table "public"."payment_plan_notification" from "service_role";

revoke delete on table "public"."subscription" from "anon";

revoke insert on table "public"."subscription" from "anon";

revoke references on table "public"."subscription" from "anon";

revoke select on table "public"."subscription" from "anon";

revoke trigger on table "public"."subscription" from "anon";

revoke truncate on table "public"."subscription" from "anon";

revoke update on table "public"."subscription" from "anon";

revoke delete on table "public"."subscription" from "authenticated";

revoke insert on table "public"."subscription" from "authenticated";

revoke references on table "public"."subscription" from "authenticated";

revoke select on table "public"."subscription" from "authenticated";

revoke trigger on table "public"."subscription" from "authenticated";

revoke truncate on table "public"."subscription" from "authenticated";

revoke update on table "public"."subscription" from "authenticated";

revoke delete on table "public"."subscription" from "service_role";

revoke insert on table "public"."subscription" from "service_role";

revoke references on table "public"."subscription" from "service_role";

revoke select on table "public"."subscription" from "service_role";

revoke trigger on table "public"."subscription" from "service_role";

revoke truncate on table "public"."subscription" from "service_role";

revoke update on table "public"."subscription" from "service_role";

revoke delete on table "public"."transaction_detail" from "anon";

revoke insert on table "public"."transaction_detail" from "anon";

revoke references on table "public"."transaction_detail" from "anon";

revoke select on table "public"."transaction_detail" from "anon";

revoke trigger on table "public"."transaction_detail" from "anon";

revoke truncate on table "public"."transaction_detail" from "anon";

revoke update on table "public"."transaction_detail" from "anon";

revoke delete on table "public"."transaction_detail" from "authenticated";

revoke insert on table "public"."transaction_detail" from "authenticated";

revoke references on table "public"."transaction_detail" from "authenticated";

revoke select on table "public"."transaction_detail" from "authenticated";

revoke trigger on table "public"."transaction_detail" from "authenticated";

revoke truncate on table "public"."transaction_detail" from "authenticated";

revoke update on table "public"."transaction_detail" from "authenticated";

revoke delete on table "public"."transaction_detail" from "service_role";

revoke insert on table "public"."transaction_detail" from "service_role";

revoke references on table "public"."transaction_detail" from "service_role";

revoke select on table "public"."transaction_detail" from "service_role";

revoke trigger on table "public"."transaction_detail" from "service_role";

revoke truncate on table "public"."transaction_detail" from "service_role";

revoke update on table "public"."transaction_detail" from "service_role";

revoke delete on table "public"."user_roles" from "anon";

revoke insert on table "public"."user_roles" from "anon";

revoke references on table "public"."user_roles" from "anon";

revoke select on table "public"."user_roles" from "anon";

revoke trigger on table "public"."user_roles" from "anon";

revoke truncate on table "public"."user_roles" from "anon";

revoke update on table "public"."user_roles" from "anon";

revoke delete on table "public"."user_roles" from "authenticated";

revoke insert on table "public"."user_roles" from "authenticated";

revoke references on table "public"."user_roles" from "authenticated";

revoke select on table "public"."user_roles" from "authenticated";

revoke trigger on table "public"."user_roles" from "authenticated";

revoke truncate on table "public"."user_roles" from "authenticated";

revoke update on table "public"."user_roles" from "authenticated";

revoke delete on table "public"."user_roles" from "service_role";

revoke insert on table "public"."user_roles" from "service_role";

revoke references on table "public"."user_roles" from "service_role";

revoke select on table "public"."user_roles" from "service_role";

revoke trigger on table "public"."user_roles" from "service_role";

revoke truncate on table "public"."user_roles" from "service_role";

revoke update on table "public"."user_roles" from "service_role";

revoke delete on table "public"."user_roles" from "supabase_auth_admin";

revoke insert on table "public"."user_roles" from "supabase_auth_admin";

revoke references on table "public"."user_roles" from "supabase_auth_admin";

revoke select on table "public"."user_roles" from "supabase_auth_admin";

revoke trigger on table "public"."user_roles" from "supabase_auth_admin";

revoke truncate on table "public"."user_roles" from "supabase_auth_admin";

revoke update on table "public"."user_roles" from "supabase_auth_admin";

alter table "public"."mp_application" drop constraint "mp_application_name_key";

alter table "public"."payment_pkg_notification" drop constraint "payment_pkg_notification_profileId_fkey";

alter table "public"."payment_plan" drop constraint "payment_plan_subscriptionId_fkey";

alter table "public"."payment_plan_notification" drop constraint "payment_plan_notification_profileId_fkey";

alter table "public"."subscription" drop constraint "subscription_applicationId_fkey";

alter table "public"."subscription" drop constraint "subscription_useId_fk";

alter table "public"."user_roles" drop constraint "user_roles_user_id_fkey";

alter table "public"."user_roles" drop constraint "user_roles_user_id_role_key";

alter table "public"."mp_application" drop constraint "mp_application_pkey";

alter table "public"."payment_pkg_notification" drop constraint "payment_pkg_notification_pkey";

alter table "public"."payment_plan_notification" drop constraint "payment_notification_pkey";

alter table "public"."subscription" drop constraint "subscription_pkey";

alter table "public"."transaction_detail" drop constraint "transaction_detail_pkey";

alter table "public"."user_roles" drop constraint "user_roles_pkey";

drop index if exists "public"."mp_application_name_key";

drop index if exists "public"."mp_application_pkey";

drop index if exists "public"."payment_notification_pkey";

drop index if exists "public"."payment_pkg_notification_pkey";

drop index if exists "public"."subscription_id_idx";

drop index if exists "public"."subscription_pkey";

drop index if exists "public"."subscription_userId_idx";

drop index if exists "public"."transaction_detail_pkey";

drop index if exists "public"."user_roles_pkey";

drop index if exists "public"."user_roles_user_id_role_key";

drop table "public"."mp_application";

drop table "public"."payment_pkg_notification";

drop table "public"."payment_plan_notification";

drop table "public"."subscription";

drop table "public"."transaction_detail";

drop table "public"."user_roles";

alter table "public"."school" add column "cnpj" character varying;
