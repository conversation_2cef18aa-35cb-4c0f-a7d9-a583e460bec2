alter table "public"."payment" drop constraint "payment_transactionDetailId_fkey";

alter table "public"."payment_plan" drop constraint "payment_plan_paymentId_fkey";

alter table "public"."payment_profile" drop constraint "payment_profile_paymentId_fkey";

alter table "public"."payment" drop column "additionalInfo";

alter table "public"."payment" drop column "authorizationCode";

alter table "public"."payment" drop column "buildVersion";

alter table "public"."payment" drop column "corporationId";

alter table "public"."payment" drop column "counterCurrency";

alter table "public"."payment" drop column "couponAmount";

alter table "public"."payment" drop column "couponCode";

alter table "public"."payment" drop column "currencyId";

alter table "public"."payment" drop column "dateOfExpiration";

alter table "public"."payment" drop column "description";

alter table "public"."payment" drop column "installments";

alter table "public"."payment" drop column "integratorId";

alter table "public"."payment" drop column "issuerId";

alter table "public"."payment" drop column "liveMode";

alter table "public"."payment" drop column "merchantAccountId";

alter table "public"."payment" drop column "merchantNumber";

alter table "public"."payment" drop column "method";

alter table "public"."payment" drop column "methodId";

alter table "public"."payment" drop column "methodOptionId";

alter table "public"."payment" drop column "moneyReleaseDate";

alter table "public"."payment" drop column "moneyReleaseSchema";

alter table "public"."payment" drop column "moneyReleaseStatus";

alter table "public"."payment" drop column "netAmount";

alter table "public"."payment" drop column "operationType";

alter table "public"."payment" drop column "paymentId";

alter table "public"."payment" drop column "platformId";

alter table "public"."payment" drop column "posId";

alter table "public"."payment" drop column "shippingAmount";

alter table "public"."payment" drop column "sponsorId";

alter table "public"."payment" drop column "storeId";

alter table "public"."payment" drop column "taxesAmount";

alter table "public"."payment" drop column "transactionAmountRefunded";

alter table "public"."payment" drop column "transactionDetailId";

alter table "public"."payment" alter column "dateLastUpdated" set data type timestamp without time zone using "dateLastUpdated"::timestamp without time zone;

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.new_payment()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment?paymentId=%s',
            NEW.id
        ),
        ARRAY[http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_notification()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_notification?paymentId=%s',
            NEW.id
        ),
        ARRAY[http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE TRIGGER payment AFTER INSERT OR UPDATE ON public.payment FOR EACH ROW EXECUTE FUNCTION new_payment();
ALTER TABLE "public"."payment" DISABLE TRIGGER "payment";


