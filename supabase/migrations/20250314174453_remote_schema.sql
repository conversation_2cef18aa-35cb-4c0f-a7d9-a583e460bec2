CREATE OR REPLACE FUNCTION public.mark_expired_bookings()
  RET<PERSON>NS void
  LANGUAGE plpgsql
AS $function$DECLARE
  expired_booking RECORD;
BEGIN
  FOR expired_booking IN
    SELECT b.id, b.status
    FROM public.booking b
    JOIN public.schedule s ON b."scheduleId" = s.id
    WHERE (b.day::TIMESTAMP + s.hour::INTERVAL) < (NOW() AT TIME ZONE 'America/Fortaleza')
      AND b.status IN ('pending', 'approved')
  LOOP
    UPDATE public.booking
    SET status = 'expired'
    WHERE id = expired_booking.id;
  END LOOP;
END;$function$
;