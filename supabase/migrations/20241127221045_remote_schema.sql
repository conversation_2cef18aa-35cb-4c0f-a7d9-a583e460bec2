alter table "public"."payment_plan" drop constraint "payment_plan_pkey";

drop index if exists "public"."payment_plan_pkey";

alter table "public"."payment_plan" add column "new_id" uuid not null default gen_random_uuid();

CREATE UNIQUE INDEX payment_plan_pkey ON public.payment_plan USING btree (id, new_id);

alter table "public"."payment_plan" add constraint "payment_plan_pkey" PRIMARY KEY using index "payment_plan_pkey";


