revoke delete on table "public"."service_instructor" from "anon";

revoke insert on table "public"."service_instructor" from "anon";

revoke references on table "public"."service_instructor" from "anon";

revoke select on table "public"."service_instructor" from "anon";

revoke trigger on table "public"."service_instructor" from "anon";

revoke truncate on table "public"."service_instructor" from "anon";

revoke update on table "public"."service_instructor" from "anon";

revoke delete on table "public"."service_instructor" from "authenticated";

revoke insert on table "public"."service_instructor" from "authenticated";

revoke references on table "public"."service_instructor" from "authenticated";

revoke select on table "public"."service_instructor" from "authenticated";

revoke trigger on table "public"."service_instructor" from "authenticated";

revoke truncate on table "public"."service_instructor" from "authenticated";

revoke update on table "public"."service_instructor" from "authenticated";

revoke delete on table "public"."service_instructor" from "service_role";

revoke insert on table "public"."service_instructor" from "service_role";

revoke references on table "public"."service_instructor" from "service_role";

revoke select on table "public"."service_instructor" from "service_role";

revoke trigger on table "public"."service_instructor" from "service_role";

revoke truncate on table "public"."service_instructor" from "service_role";

revoke update on table "public"."service_instructor" from "service_role";

alter table "public"."service_instructor" drop constraint "service_instructor_profileId_fkey";

alter table "public"."service_instructor" drop constraint "service_instructor_serviceId_fkey";

alter table "public"."service_instructor" drop constraint "service_instructor_pkey";

drop index if exists "public"."service_instructor_pkey";

drop table "public"."service_instructor";

alter table "public"."profile" alter column "type" drop default;

alter type "public"."user_type" rename to "user_type__old_version_to_be_dropped";

create type "public"."user_type" as enum ('admin', 'student', 'teacher');

create table "public"."service_teacher" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "serviceId" uuid not null,
    "profileId" uuid not null
);


alter table "public"."service_teacher" enable row level security;

alter table "public"."profile" alter column type type "public"."user_type" using type::text::"public"."user_type";

alter table "public"."profile" alter column "type" set default 'student'::user_type;

drop type "public"."user_type__old_version_to_be_dropped";

CREATE UNIQUE INDEX service_instructor_pkey ON public.service_teacher USING btree (id);

alter table "public"."service_teacher" add constraint "service_instructor_pkey" PRIMARY KEY using index "service_instructor_pkey";

alter table "public"."service_teacher" add constraint "service_instructor_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."service_teacher" validate constraint "service_instructor_profileId_fkey";

alter table "public"."service_teacher" add constraint "service_instructor_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) ON DELETE CASCADE not valid;

alter table "public"."service_teacher" validate constraint "service_instructor_serviceId_fkey";

grant delete on table "public"."service_teacher" to "anon";

grant insert on table "public"."service_teacher" to "anon";

grant references on table "public"."service_teacher" to "anon";

grant select on table "public"."service_teacher" to "anon";

grant trigger on table "public"."service_teacher" to "anon";

grant truncate on table "public"."service_teacher" to "anon";

grant update on table "public"."service_teacher" to "anon";

grant delete on table "public"."service_teacher" to "authenticated";

grant insert on table "public"."service_teacher" to "authenticated";

grant references on table "public"."service_teacher" to "authenticated";

grant select on table "public"."service_teacher" to "authenticated";

grant trigger on table "public"."service_teacher" to "authenticated";

grant truncate on table "public"."service_teacher" to "authenticated";

grant update on table "public"."service_teacher" to "authenticated";

grant delete on table "public"."service_teacher" to "service_role";

grant insert on table "public"."service_teacher" to "service_role";

grant references on table "public"."service_teacher" to "service_role";

grant select on table "public"."service_teacher" to "service_role";

grant trigger on table "public"."service_teacher" to "service_role";

grant truncate on table "public"."service_teacher" to "service_role";

grant update on table "public"."service_teacher" to "service_role";


