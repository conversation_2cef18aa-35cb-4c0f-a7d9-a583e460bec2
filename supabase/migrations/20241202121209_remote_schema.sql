drop trigger if exists "payment" on "public"."payment";

drop trigger if exists "payment_notification" on "public"."payment_notification";

alter table "public"."authorized_payment_notification" drop constraint "authorized_payment_notification_applicationId_fkey";

alter table "public"."payment" alter column "collectorId" set data type character varying using "collectorId"::character varying;

alter table "public"."subscription" drop column "applicationid";

alter table "public"."subscription" drop column "backurl";

alter table "public"."subscription" drop column "billingday";

alter table "public"."subscription" drop column "billingdayproportional";

alter table "public"."subscription" drop column "collectorid";

alter table "public"."subscription" drop column "externalreference";

alter table "public"."subscription" drop column "frequencytype";

alter table "public"."subscription" drop column "initpoint";

alter table "public"."subscription" drop column "nextpaymentdate";

alter table "public"."subscription" drop column "payerid";

alter table "public"."subscription" drop column "startdate";

alter table "public"."subscription" drop column "transactionamount";

alter table "public"."subscription" add column "applicationId" character varying;

alter table "public"."subscription" add column "backUrl" text;

alter table "public"."subscription" add column "billingDay" integer;

alter table "public"."subscription" add column "billingDayProportional" boolean;

alter table "public"."subscription" add column "collectorId" character varying;

alter table "public"."subscription" add column "externalReference" integer;

alter table "public"."subscription" add column "frequencyType" text;

alter table "public"."subscription" add column "initPoint" text;

alter table "public"."subscription" add column "nextPaymentDate" date;

alter table "public"."subscription" add column "payerId" character varying;

alter table "public"."subscription" add column "startDate" date;

alter table "public"."subscription" add column "transactionAmount" integer;

alter table "public"."authorized_payment_notification" add constraint "authorized_payment_notification_applicationid_fkey" FOREIGN KEY ("applicationId") REFERENCES mp_application(id) not valid;

alter table "public"."authorized_payment_notification" validate constraint "authorized_payment_notification_applicationid_fkey";

alter table "public"."subscription" add constraint "subscription_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES mp_application(id) not valid;

alter table "public"."subscription" validate constraint "subscription_applicationId_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.new_authorized_payment_notification()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_authorized_payment_notification?id=%s&type=%s',
            NEW.id,
            NEW.type
        ),
        ARRAY[http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.verify_plan_expiration()
 RETURNS void
 LANGUAGE plpgsql
AS $function$DECLARE
  expired_profile RECORD;
BEGIN
  FOR expired_profile IN
    SELECT *
    FROM public.plan_profile pp
    JOIN public.plan p ON pp."planId" = p.id
    WHERE (pp."createdAt" + INTERVAL '1 day' * p.expires) < NOW()
  LOOP
    UPDATE public.plan_profile
    SET active = false
    WHERE id = expired_profile.id;
  END LOOP;
END;$function$
;

CREATE TRIGGER authorized_payment_notification AFTER INSERT OR UPDATE ON public.authorized_payment_notification FOR EACH STATEMENT EXECUTE FUNCTION new_authorized_payment_notification();

CREATE TRIGGER payment AFTER INSERT OR UPDATE ON public.payment FOR EACH STATEMENT EXECUTE FUNCTION new_payment();

CREATE TRIGGER payment_notification AFTER INSERT OR UPDATE ON public.payment_notification FOR EACH STATEMENT EXECUTE FUNCTION new_payment_notification();


