drop view if exists "public"."attendance_monthly";

drop view if exists "public"."attendance_yearly";

create or replace view "public"."attendance_monthly" as  SELECT a."userId",
    a."serviceId",
    a."scheduleId",
    u.name,
    EXTRACT(year FROM a.date) AS year,
    EXTRACT(month FROM a.date) AS month,
    count(*) AS total
   FROM (attendance a
     JOIN profile u ON ((a."userId" = u.id)))
  GROUP BY a."userId", a."serviceId", a."scheduleId", u.name, (EXTRACT(year FROM a.date)), (EXTRACT(month FROM a.date))
  ORDER BY (count(*)) DESC;


create or replace view "public"."attendance_yearly" as  SELECT a."userId",
    a."serviceId",
    a."scheduleId",
    u.name,
    EXTRACT(year FROM a.date) AS year,
    count(*) AS total
   FROM (attendance a
     JOIN profile u ON ((a."userId" = u.id)))
  GROUP BY a."userId", a."serviceId", a."scheduleId", u.name, (EXTRACT(year FROM a.date))
  ORDER BY (count(*)) DESC;



