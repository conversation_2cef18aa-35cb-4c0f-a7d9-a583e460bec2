alter table "public"."rank" drop column "sport";

alter type "public"."sports" rename to "sports__old_version_to_be_dropped";

create type "public"."sports" as enum ('jiu-jitsu', 'muay-thai', 'karate', 'judo', 'surf');

create table "public"."rank_student" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "rankId" uuid,
    "profileId" uuid
);


alter table "public"."rank_student" enable row level security;

create table "public"."sport" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "name" character varying not null
);

alter table "public"."sport" enable row level security;

drop type "public"."sports__old_version_to_be_dropped";

alter table "public"."profile" add column "sportId" uuid;


alter table "public"."rank" add column "sportId" uuid;

CREATE UNIQUE INDEX rank_student_pkey ON public.rank_student USING btree (id);

CREATE UNIQUE INDEX sport_pkey ON public.sport USING btree (id);

alter table "public"."rank_student" add constraint "rank_student_pkey" PRIMARY KEY using index "rank_student_pkey";

alter table "public"."sport" add constraint "sport_pkey" PRIMARY KEY using index "sport_pkey";

alter table "public"."profile" add constraint "profile_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES sport(id) not valid;

alter table "public"."profile" validate constraint "profile_sportId_fkey";

alter table "public"."rank" add constraint "rank_sportid_fkey" FOREIGN KEY ("sportId") REFERENCES sport(id) not valid;

alter table "public"."rank" validate constraint "rank_sportid_fkey";

alter table "public"."rank_student" add constraint "rank_student_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."rank_student" validate constraint "rank_student_profileId_fkey";

alter table "public"."rank_student" add constraint "rank_student_rankId_fkey" FOREIGN KEY ("rankId") REFERENCES rank(id) not valid;

alter table "public"."rank_student" validate constraint "rank_student_rankId_fkey";

grant delete on table "public"."rank_student" to "anon";

grant insert on table "public"."rank_student" to "anon";

grant references on table "public"."rank_student" to "anon";

grant select on table "public"."rank_student" to "anon";

grant trigger on table "public"."rank_student" to "anon";

grant truncate on table "public"."rank_student" to "anon";

grant update on table "public"."rank_student" to "anon";

grant delete on table "public"."rank_student" to "authenticated";

grant insert on table "public"."rank_student" to "authenticated";

grant references on table "public"."rank_student" to "authenticated";

grant select on table "public"."rank_student" to "authenticated";

grant trigger on table "public"."rank_student" to "authenticated";

grant truncate on table "public"."rank_student" to "authenticated";

grant update on table "public"."rank_student" to "authenticated";

grant delete on table "public"."rank_student" to "service_role";

grant insert on table "public"."rank_student" to "service_role";

grant references on table "public"."rank_student" to "service_role";

grant select on table "public"."rank_student" to "service_role";

grant trigger on table "public"."rank_student" to "service_role";

grant truncate on table "public"."rank_student" to "service_role";

grant update on table "public"."rank_student" to "service_role";

grant delete on table "public"."sport" to "anon";

grant insert on table "public"."sport" to "anon";

grant references on table "public"."sport" to "anon";

grant select on table "public"."sport" to "anon";

grant trigger on table "public"."sport" to "anon";

grant truncate on table "public"."sport" to "anon";

grant update on table "public"."sport" to "anon";

grant delete on table "public"."sport" to "authenticated";

grant insert on table "public"."sport" to "authenticated";

grant references on table "public"."sport" to "authenticated";

grant select on table "public"."sport" to "authenticated";

grant trigger on table "public"."sport" to "authenticated";

grant truncate on table "public"."sport" to "authenticated";

grant update on table "public"."sport" to "authenticated";

grant delete on table "public"."sport" to "service_role";

grant insert on table "public"."sport" to "service_role";

grant references on table "public"."sport" to "service_role";

grant select on table "public"."sport" to "service_role";

grant trigger on table "public"."sport" to "service_role";

grant truncate on table "public"."sport" to "service_role";

grant update on table "public"."sport" to "service_role";

