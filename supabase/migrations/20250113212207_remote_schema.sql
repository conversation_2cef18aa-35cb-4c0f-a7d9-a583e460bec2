create table "public"."sale" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "name" character varying not null,
    "description" character varying,
    "value" numeric not null,
    "schoolId" uuid not null,
    "clientId" uuid not null,
    "packId" uuid,
    "billingDate" date not null,
    "method" payment_method,
    "saleRootId" uuid not null
);


alter table "public"."sale" enable row level security;

create table "public"."sale_root" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "value" numeric not null,
    "installments" smallint default '1'::smallint,
    "description" character varying,
    "name" character varying not null,
    "packId" uuid,
    "billingDay" smallint not null,
    "schoolId" uuid not null,
    "clientId" uuid not null,
    "method" payment_method
);


alter table "public"."sale_root" enable row level security;

CREATE UNIQUE INDEX sale_pkey ON public.sale USING btree (id);

CREATE UNIQUE INDEX sale_root_pkey ON public.sale_root USING btree (id);

alter table "public"."sale" add constraint "sale_pkey" PRIMARY KEY using index "sale_pkey";

alter table "public"."sale_root" add constraint "sale_root_pkey" PRIMARY KEY using index "sale_root_pkey";

alter table "public"."sale" add constraint "sale_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."sale" validate constraint "sale_clientId_fkey";

alter table "public"."sale" add constraint "sale_packId_fkey" FOREIGN KEY ("packId") REFERENCES pkg(id) ON DELETE SET NULL not valid;

alter table "public"."sale" validate constraint "sale_packId_fkey";

alter table "public"."sale" add constraint "sale_saleRootId_fkey" FOREIGN KEY ("saleRootId") REFERENCES sale_root(id) ON DELETE CASCADE not valid;

alter table "public"."sale" validate constraint "sale_saleRootId_fkey";

alter table "public"."sale" add constraint "sale_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) ON DELETE CASCADE not valid;

alter table "public"."sale" validate constraint "sale_schoolId_fkey";

alter table "public"."sale_root" add constraint "sale_root_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES profile(id) ON DELETE SET NULL not valid;

alter table "public"."sale_root" validate constraint "sale_root_clientId_fkey";

alter table "public"."sale_root" add constraint "sale_root_packId_fkey" FOREIGN KEY ("packId") REFERENCES pkg(id) ON DELETE CASCADE not valid;

alter table "public"."sale_root" validate constraint "sale_root_packId_fkey";

alter table "public"."sale_root" add constraint "sale_root_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) ON DELETE CASCADE not valid;

alter table "public"."sale_root" validate constraint "sale_root_schoolId_fkey";

grant delete on table "public"."sale" to "anon";

grant insert on table "public"."sale" to "anon";

grant references on table "public"."sale" to "anon";

grant select on table "public"."sale" to "anon";

grant trigger on table "public"."sale" to "anon";

grant truncate on table "public"."sale" to "anon";

grant update on table "public"."sale" to "anon";

grant delete on table "public"."sale" to "authenticated";

grant insert on table "public"."sale" to "authenticated";

grant references on table "public"."sale" to "authenticated";

grant select on table "public"."sale" to "authenticated";

grant trigger on table "public"."sale" to "authenticated";

grant truncate on table "public"."sale" to "authenticated";

grant update on table "public"."sale" to "authenticated";

grant delete on table "public"."sale" to "service_role";

grant insert on table "public"."sale" to "service_role";

grant references on table "public"."sale" to "service_role";

grant select on table "public"."sale" to "service_role";

grant trigger on table "public"."sale" to "service_role";

grant truncate on table "public"."sale" to "service_role";

grant update on table "public"."sale" to "service_role";

grant delete on table "public"."sale_root" to "anon";

grant insert on table "public"."sale_root" to "anon";

grant references on table "public"."sale_root" to "anon";

grant select on table "public"."sale_root" to "anon";

grant trigger on table "public"."sale_root" to "anon";

grant truncate on table "public"."sale_root" to "anon";

grant update on table "public"."sale_root" to "anon";

grant delete on table "public"."sale_root" to "authenticated";

grant insert on table "public"."sale_root" to "authenticated";

grant references on table "public"."sale_root" to "authenticated";

grant select on table "public"."sale_root" to "authenticated";

grant trigger on table "public"."sale_root" to "authenticated";

grant truncate on table "public"."sale_root" to "authenticated";

grant update on table "public"."sale_root" to "authenticated";

grant delete on table "public"."sale_root" to "service_role";

grant insert on table "public"."sale_root" to "service_role";

grant references on table "public"."sale_root" to "service_role";

grant select on table "public"."sale_root" to "service_role";

grant trigger on table "public"."sale_root" to "service_role";

grant truncate on table "public"."sale_root" to "service_role";

grant update on table "public"."sale_root" to "service_role";
