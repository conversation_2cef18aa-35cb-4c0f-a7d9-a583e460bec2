drop trigger if exists "authorized_payment_notification" on "public"."authorized_payment_notification";

revoke delete on table "public"."authorized_payment" from "anon";

revoke insert on table "public"."authorized_payment" from "anon";

revoke references on table "public"."authorized_payment" from "anon";

revoke select on table "public"."authorized_payment" from "anon";

revoke trigger on table "public"."authorized_payment" from "anon";

revoke truncate on table "public"."authorized_payment" from "anon";

revoke update on table "public"."authorized_payment" from "anon";

revoke delete on table "public"."authorized_payment" from "authenticated";

revoke insert on table "public"."authorized_payment" from "authenticated";

revoke references on table "public"."authorized_payment" from "authenticated";

revoke select on table "public"."authorized_payment" from "authenticated";

revoke trigger on table "public"."authorized_payment" from "authenticated";

revoke truncate on table "public"."authorized_payment" from "authenticated";

revoke update on table "public"."authorized_payment" from "authenticated";

revoke delete on table "public"."authorized_payment" from "service_role";

revoke insert on table "public"."authorized_payment" from "service_role";

revoke references on table "public"."authorized_payment" from "service_role";

revoke select on table "public"."authorized_payment" from "service_role";

revoke trigger on table "public"."authorized_payment" from "service_role";

revoke truncate on table "public"."authorized_payment" from "service_role";

revoke update on table "public"."authorized_payment" from "service_role";

revoke delete on table "public"."authorized_payment_notification" from "anon";

revoke insert on table "public"."authorized_payment_notification" from "anon";

revoke references on table "public"."authorized_payment_notification" from "anon";

revoke select on table "public"."authorized_payment_notification" from "anon";

revoke trigger on table "public"."authorized_payment_notification" from "anon";

revoke truncate on table "public"."authorized_payment_notification" from "anon";

revoke update on table "public"."authorized_payment_notification" from "anon";

revoke delete on table "public"."authorized_payment_notification" from "authenticated";

revoke insert on table "public"."authorized_payment_notification" from "authenticated";

revoke references on table "public"."authorized_payment_notification" from "authenticated";

revoke select on table "public"."authorized_payment_notification" from "authenticated";

revoke trigger on table "public"."authorized_payment_notification" from "authenticated";

revoke truncate on table "public"."authorized_payment_notification" from "authenticated";

revoke update on table "public"."authorized_payment_notification" from "authenticated";

revoke delete on table "public"."authorized_payment_notification" from "service_role";

revoke insert on table "public"."authorized_payment_notification" from "service_role";

revoke references on table "public"."authorized_payment_notification" from "service_role";

revoke select on table "public"."authorized_payment_notification" from "service_role";

revoke trigger on table "public"."authorized_payment_notification" from "service_role";

revoke truncate on table "public"."authorized_payment_notification" from "service_role";

revoke update on table "public"."authorized_payment_notification" from "service_role";

alter table "public"."authorized_payment_notification" drop constraint "authorized_payment_notification_applicationid_fkey";

alter table "public"."authorized_payment" drop constraint "authorized_payment_pkey";

alter table "public"."authorized_payment_notification" drop constraint "authorized_payment_notification_pkey";

drop index if exists "public"."authorized_payment_notification_pkey";

drop index if exists "public"."authorized_payment_pkey";

drop table "public"."authorized_payment";

drop table "public"."authorized_payment_notification";


