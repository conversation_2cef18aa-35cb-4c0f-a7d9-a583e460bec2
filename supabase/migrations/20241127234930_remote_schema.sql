alter table "public"."payment_notification" drop constraint "payment_notification_paymentId_fkey";

alter table "public"."payment_notification" drop constraint "payment_notification_pkey";

drop index if exists "public"."payment_notification_pkey";

alter table "public"."payment_notification" drop column "paymentId";

alter table "public"."payment_notification" add column "id" character varying not null;

CREATE UNIQUE INDEX payment_notification_pkey ON public.payment_notification USING btree (id);

alter table "public"."payment_notification" add constraint "payment_notification_pkey" PRIMARY KEY using index "payment_notification_pkey";

alter table "public"."payment_notification" add constraint "payment_notification_paymentId_fkey" FOREIGN KEY (id) REFERENCES payment(id) not valid;

alter table "public"."payment_notification" validate constraint "payment_notification_paymentId_fkey";


