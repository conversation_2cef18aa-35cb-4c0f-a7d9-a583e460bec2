create type "public"."semaphore" as enum ('green', 'yellow', 'red', 'blank');

alter table "public"."subscription" add column "chargedAmount" numeric;

alter table "public"."subscription" add column "chargedQty" smallint;

alter table "public"."subscription" add column "lastChargedAmount" numeric;

alter table "public"."subscription" add column "lastChargedDate" timestamp with time zone;

alter table "public"."subscription" add column "pendingChargeAmount" numeric;

alter table "public"."subscription" add column "pendingChargedQty" smallint;

alter table "public"."subscription" add column "semaphore" semaphore;


