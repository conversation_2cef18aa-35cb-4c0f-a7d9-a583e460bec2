drop view if exists "public"."booking_counts";

create table "public"."log" (
    "id" bigint generated by default as identity not null,
    "createdAt" timestamp with time zone not null default now(),
    "value" character varying not null
);


alter table "public"."log" enable row level security;

alter table "public"."plan" alter column "price" set not null;

alter table "public"."plan" alter column "price" set data type numeric using "price"::numeric;

CREATE UNIQUE INDEX log_pkey ON public.log USING btree (id);

alter table "public"."log" add constraint "log_pkey" PRIMARY KEY using index "log_pkey";

create or replace view "public"."bookings_count" as  SELECT b."userId",
    b."planId",
    count(*) AS total,
    sum(
        CASE
            WHEN (b.status = 'approved'::booking_status) THEN 1
            ELSE 0
        END) AS approved,
    sum(
        CASE
            WHEN (b.status = 'canceled'::booking_status) THEN 1
            ELSE 0
        END) AS canceled,
    sum(
        CASE
            WHEN (b.status = 'expired'::booking_status) THEN 1
            ELSE 0
        END) AS expired,
    sum(
        CASE
            WHEN (b.status = 'pending'::booking_status) THEN 1
            ELSE 0
        END) AS pending,
    sum(
        CASE
            WHEN (b.status = 'used'::booking_status) THEN 1
            ELSE 0
        END) AS used,
    sum(
        CASE
            WHEN (b.status = 'missed'::booking_status) THEN 1
            ELSE 0
        END) AS missed,
    sum(
        CASE
            WHEN (b.status = 'bailed'::booking_status) THEN 1
            ELSE 0
        END) AS bailed
   FROM booking b
  GROUP BY b."userId", b."planId";


grant delete on table "public"."log" to "anon";

grant insert on table "public"."log" to "anon";

grant references on table "public"."log" to "anon";

grant select on table "public"."log" to "anon";

grant trigger on table "public"."log" to "anon";

grant truncate on table "public"."log" to "anon";

grant update on table "public"."log" to "anon";

grant delete on table "public"."log" to "authenticated";

grant insert on table "public"."log" to "authenticated";

grant references on table "public"."log" to "authenticated";

grant select on table "public"."log" to "authenticated";

grant trigger on table "public"."log" to "authenticated";

grant truncate on table "public"."log" to "authenticated";

grant update on table "public"."log" to "authenticated";

grant delete on table "public"."log" to "service_role";

grant insert on table "public"."log" to "service_role";

grant references on table "public"."log" to "service_role";

grant select on table "public"."log" to "service_role";

grant trigger on table "public"."log" to "service_role";

grant truncate on table "public"."log" to "service_role";

grant update on table "public"."log" to "service_role";


