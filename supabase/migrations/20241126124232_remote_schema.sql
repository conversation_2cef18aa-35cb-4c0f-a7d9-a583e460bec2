create table "public"."plan_profile" (
    "id" bigint generated by default as identity not null,
    "createdAt" timestamp with time zone not null default now(),
    "planId" uuid not null default gen_random_uuid(),
    "profileId" uuid not null default gen_random_uuid(),
    "active" boolean not null default true
);


alter table "public"."plan_profile" enable row level security;

create table "public"."transaction_detail" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "paymentMethodReferenceId" character varying,
    "acquirerReference" character varying,
    "netReceivedAmount" smallint,
    "totalPaidAmount" smallint,
    "overPaidAmount" smallint,
    "installmentAmount" smallint,
    "financialInstitution" character varying,
    "transactionId" character varying,
    "digitableLine" character varying,
    "verificationCode" character varying,
    "bankTransferId" character varying,
    "barCode" character varying
);


alter table "public"."transaction_detail" enable row level security;

alter table "public"."booking" add column "planId" uuid;

alter table "public"."payment" add column "additionalInfo" character varying;

alter table "public"."payment" add column "authorizationCode" character varying;

alter table "public"."payment" add column "buildVersion" character varying;

alter table "public"."payment" add column "captured" boolean;

alter table "public"."payment" add column "collectorId" smallint;

alter table "public"."payment" add column "corporationId" character varying;

alter table "public"."payment" add column "counterCurrency" character varying;

alter table "public"."payment" add column "couponAmount" smallint;

alter table "public"."payment" add column "couponCode" character varying;

alter table "public"."payment" add column "currencyId" character varying;

alter table "public"."payment" add column "dateApproved" timestamp with time zone;

alter table "public"."payment" add column "dateCreated" timestamp with time zone;

alter table "public"."payment" add column "dateLastUpdated" timestamp with time zone;

alter table "public"."payment" add column "dateOfExpiration" timestamp with time zone;

alter table "public"."payment" add column "description" character varying;

alter table "public"."payment" add column "installments" smallint;

alter table "public"."payment" add column "integratorId" character varying;

alter table "public"."payment" add column "issuerId" character varying;

alter table "public"."payment" add column "liveMode" boolean;

alter table "public"."payment" add column "merchantAccountId" character varying;

alter table "public"."payment" add column "merchantNumber" character varying;

alter table "public"."payment" add column "method" character varying;

alter table "public"."payment" add column "methodId" character varying;

alter table "public"."payment" add column "methodOptionId" character varying;

alter table "public"."payment" add column "moneyReleaseDate" timestamp with time zone;

alter table "public"."payment" add column "moneyReleaseSchema" character varying;

alter table "public"."payment" add column "moneyReleaseStatus" character varying;

alter table "public"."payment" add column "netAmount" smallint;

alter table "public"."payment" add column "operationType" character varying;

alter table "public"."payment" add column "platformId" character varying;

alter table "public"."payment" add column "posId" character varying;

alter table "public"."payment" add column "profileId" uuid not null;

alter table "public"."payment" add column "shippingAmount" smallint;

alter table "public"."payment" add column "sponsorId" character varying;

alter table "public"."payment" add column "storeId" character varying;

alter table "public"."payment" add column "taxesAmount" smallint;

alter table "public"."payment" add column "transactionAmount" smallint;

alter table "public"."payment" add column "transactionAmountRefunded" smallint;

alter table "public"."payment" add column "transactionDetailId" uuid;

alter table "public"."payment" add column "typeId" character varying;

alter table "public"."plan" add column "active" boolean default false;

alter table "public"."plan" add column "expires" smallint;

alter table "public"."plan" add column "use" smallint;

alter table "public"."plan" alter column "planId" drop not null;

CREATE UNIQUE INDEX plan_profile_pkey ON public.plan_profile USING btree (id);

CREATE UNIQUE INDEX transaction_detail_pkey ON public.transaction_detail USING btree (id);

alter table "public"."plan_profile" add constraint "plan_profile_pkey" PRIMARY KEY using index "plan_profile_pkey";

alter table "public"."transaction_detail" add constraint "transaction_detail_pkey" PRIMARY KEY using index "transaction_detail_pkey";

alter table "public"."booking" add constraint "booking_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) not valid;

alter table "public"."booking" validate constraint "booking_planId_fkey";

alter table "public"."payment" add constraint "payment_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) not valid;

alter table "public"."payment" validate constraint "payment_profileId_fkey";

alter table "public"."payment" add constraint "payment_transactionDetailId_fkey" FOREIGN KEY ("transactionDetailId") REFERENCES transaction_detail(id) not valid;

alter table "public"."payment" validate constraint "payment_transactionDetailId_fkey";

alter table "public"."plan_profile" add constraint "plan_profile_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) ON DELETE CASCADE not valid;

alter table "public"."plan_profile" validate constraint "plan_profile_planId_fkey";

alter table "public"."plan_profile" add constraint "plan_profile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."plan_profile" validate constraint "plan_profile_profileId_fkey";

grant delete on table "public"."plan_profile" to "anon";

grant insert on table "public"."plan_profile" to "anon";

grant references on table "public"."plan_profile" to "anon";

grant select on table "public"."plan_profile" to "anon";

grant trigger on table "public"."plan_profile" to "anon";

grant truncate on table "public"."plan_profile" to "anon";

grant update on table "public"."plan_profile" to "anon";

grant delete on table "public"."plan_profile" to "authenticated";

grant insert on table "public"."plan_profile" to "authenticated";

grant references on table "public"."plan_profile" to "authenticated";

grant select on table "public"."plan_profile" to "authenticated";

grant trigger on table "public"."plan_profile" to "authenticated";

grant truncate on table "public"."plan_profile" to "authenticated";

grant update on table "public"."plan_profile" to "authenticated";

grant delete on table "public"."plan_profile" to "service_role";

grant insert on table "public"."plan_profile" to "service_role";

grant references on table "public"."plan_profile" to "service_role";

grant select on table "public"."plan_profile" to "service_role";

grant trigger on table "public"."plan_profile" to "service_role";

grant truncate on table "public"."plan_profile" to "service_role";

grant update on table "public"."plan_profile" to "service_role";

grant delete on table "public"."transaction_detail" to "anon";

grant insert on table "public"."transaction_detail" to "anon";

grant references on table "public"."transaction_detail" to "anon";

grant select on table "public"."transaction_detail" to "anon";

grant trigger on table "public"."transaction_detail" to "anon";

grant truncate on table "public"."transaction_detail" to "anon";

grant update on table "public"."transaction_detail" to "anon";

grant delete on table "public"."transaction_detail" to "authenticated";

grant insert on table "public"."transaction_detail" to "authenticated";

grant references on table "public"."transaction_detail" to "authenticated";

grant select on table "public"."transaction_detail" to "authenticated";

grant trigger on table "public"."transaction_detail" to "authenticated";

grant truncate on table "public"."transaction_detail" to "authenticated";

grant update on table "public"."transaction_detail" to "authenticated";

grant delete on table "public"."transaction_detail" to "service_role";

grant insert on table "public"."transaction_detail" to "service_role";

grant references on table "public"."transaction_detail" to "service_role";

grant select on table "public"."transaction_detail" to "service_role";

grant trigger on table "public"."transaction_detail" to "service_role";

grant truncate on table "public"."transaction_detail" to "service_role";

grant update on table "public"."transaction_detail" to "service_role";


