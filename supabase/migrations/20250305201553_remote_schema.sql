alter table "public"."contract" drop constraint "contract_studentEmail_fkey";

alter table "public"."contract" drop column "studentEmail";

alter table "public"."contract" add column "signed" boolean default false;

alter table "public"."contract" add column "studentId" uuid;

alter table "public"."contract" add constraint "contract_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES profile(id) not valid;

alter table "public"."contract" validate constraint "contract_studentId_fkey";