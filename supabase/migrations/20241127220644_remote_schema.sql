create table "public"."payment_created" (
    "id" bigint generated by default as identity not null,
    "createdAt" timestamp with time zone not null default now(),
    "notificationId" uuid default gen_random_uuid()
);


alter table "public"."payment_created" enable row level security;

create table "public"."payment_plan" (
    "id" bigint generated by default as identity not null,
    "createdAt" timestamp with time zone not null default now(),
    "paymentId" character varying not null,
    "planId" character varying not null
);


alter table "public"."payment_plan" enable row level security;

alter table "public"."payment_notification" add column "planId" character varying;

CREATE UNIQUE INDEX payment_created_pkey ON public.payment_created USING btree (id);

CREATE UNIQUE INDEX payment_plan_pkey ON public.payment_plan USING btree (id);

alter table "public"."payment_created" add constraint "payment_created_pkey" PRIMARY KEY using index "payment_created_pkey";

alter table "public"."payment_plan" add constraint "payment_plan_pkey" PRIMARY KEY using index "payment_plan_pkey";

alter table "public"."payment_created" add constraint "payment_created_notificationId_fkey" FOREIGN KEY ("notificationId") REFERENCES payment_notification(id) not valid;

alter table "public"."payment_created" validate constraint "payment_created_notificationId_fkey";

alter table "public"."payment_plan" add constraint "payment_plan_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) not valid;

alter table "public"."payment_plan" validate constraint "payment_plan_planId_fkey";

grant delete on table "public"."payment_created" to "anon";

grant insert on table "public"."payment_created" to "anon";

grant references on table "public"."payment_created" to "anon";

grant select on table "public"."payment_created" to "anon";

grant trigger on table "public"."payment_created" to "anon";

grant truncate on table "public"."payment_created" to "anon";

grant update on table "public"."payment_created" to "anon";

grant delete on table "public"."payment_created" to "authenticated";

grant insert on table "public"."payment_created" to "authenticated";

grant references on table "public"."payment_created" to "authenticated";

grant select on table "public"."payment_created" to "authenticated";

grant trigger on table "public"."payment_created" to "authenticated";

grant truncate on table "public"."payment_created" to "authenticated";

grant update on table "public"."payment_created" to "authenticated";

grant delete on table "public"."payment_created" to "service_role";

grant insert on table "public"."payment_created" to "service_role";

grant references on table "public"."payment_created" to "service_role";

grant select on table "public"."payment_created" to "service_role";

grant trigger on table "public"."payment_created" to "service_role";

grant truncate on table "public"."payment_created" to "service_role";

grant update on table "public"."payment_created" to "service_role";

grant delete on table "public"."payment_plan" to "anon";

grant insert on table "public"."payment_plan" to "anon";

grant references on table "public"."payment_plan" to "anon";

grant select on table "public"."payment_plan" to "anon";

grant trigger on table "public"."payment_plan" to "anon";

grant truncate on table "public"."payment_plan" to "anon";

grant update on table "public"."payment_plan" to "anon";

grant delete on table "public"."payment_plan" to "authenticated";

grant insert on table "public"."payment_plan" to "authenticated";

grant references on table "public"."payment_plan" to "authenticated";

grant select on table "public"."payment_plan" to "authenticated";

grant trigger on table "public"."payment_plan" to "authenticated";

grant truncate on table "public"."payment_plan" to "authenticated";

grant update on table "public"."payment_plan" to "authenticated";

grant delete on table "public"."payment_plan" to "service_role";

grant insert on table "public"."payment_plan" to "service_role";

grant references on table "public"."payment_plan" to "service_role";

grant select on table "public"."payment_plan" to "service_role";

grant trigger on table "public"."payment_plan" to "service_role";

grant truncate on table "public"."payment_plan" to "service_role";

grant update on table "public"."payment_plan" to "service_role";


