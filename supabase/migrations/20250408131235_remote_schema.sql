CREATE OR R<PERSON>LACE FUNCTION public.verify_pack_expiration()
  RET<PERSON>NS void
  LANGUAGE plpgsql
AS $function$DECLARE
  expired_profile RECORD;
BEGIN
  FOR expired_profile IN
    SELECT *
    FROM public.pack_profile pp
    WHERE pp."expireDate" < NOW()::DATE -- Compare expiredDate
    AND pp."purchaseStatus" != 'used' -- Filter not used packProf
  LOOP
    UPDATE public.pack_profile
    SET "purchaseStatus" = 'expired'
    WHERE id = expired_profile.id;
  END LOOP;
END;$function$
;
