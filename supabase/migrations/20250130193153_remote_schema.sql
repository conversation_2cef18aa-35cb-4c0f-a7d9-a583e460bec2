alter table "public"."cancelation_policy" add column "packId" uuid;

alter table "public"."cancelation_policy" alter column "hour" set data type smallint using "hour"::smallint;

alter table "public"."cancelation_policy" alter column "percentage" set default '100'::smallint;

alter table "public"."service" drop column "cancelationPolicy";

alter table "public"."cancelation_policy" add constraint "cancelation_policy_packId_fkey" FOREIGN KEY ("packId") REFERENCES pack(id) ON DELETE CASCADE not valid;

alter table "public"."cancelation_policy" validate constraint "cancelation_policy_packId_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.verify_pack_expiration()
RETURNS void
LANGUAGE plpgsql
AS $function$DECLARE
  expired_profile RECORD;
BEGIN
  FOR expired_profile IN
    SELECT *
    FROM public.pack_profile pp
    JOIN public.pack p ON pp."packId" = p.id
    WHERE (pp."createdAt" + INTERVAL '1 day' * p.expires) < NOW()
  LOOP
    UPDATE public.pack_profile
    SET active = false
    WHERE id = expired_profile.id;
  END LOOP;
END;$function$
;
