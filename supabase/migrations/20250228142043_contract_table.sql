CREATE TABLE public.contract (
  id character varying NOT NULL,
  "createdAt" timestamp with time zone NOT NULL DEFAULT now(),
  "schoolId" uuid NULL,
  "profileId" uuid NULL,
  "subscriptionId" character varying NULL,
  CONSTRAINT contract_pkey PRIMARY KEY (id),
  CONSTRAINT contract_profileId_fkey FOREIGN KEY ("profileId") REFERENCES public.profile (id),
  CONSTRAINT contract_schoolId_fkey FOREIGN KEY ("schoolId") REFERENCES public.school (id),
  CONSTRAINT contract_subscriptionId_fkey FOREIGN KEY ("subscriptionId") REFERENCES public.subscription (id)
) TABLESPACE pg_default;