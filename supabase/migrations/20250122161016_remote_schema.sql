create table "public"."sport_school" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "sportId" uuid not null,
    "schoolId" uuid not null
);

alter table "public"."sport_school" enable row level security;

CREATE UNIQUE INDEX sport_school_pkey ON public.sport_school USING btree (id);

alter table "public"."sport_school" add constraint "sport_school_pkey" PRIMARY KEY using index "sport_school_pkey";

alter table "public"."sport_school" add constraint "sport_school_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) ON DELETE CASCADE not valid;

alter table "public"."sport_school" validate constraint "sport_school_schoolId_fkey";

alter table "public"."sport_school" add constraint "sport_school_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES sport(id) ON DELETE CASCADE not valid;

alter table "public"."sport_school" validate constraint "sport_school_sportId_fkey";

grant delete on table "public"."sport_school" to "anon";

grant insert on table "public"."sport_school" to "anon";

grant references on table "public"."sport_school" to "anon";

grant select on table "public"."sport_school" to "anon";

grant trigger on table "public"."sport_school" to "anon";

grant truncate on table "public"."sport_school" to "anon";

grant update on table "public"."sport_school" to "anon";

grant delete on table "public"."sport_school" to "authenticated";

grant insert on table "public"."sport_school" to "authenticated";

grant references on table "public"."sport_school" to "authenticated";

grant select on table "public"."sport_school" to "authenticated";

grant trigger on table "public"."sport_school" to "authenticated";

grant truncate on table "public"."sport_school" to "authenticated";

grant update on table "public"."sport_school" to "authenticated";

grant delete on table "public"."sport_school" to "service_role";

grant insert on table "public"."sport_school" to "service_role";

grant references on table "public"."sport_school" to "service_role";

grant select on table "public"."sport_school" to "service_role";

grant trigger on table "public"."sport_school" to "service_role";

grant truncate on table "public"."sport_school" to "service_role";

grant update on table "public"."sport_school" to "service_role";
