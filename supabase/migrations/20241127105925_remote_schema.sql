alter table "public"."payment_notification" drop constraint "payment_notification_pkey";

drop index if exists "public"."payment_notification_pkey";

alter table "public"."payment_notification" add column "paymentId" character varying;

alter table "public"."payment_notification" alter column "id" set default gen_random_uuid();

alter table "public"."payment_notification" alter column "id" set data type uuid using "id"::uuid;

CREATE UNIQUE INDEX payment_notification_pkey ON public.payment_notification USING btree (id);

alter table "public"."payment_notification" add constraint "payment_notification_pkey" PRIMARY KEY using index "payment_notification_pkey";


