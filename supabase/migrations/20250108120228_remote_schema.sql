alter table "public"."debt_category" add column "schoolId" uuid;

CREATE UNIQUE INDEX unique_name_schoolid ON public.category USING btree (name, "schoolId");

alter table "public"."category" add constraint "unique_name_schoolid" UNIQUE using index "unique_name_schoolid";

alter table "public"."debt_category" add constraint "debt_category_schoolId_fkey1" FOREIGN KEY ("schoolId") REFERENCES school(id) ON DELETE CASCADE not valid;

alter table "public"."debt_category" validate constraint "debt_category_schoolId_fkey1";
