create table "public"."payment" (
    "id" character varying not null,
    "createdAt" timestamp with time zone not null default now(),
    "profileAsId" character varying,
    "value" numeric not null,
    "netValue" numeric,
    "description" character varying,
    "paymentType" payment_method,
    "dueDate" date,
    "paymentDate" date,
    "installmentNumber" smallint,
    "invoiceUrl" character varying,
    "externalReference" character varying,
    "planId" uuid,
    "packId" uuid,
    "dateCreated" date,
    "subscriptionId" character varying,
    "status" payment_status
);


alter table "public"."payment" enable row level security;

CREATE UNIQUE INDEX payment_pkey ON public.payment USING btree (id);

alter table "public"."payment" add constraint "payment_pkey" PRIMARY KEY using index "payment_pkey";

alter table "public"."pack_profile" add constraint "pack_profile_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES payment(id) ON DELETE SET NULL not valid;

alter table "public"."pack_profile" validate constraint "pack_profile_paymentId_fkey";

alter table "public"."payment" add constraint "payment_packId_fkey" FOREIGN KEY ("packId") REFERENCES pack(id) ON DELETE SET NULL not valid;

alter table "public"."payment" validate constraint "payment_packId_fkey";

alter table "public"."payment" add constraint "payment_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) ON DELETE SET NULL not valid;

alter table "public"."payment" validate constraint "payment_planId_fkey";

alter table "public"."payment" add constraint "payment_profileAsId_fkey" FOREIGN KEY ("profileAsId") REFERENCES profile("asId") ON DELETE SET NULL not valid;

alter table "public"."payment" validate constraint "payment_profileAsId_fkey";

alter table "public"."payment" add constraint "payment_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES subscription(id) ON DELETE SET NULL not valid;

alter table "public"."payment" validate constraint "payment_subscriptionId_fkey";

grant delete on table "public"."payment" to "anon";

grant insert on table "public"."payment" to "anon";

grant references on table "public"."payment" to "anon";

grant select on table "public"."payment" to "anon";

grant trigger on table "public"."payment" to "anon";

grant truncate on table "public"."payment" to "anon";

grant update on table "public"."payment" to "anon";

grant delete on table "public"."payment" to "authenticated";

grant insert on table "public"."payment" to "authenticated";

grant references on table "public"."payment" to "authenticated";

grant select on table "public"."payment" to "authenticated";

grant trigger on table "public"."payment" to "authenticated";

grant truncate on table "public"."payment" to "authenticated";

grant update on table "public"."payment" to "authenticated";

grant delete on table "public"."payment" to "service_role";

grant insert on table "public"."payment" to "service_role";

grant references on table "public"."payment" to "service_role";

grant select on table "public"."payment" to "service_role";

grant trigger on table "public"."payment" to "service_role";

grant truncate on table "public"."payment" to "service_role";

grant update on table "public"."payment" to "service_role";
