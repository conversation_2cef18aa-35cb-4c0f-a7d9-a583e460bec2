drop trigger if exists "payment" on "public"."payment";

drop trigger if exists "payment_notification" on "public"."payment_notification";

revoke delete on table "public"."package" from "anon";

revoke insert on table "public"."package" from "anon";

revoke references on table "public"."package" from "anon";

revoke select on table "public"."package" from "anon";

revoke trigger on table "public"."package" from "anon";

revoke truncate on table "public"."package" from "anon";

revoke update on table "public"."package" from "anon";

revoke delete on table "public"."package" from "authenticated";

revoke insert on table "public"."package" from "authenticated";

revoke references on table "public"."package" from "authenticated";

revoke select on table "public"."package" from "authenticated";

revoke trigger on table "public"."package" from "authenticated";

revoke truncate on table "public"."package" from "authenticated";

revoke update on table "public"."package" from "authenticated";

revoke delete on table "public"."package" from "service_role";

revoke insert on table "public"."package" from "service_role";

revoke references on table "public"."package" from "service_role";

revoke select on table "public"."package" from "service_role";

revoke trigger on table "public"."package" from "service_role";

revoke truncate on table "public"."package" from "service_role";

revoke update on table "public"."package" from "service_role";

revoke delete on table "public"."payment" from "anon";

revoke insert on table "public"."payment" from "anon";

revoke references on table "public"."payment" from "anon";

revoke select on table "public"."payment" from "anon";

revoke trigger on table "public"."payment" from "anon";

revoke truncate on table "public"."payment" from "anon";

revoke update on table "public"."payment" from "anon";

revoke delete on table "public"."payment" from "authenticated";

revoke insert on table "public"."payment" from "authenticated";

revoke references on table "public"."payment" from "authenticated";

revoke select on table "public"."payment" from "authenticated";

revoke trigger on table "public"."payment" from "authenticated";

revoke truncate on table "public"."payment" from "authenticated";

revoke update on table "public"."payment" from "authenticated";

revoke delete on table "public"."payment" from "service_role";

revoke insert on table "public"."payment" from "service_role";

revoke references on table "public"."payment" from "service_role";

revoke select on table "public"."payment" from "service_role";

revoke trigger on table "public"."payment" from "service_role";

revoke truncate on table "public"."payment" from "service_role";

revoke update on table "public"."payment" from "service_role";

revoke delete on table "public"."payment_notification" from "anon";

revoke insert on table "public"."payment_notification" from "anon";

revoke references on table "public"."payment_notification" from "anon";

revoke select on table "public"."payment_notification" from "anon";

revoke trigger on table "public"."payment_notification" from "anon";

revoke truncate on table "public"."payment_notification" from "anon";

revoke update on table "public"."payment_notification" from "anon";

revoke delete on table "public"."payment_notification" from "authenticated";

revoke insert on table "public"."payment_notification" from "authenticated";

revoke references on table "public"."payment_notification" from "authenticated";

revoke select on table "public"."payment_notification" from "authenticated";

revoke trigger on table "public"."payment_notification" from "authenticated";

revoke truncate on table "public"."payment_notification" from "authenticated";

revoke update on table "public"."payment_notification" from "authenticated";

revoke delete on table "public"."payment_notification" from "service_role";

revoke insert on table "public"."payment_notification" from "service_role";

revoke references on table "public"."payment_notification" from "service_role";

revoke select on table "public"."payment_notification" from "service_role";

revoke trigger on table "public"."payment_notification" from "service_role";

revoke truncate on table "public"."payment_notification" from "service_role";

revoke update on table "public"."payment_notification" from "service_role";

alter table "public"."package" drop constraint "package_schoolId_fkey";

alter table "public"."payment" drop constraint "payment_planId_fkey";

alter table "public"."payment" drop constraint "payment_profileId_fkey";

drop function if exists "public"."new_payment"();

drop function if exists "public"."new_payment_notification"();

drop function if exists "public"."verify_plan_expiration"();

drop view if exists "public"."bookings_count";

alter table "public"."package" drop constraint "package_pkey";

alter table "public"."payment" drop constraint "payment_pkey";

alter table "public"."payment_notification" drop constraint "payment_notification_pkey";

drop index if exists "public"."package_pkey";

drop index if exists "public"."payment_notification_pkey";

drop index if exists "public"."payment_pkey";

drop table "public"."package";

drop table "public"."payment";

drop table "public"."payment_notification";

create table "public"."payment_pkg" (
    "id" character varying not null,
    "createdAt" timestamp with time zone not null default now(),
    "status" character varying not null,
    "statusDetails" character varying,
    "captured" boolean,
    "collectorId" character varying,
    "dateApproved" timestamp with time zone,
    "dateCreated" timestamp with time zone,
    "profileId" uuid,
    "transactionAmount" numeric,
    "typeId" character varying,
    "dateLastUpdated" timestamp without time zone,
    "pkg" uuid default gen_random_uuid()
);


alter table "public"."payment_pkg" enable row level security;

create table "public"."payment_pkg_notification" (
    "action" character varying not null,
    "date" timestamp without time zone,
    "type" character varying,
    "createdAt" timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
    "id" character varying not null
);


alter table "public"."payment_pkg_notification" enable row level security;

create table "public"."payment_plan" (
    "id" character varying not null,
    "createdAt" timestamp with time zone not null default now(),
    "status" character varying not null,
    "statusDetails" character varying,
    "captured" boolean,
    "collectorId" character varying,
    "dateApproved" timestamp with time zone,
    "dateCreated" timestamp with time zone,
    "profileId" uuid,
    "transactionAmount" numeric,
    "typeId" character varying,
    "planId" character varying,
    "dateLastUpdated" timestamp without time zone
);


alter table "public"."payment_plan" enable row level security;

create table "public"."payment_plan_notification" (
    "action" character varying not null,
    "date" timestamp without time zone,
    "type" character varying,
    "createdAt" timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
    "id" character varying not null
);


alter table "public"."payment_plan_notification" enable row level security;

create table "public"."pkg" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "name" character varying not null,
    "schoolId" uuid not null default gen_random_uuid(),
    "price" numeric not null,
    "active" boolean not null default true,
    "expires" smallint,
    "use" smallint
);


alter table "public"."pkg" enable row level security;

create table "public"."pkg_profile" (
    "id" bigint generated by default as identity not null,
    "createdAt" timestamp with time zone not null default now(),
    "pkgId" uuid not null default gen_random_uuid(),
    "profileId" uuid not null default gen_random_uuid(),
    "active" boolean default true
);


alter table "public"."pkg_profile" enable row level security;

alter table "public"."booking" add column "pkgId" uuid default gen_random_uuid();

CREATE UNIQUE INDEX payment_pkg_notification_pkey ON public.payment_pkg_notification USING btree (id);

CREATE UNIQUE INDEX payment_pkg_pkey ON public.payment_pkg USING btree (id);

CREATE UNIQUE INDEX pkg_profile_pkey ON public.pkg_profile USING btree (id);

CREATE UNIQUE INDEX package_pkey ON public.pkg USING btree (id);

CREATE UNIQUE INDEX payment_notification_pkey ON public.payment_plan_notification USING btree (id);

CREATE UNIQUE INDEX payment_pkey ON public.payment_plan USING btree (id);

alter table "public"."payment_pkg" add constraint "payment_pkg_pkey" PRIMARY KEY using index "payment_pkg_pkey";

alter table "public"."payment_pkg_notification" add constraint "payment_pkg_notification_pkey" PRIMARY KEY using index "payment_pkg_notification_pkey";

alter table "public"."payment_plan" add constraint "payment_pkey" PRIMARY KEY using index "payment_pkey";

alter table "public"."payment_plan_notification" add constraint "payment_notification_pkey" PRIMARY KEY using index "payment_notification_pkey";

alter table "public"."pkg" add constraint "package_pkey" PRIMARY KEY using index "package_pkey";

alter table "public"."pkg_profile" add constraint "pkg_profile_pkey" PRIMARY KEY using index "pkg_profile_pkey";

alter table "public"."booking" add constraint "booking_packageId_fkey" FOREIGN KEY ("pkgId") REFERENCES pkg(id) not valid;

alter table "public"."booking" validate constraint "booking_packageId_fkey";

alter table "public"."payment_pkg" add constraint "payment_pkg_pkg_fkey" FOREIGN KEY (pkg) REFERENCES pkg(id) not valid;

alter table "public"."payment_pkg" validate constraint "payment_pkg_pkg_fkey";

alter table "public"."payment_pkg" add constraint "payment_pkg_profileid_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) not valid;

alter table "public"."payment_pkg" validate constraint "payment_pkg_profileid_fkey";

alter table "public"."payment_plan" add constraint "payment_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) not valid;

alter table "public"."payment_plan" validate constraint "payment_planId_fkey";

alter table "public"."payment_plan" add constraint "payment_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) not valid;

alter table "public"."payment_plan" validate constraint "payment_profileId_fkey";

alter table "public"."pkg" add constraint "package_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) not valid;

alter table "public"."pkg" validate constraint "package_schoolId_fkey";

alter table "public"."pkg_profile" add constraint "pkg_profile_pkgId_fkey" FOREIGN KEY ("pkgId") REFERENCES pkg(id) not valid;

alter table "public"."pkg_profile" validate constraint "pkg_profile_pkgId_fkey";

alter table "public"."pkg_profile" add constraint "pkg_profile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) not valid;

alter table "public"."pkg_profile" validate constraint "pkg_profile_profileId_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.new_payment_pkg()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_pkg?paymentId=%s',
            NEW.id
        ),
        ARRAY[http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_pkg_notification()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_pkg_notification?paymentId=%s',
            NEW.id
        ),
        ARRAY[http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_plan()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_plan?paymentId=%s',
            NEW.id
        ),
        ARRAY[http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.new_payment_plan_notification()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_plan_notification?paymentId=%s',
            NEW.id
        ),
        ARRAY[http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE OR REPLACE FUNCTION public.verify_pkg_expiration()
 RETURNS void
 LANGUAGE plpgsql
AS $function$DECLARE
  expired_profile RECORD;
BEGIN
  FOR expired_profile IN
    SELECT *
    FROM public.pkg_profile pp
    JOIN public.pkg p ON pp."pkgId" = p.id
    WHERE (pp."createdAt" + INTERVAL '1 day' * p.expires) < NOW()
  LOOP
    UPDATE public.pkg_profile
    SET active = false
    WHERE id = expired_profile.id;
  END LOOP;
END;$function$
;

create or replace view "public"."bookings_count" as  SELECT b."userId",
    b."pkgId",
    count(*) AS total,
    sum(
        CASE
            WHEN (b.status = 'approved'::booking_status) THEN 1
            ELSE 0
        END) AS approved,
    sum(
        CASE
            WHEN (b.status = 'canceled'::booking_status) THEN 1
            ELSE 0
        END) AS canceled,
    sum(
        CASE
            WHEN (b.status = 'expired'::booking_status) THEN 1
            ELSE 0
        END) AS expired,
    sum(
        CASE
            WHEN (b.status = 'pending'::booking_status) THEN 1
            ELSE 0
        END) AS pending,
    sum(
        CASE
            WHEN (b.status = 'used'::booking_status) THEN 1
            ELSE 0
        END) AS used,
    sum(
        CASE
            WHEN (b.status = 'missed'::booking_status) THEN 1
            ELSE 0
        END) AS missed,
    sum(
        CASE
            WHEN (b.status = 'bailed'::booking_status) THEN 1
            ELSE 0
        END) AS bailed
   FROM booking b
  GROUP BY b."userId", b."pkgId";


grant delete on table "public"."payment_pkg" to "anon";

grant insert on table "public"."payment_pkg" to "anon";

grant references on table "public"."payment_pkg" to "anon";

grant select on table "public"."payment_pkg" to "anon";

grant trigger on table "public"."payment_pkg" to "anon";

grant truncate on table "public"."payment_pkg" to "anon";

grant update on table "public"."payment_pkg" to "anon";

grant delete on table "public"."payment_pkg" to "authenticated";

grant insert on table "public"."payment_pkg" to "authenticated";

grant references on table "public"."payment_pkg" to "authenticated";

grant select on table "public"."payment_pkg" to "authenticated";

grant trigger on table "public"."payment_pkg" to "authenticated";

grant truncate on table "public"."payment_pkg" to "authenticated";

grant update on table "public"."payment_pkg" to "authenticated";

grant delete on table "public"."payment_pkg" to "service_role";

grant insert on table "public"."payment_pkg" to "service_role";

grant references on table "public"."payment_pkg" to "service_role";

grant select on table "public"."payment_pkg" to "service_role";

grant trigger on table "public"."payment_pkg" to "service_role";

grant truncate on table "public"."payment_pkg" to "service_role";

grant update on table "public"."payment_pkg" to "service_role";

grant delete on table "public"."payment_pkg_notification" to "anon";

grant insert on table "public"."payment_pkg_notification" to "anon";

grant references on table "public"."payment_pkg_notification" to "anon";

grant select on table "public"."payment_pkg_notification" to "anon";

grant trigger on table "public"."payment_pkg_notification" to "anon";

grant truncate on table "public"."payment_pkg_notification" to "anon";

grant update on table "public"."payment_pkg_notification" to "anon";

grant delete on table "public"."payment_pkg_notification" to "authenticated";

grant insert on table "public"."payment_pkg_notification" to "authenticated";

grant references on table "public"."payment_pkg_notification" to "authenticated";

grant select on table "public"."payment_pkg_notification" to "authenticated";

grant trigger on table "public"."payment_pkg_notification" to "authenticated";

grant truncate on table "public"."payment_pkg_notification" to "authenticated";

grant update on table "public"."payment_pkg_notification" to "authenticated";

grant delete on table "public"."payment_pkg_notification" to "service_role";

grant insert on table "public"."payment_pkg_notification" to "service_role";

grant references on table "public"."payment_pkg_notification" to "service_role";

grant select on table "public"."payment_pkg_notification" to "service_role";

grant trigger on table "public"."payment_pkg_notification" to "service_role";

grant truncate on table "public"."payment_pkg_notification" to "service_role";

grant update on table "public"."payment_pkg_notification" to "service_role";

grant delete on table "public"."payment_plan" to "anon";

grant insert on table "public"."payment_plan" to "anon";

grant references on table "public"."payment_plan" to "anon";

grant select on table "public"."payment_plan" to "anon";

grant trigger on table "public"."payment_plan" to "anon";

grant truncate on table "public"."payment_plan" to "anon";

grant update on table "public"."payment_plan" to "anon";

grant delete on table "public"."payment_plan" to "authenticated";

grant insert on table "public"."payment_plan" to "authenticated";

grant references on table "public"."payment_plan" to "authenticated";

grant select on table "public"."payment_plan" to "authenticated";

grant trigger on table "public"."payment_plan" to "authenticated";

grant truncate on table "public"."payment_plan" to "authenticated";

grant update on table "public"."payment_plan" to "authenticated";

grant delete on table "public"."payment_plan" to "service_role";

grant insert on table "public"."payment_plan" to "service_role";

grant references on table "public"."payment_plan" to "service_role";

grant select on table "public"."payment_plan" to "service_role";

grant trigger on table "public"."payment_plan" to "service_role";

grant truncate on table "public"."payment_plan" to "service_role";

grant update on table "public"."payment_plan" to "service_role";

grant delete on table "public"."payment_plan_notification" to "anon";

grant insert on table "public"."payment_plan_notification" to "anon";

grant references on table "public"."payment_plan_notification" to "anon";

grant select on table "public"."payment_plan_notification" to "anon";

grant trigger on table "public"."payment_plan_notification" to "anon";

grant truncate on table "public"."payment_plan_notification" to "anon";

grant update on table "public"."payment_plan_notification" to "anon";

grant delete on table "public"."payment_plan_notification" to "authenticated";

grant insert on table "public"."payment_plan_notification" to "authenticated";

grant references on table "public"."payment_plan_notification" to "authenticated";

grant select on table "public"."payment_plan_notification" to "authenticated";

grant trigger on table "public"."payment_plan_notification" to "authenticated";

grant truncate on table "public"."payment_plan_notification" to "authenticated";

grant update on table "public"."payment_plan_notification" to "authenticated";

grant delete on table "public"."payment_plan_notification" to "service_role";

grant insert on table "public"."payment_plan_notification" to "service_role";

grant references on table "public"."payment_plan_notification" to "service_role";

grant select on table "public"."payment_plan_notification" to "service_role";

grant trigger on table "public"."payment_plan_notification" to "service_role";

grant truncate on table "public"."payment_plan_notification" to "service_role";

grant update on table "public"."payment_plan_notification" to "service_role";

grant delete on table "public"."pkg" to "anon";

grant insert on table "public"."pkg" to "anon";

grant references on table "public"."pkg" to "anon";

grant select on table "public"."pkg" to "anon";

grant trigger on table "public"."pkg" to "anon";

grant truncate on table "public"."pkg" to "anon";

grant update on table "public"."pkg" to "anon";

grant delete on table "public"."pkg" to "authenticated";

grant insert on table "public"."pkg" to "authenticated";

grant references on table "public"."pkg" to "authenticated";

grant select on table "public"."pkg" to "authenticated";

grant trigger on table "public"."pkg" to "authenticated";

grant truncate on table "public"."pkg" to "authenticated";

grant update on table "public"."pkg" to "authenticated";

grant delete on table "public"."pkg" to "service_role";

grant insert on table "public"."pkg" to "service_role";

grant references on table "public"."pkg" to "service_role";

grant select on table "public"."pkg" to "service_role";

grant trigger on table "public"."pkg" to "service_role";

grant truncate on table "public"."pkg" to "service_role";

grant update on table "public"."pkg" to "service_role";

grant delete on table "public"."pkg_profile" to "anon";

grant insert on table "public"."pkg_profile" to "anon";

grant references on table "public"."pkg_profile" to "anon";

grant select on table "public"."pkg_profile" to "anon";

grant trigger on table "public"."pkg_profile" to "anon";

grant truncate on table "public"."pkg_profile" to "anon";

grant update on table "public"."pkg_profile" to "anon";

grant delete on table "public"."pkg_profile" to "authenticated";

grant insert on table "public"."pkg_profile" to "authenticated";

grant references on table "public"."pkg_profile" to "authenticated";

grant select on table "public"."pkg_profile" to "authenticated";

grant trigger on table "public"."pkg_profile" to "authenticated";

grant truncate on table "public"."pkg_profile" to "authenticated";

grant update on table "public"."pkg_profile" to "authenticated";

grant delete on table "public"."pkg_profile" to "service_role";

grant insert on table "public"."pkg_profile" to "service_role";

grant references on table "public"."pkg_profile" to "service_role";

grant select on table "public"."pkg_profile" to "service_role";

grant trigger on table "public"."pkg_profile" to "service_role";

grant truncate on table "public"."pkg_profile" to "service_role";

grant update on table "public"."pkg_profile" to "service_role";

CREATE TRIGGER payment_pkg AFTER INSERT OR UPDATE ON public.payment_pkg FOR EACH STATEMENT EXECUTE FUNCTION new_payment_pkg();

CREATE TRIGGER payment_pkg_notification AFTER INSERT OR UPDATE ON public.payment_pkg_notification FOR EACH STATEMENT EXECUTE FUNCTION new_payment_pkg_notification();

CREATE TRIGGER payment_plan AFTER INSERT OR UPDATE ON public.payment_plan FOR EACH STATEMENT EXECUTE FUNCTION new_payment_plan();

CREATE TRIGGER payment_plan_notification AFTER INSERT OR UPDATE ON public.payment_plan_notification FOR EACH STATEMENT EXECUTE FUNCTION new_payment_plan_notification();


