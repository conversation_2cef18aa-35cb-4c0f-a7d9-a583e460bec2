drop trigger if exists "new_subscription" on "public"."subscription";

alter table "public"."booking_equipment" drop constraint "booking_equipment_bookingId_fkey";

alter table "public"."booking_equipment" drop constraint "booking_equipment_equipmentId_fkey";

alter table "public"."booking_equipment" add constraint "booking_equipment_bookingId_fkey" FOREIGN KEY ("bookingId") REFERENCES booking(id) ON DELETE CASCADE not valid;

alter table "public"."booking_equipment" validate constraint "booking_equipment_bookingId_fkey";

alter table "public"."booking_equipment" add constraint "booking_equipment_equipmentId_fkey" FOREIGN KEY ("equipmentId") REFERENCES equipment(id) ON DELETE CASCADE not valid;

alter table "public"."booking_equipment" validate constraint "booking_equipment_equipmentId_fkey";

set check_function_bodies = off;
