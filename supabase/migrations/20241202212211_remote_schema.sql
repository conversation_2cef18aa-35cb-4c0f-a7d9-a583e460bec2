drop trigger if exists "payment_pkg_notification" on "public"."payment_pkg_notification";

drop trigger if exists "payment_pkg" on "public"."payment_pkg";

drop trigger if exists "payment_plan" on "public"."payment_plan";

drop trigger if exists "payment_plan_notification" on "public"."payment_plan_notification";

alter table "public"."payment_pkg" drop constraint "payment_pkg_pkg_fkey";

alter table "public"."payment_pkg" drop column "pkg";

alter table "public"."payment_pkg" add column "pkgId" uuid default gen_random_uuid();

alter table "public"."pkg_profile" alter column "active" set not null;

alter table "public"."subscription" alter column "externalReference" set data type character varying using "externalReference"::character varying;

CREATE UNIQUE INDEX unique_pkg_profile_active_idx ON public.pkg_profile USING btree ("profileId") WHERE (active = true);

alter table "public"."payment_pkg" add constraint "payment_pkg_pkg_fkey" FOREIGN KEY ("pkgId") REFERENCES pkg(id) not valid;

alter table "public"."payment_pkg" validate constraint "payment_pkg_pkg_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.new_subscription()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    -- SELECT content::json
    -- INTO response
    -- FROM http((
    --     'GET',
    --     format(
    --         'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_subscription'
    --     ),
    --     ARRAY[http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'),
    --     http_header('Content-Type', 'application/json')
    --     ],
    --     '{"name":"Functions"}',
    --     NULL
    -- )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;

CREATE TRIGGER payment_pkg_notification_trigger AFTER INSERT OR UPDATE ON public.payment_pkg_notification FOR EACH ROW EXECUTE FUNCTION new_payment_pkg_notification();

CREATE TRIGGER new_subscription AFTER INSERT OR UPDATE ON public.subscription FOR EACH ROW EXECUTE FUNCTION supabase_functions.http_request('https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_subscription', 'POST', '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg"}', '{}', '5000');

CREATE TRIGGER payment_pkg AFTER INSERT OR UPDATE ON public.payment_pkg FOR EACH ROW EXECUTE FUNCTION new_payment_pkg();

CREATE TRIGGER payment_plan AFTER INSERT OR UPDATE ON public.payment_plan FOR EACH ROW EXECUTE FUNCTION new_payment_plan();

CREATE TRIGGER payment_plan_notification AFTER INSERT OR UPDATE ON public.payment_plan_notification FOR EACH ROW EXECUTE FUNCTION new_payment_plan_notification();


