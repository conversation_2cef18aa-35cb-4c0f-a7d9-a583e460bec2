CREATE OR REPLACE FUNCTION public.insert_teacher_schedules()
  RETURNS void
  LANGUAGE plpgsql
AS $function$
BEGIN
  -- CTE to calculate teacher schedules
  WITH teacher_schedules AS (
    SELECT
      sch.id AS "scheduleId",
      p.id AS "profileId",
      sch."serviceId",
      (CURRENT_DATE - (EXTRACT(DOW FROM CURRENT_DATE) * interval '1 day') + (sch.number * interval '1 day') + interval '7 days')::date AS "day"
    FROM
      public.schedule sch
      JOIN public.service_teacher st ON sch."serviceId" = st."serviceId"
      JOIN public.profile p ON st."profileId" = p.id
    WHERE
      p.type = 'teacher' and p.status = 'active'
  )
  -- Insert the results into the teacher_schedule table
  INSERT INTO public.teacher_schedule ("scheduleId", "profileId", "day", "serviceId")
  SELECT DISTINCT
    "scheduleId",
    "profileId",
    "day",
    "serviceId"
  FROM
    teacher_schedules;
END;
$function$
;