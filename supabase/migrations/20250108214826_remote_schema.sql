alter table "public"."debt_category" drop constraint "debt_category_debtId_fkey";

alter table "public"."debt" drop column "billingDay";

alter table "public"."debt" drop column "isFixed";

alter table "public"."debt" drop column "repetitions";

alter table "public"."debt" add column "debtRootId" uuid;

alter table "public"."debt_category" drop column "debtId";

alter table "public"."debt_category" add column "debtRootId" uuid;

alter table "public"."debt" add constraint "debt_debtRootId_fkey" FOREIGN KEY ("debtRootId") REFERENCES debt_root(id) ON DELETE CASCADE not valid;

alter table "public"."debt" validate constraint "debt_debtRootId_fkey";

alter table "public"."debt_category" add constraint "debt_category_debtRootId_fkey" FOREIGN KEY ("debtRootId") REFERENCES debt_root(id) ON DELETE CASCADE not valid;

alter table "public"."debt_category" validate constraint "debt_category_debtRootId_fkey";


