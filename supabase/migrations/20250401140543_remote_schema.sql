CREATE OR REPLACE FUNCTION public.get_available_schedules("selectedDay" date, "selectedServiceId" uuid)
  RETURNS TABLE(id uuid, schedule jsonb, profile jsonb, day date)
  LANGUAGE plpgsql
AS $function$BEGIN
    RETURN QUERY
SELECT
    ts.id,
    (SELECT to_jsonb(s) FROM public.schedule s WHERE s.id = ts."scheduleId"),
    (SELECT to_jsonb(p) FROM public.profile p WHERE p.id = ts."profileId"),
    ts.day
FROM
    public.teacher_schedule ts
JOIN
    public.profile p ON p.id = ts."profileId"
WHERE
    NOT EXISTS (
        SELECT 1
        FROM public.booking b
        WHERE b."teacherScheduleId" = ts.id
          AND b.status NOT IN ('canceled', 'bailed')
    )
    AND ts.day = "selectedDay"  -- Filter by day
    AND ts."serviceId" = "selectedServiceId"  -- Filter by
    AND NOT EXISTS (
        SELECT 1
        FROM public.school_settings ss
        WHERE ss."schoolId" = p."schoolId"  -- Match the teacher's school
          AND ts.day = ANY(ss.closed)
    )
ORDER BY
    (SELECT hour FROM public.schedule s WHERE s.id = ts."scheduleId");
END;$function$
;