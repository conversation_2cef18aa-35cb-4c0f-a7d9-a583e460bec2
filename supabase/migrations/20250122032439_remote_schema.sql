create table "public"."sport_profile" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "sportId" uuid not null,
    "profileId" uuid not null
);


alter table "public"."sport_profile" enable row level security;

CREATE UNIQUE INDEX sport_profile_pkey ON public.sport_profile USING btree (id);

alter table "public"."sport_profile" add constraint "sport_profile_pkey" PRIMARY KEY using index "sport_profile_pkey";

alter table "public"."sport_profile" add constraint "sport_profile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."sport_profile" validate constraint "sport_profile_profileId_fkey";

alter table "public"."sport_profile" add constraint "sport_profile_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES sport(id) ON DELETE CASCADE not valid;

alter table "public"."sport_profile" validate constraint "sport_profile_sportId_fkey";

grant delete on table "public"."sport_profile" to "anon";

grant insert on table "public"."sport_profile" to "anon";

grant references on table "public"."sport_profile" to "anon";

grant select on table "public"."sport_profile" to "anon";

grant trigger on table "public"."sport_profile" to "anon";

grant truncate on table "public"."sport_profile" to "anon";

grant update on table "public"."sport_profile" to "anon";

grant delete on table "public"."sport_profile" to "authenticated";

grant insert on table "public"."sport_profile" to "authenticated";

grant references on table "public"."sport_profile" to "authenticated";

grant select on table "public"."sport_profile" to "authenticated";

grant trigger on table "public"."sport_profile" to "authenticated";

grant truncate on table "public"."sport_profile" to "authenticated";

grant update on table "public"."sport_profile" to "authenticated";

grant delete on table "public"."sport_profile" to "service_role";

grant insert on table "public"."sport_profile" to "service_role";

grant references on table "public"."sport_profile" to "service_role";

grant select on table "public"."sport_profile" to "service_role";

grant trigger on table "public"."sport_profile" to "service_role";

grant truncate on table "public"."sport_profile" to "service_role";

grant update on table "public"."sport_profile" to "service_role";
