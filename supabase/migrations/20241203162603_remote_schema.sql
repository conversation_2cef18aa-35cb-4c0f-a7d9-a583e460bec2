alter table "public"."booking" drop constraint "booking_planId_fkey";

drop view if exists "public"."bookings_count";

alter table "public"."booking" drop column "planId";

alter table "public"."booking" alter column "cancelable" set default true;

alter table "public"."booking" alter column "status" set not null;

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.mark_expired_bookings()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
  expired_booking RECORD;
BEGIN
  FOR expired_booking IN
    SELECT *
    FROM public.booking b
    JOIN public.schedule s ON b."scheduleId" = s.id
    WHERE b.day + s.hour::INTERVAL < NOW()
  LOOP
    UPDATE public.booking
    SET status = 'expired'
    WHERE id = expired_booking.id;
  END LOOP;
END;
$function$
;

create or replace view "public"."bookings_count" as  SELECT b."userId",
    b."pkgId",
    count(*) AS total,
    sum(
        CASE
            WHEN (b.status = 'approved'::booking_status) THEN 1
            ELSE 0
        END) AS approved,
    sum(
        CASE
            WHEN (b.status = 'canceled'::booking_status) THEN 1
            ELSE 0
        END) AS canceled,
    sum(
        CASE
            WHEN (b.status = 'expired'::booking_status) THEN 1
            ELSE 0
        END) AS expired,
    sum(
        CASE
            WHEN (b.status = 'pending'::booking_status) THEN 1
            ELSE 0
        END) AS pending,
    sum(
        CASE
            WHEN (b.status = 'used'::booking_status) THEN 1
            ELSE 0
        END) AS used,
    sum(
        CASE
            WHEN (b.status = 'missed'::booking_status) THEN 1
            ELSE 0
        END) AS missed,
    sum(
        CASE
            WHEN (b.status = 'bailed'::booking_status) THEN 1
            ELSE 0
        END) AS bailed
   FROM booking b
  GROUP BY b."userId", b."pkgId";



