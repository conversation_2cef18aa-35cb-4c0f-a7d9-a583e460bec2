revoke delete on table "public"."payment_created" from "anon";

revoke insert on table "public"."payment_created" from "anon";

revoke references on table "public"."payment_created" from "anon";

revoke select on table "public"."payment_created" from "anon";

revoke trigger on table "public"."payment_created" from "anon";

revoke truncate on table "public"."payment_created" from "anon";

revoke update on table "public"."payment_created" from "anon";

revoke delete on table "public"."payment_created" from "authenticated";

revoke insert on table "public"."payment_created" from "authenticated";

revoke references on table "public"."payment_created" from "authenticated";

revoke select on table "public"."payment_created" from "authenticated";

revoke trigger on table "public"."payment_created" from "authenticated";

revoke truncate on table "public"."payment_created" from "authenticated";

revoke update on table "public"."payment_created" from "authenticated";

revoke delete on table "public"."payment_created" from "service_role";

revoke insert on table "public"."payment_created" from "service_role";

revoke references on table "public"."payment_created" from "service_role";

revoke select on table "public"."payment_created" from "service_role";

revoke trigger on table "public"."payment_created" from "service_role";

revoke truncate on table "public"."payment_created" from "service_role";

revoke update on table "public"."payment_created" from "service_role";

alter table "public"."payment_created" drop constraint "payment_created_notificationId_fkey";

alter table "public"."payment_created" drop constraint "payment_created_pkey";

drop index if exists "public"."payment_created_pkey";

drop table "public"."payment_created";


