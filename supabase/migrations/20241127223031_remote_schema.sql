create table "public"."payment_profile" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "profileId" uuid default gen_random_uuid(),
    "paymentId" character varying
);


alter table "public"."payment_profile" enable row level security;

CREATE UNIQUE INDEX payment_profile_pkey ON public.payment_profile USING btree (id);

alter table "public"."payment_profile" add constraint "payment_profile_pkey" PRIMARY KEY using index "payment_profile_pkey";

alter table "public"."payment_profile" add constraint "payment_profile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) not valid;

alter table "public"."payment_profile" validate constraint "payment_profile_profileId_fkey";

grant delete on table "public"."payment_profile" to "anon";

grant insert on table "public"."payment_profile" to "anon";

grant references on table "public"."payment_profile" to "anon";

grant select on table "public"."payment_profile" to "anon";

grant trigger on table "public"."payment_profile" to "anon";

grant truncate on table "public"."payment_profile" to "anon";

grant update on table "public"."payment_profile" to "anon";

grant delete on table "public"."payment_profile" to "authenticated";

grant insert on table "public"."payment_profile" to "authenticated";

grant references on table "public"."payment_profile" to "authenticated";

grant select on table "public"."payment_profile" to "authenticated";

grant trigger on table "public"."payment_profile" to "authenticated";

grant truncate on table "public"."payment_profile" to "authenticated";

grant update on table "public"."payment_profile" to "authenticated";

grant delete on table "public"."payment_profile" to "service_role";

grant insert on table "public"."payment_profile" to "service_role";

grant references on table "public"."payment_profile" to "service_role";

grant select on table "public"."payment_profile" to "service_role";

grant trigger on table "public"."payment_profile" to "service_role";

grant truncate on table "public"."payment_profile" to "service_role";

grant update on table "public"."payment_profile" to "service_role";


