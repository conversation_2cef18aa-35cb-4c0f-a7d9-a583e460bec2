create extension if not exists "pgaudit" with schema "extensions";


create type "public"."payment_status" as enum ('pending', 'confirmed', 'overdue');

create type "public"."status_type" as enum ('pending', 'active', 'inactive');

drop trigger if exists "payment_pkg" on "public"."payment_pkg";

alter table "public"."profile" alter column "type" drop default;

alter type "public"."app_role" rename to "app_role__old_version_to_be_dropped";

create type "public"."app_role" as enum ('admin', 'student', 'master');

alter type "public"."user_type" rename to "user_type__old_version_to_be_dropped";

create type "public"."user_type" as enum ('admin', 'student', 'teacher', 'master');

create table "public"."school_settings" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "closed" date[],
    "schoolId" uuid not null
);


alter table "public"."school_settings" enable row level security;

alter table "public"."profile" alter column type type "public"."user_type" using type::text::"public"."user_type";

alter table "public"."user_roles" alter column role type "public"."app_role" using role::text::"public"."app_role";

alter table "public"."profile" alter column "type" set default 'student'::user_type;

drop type "public"."app_role__old_version_to_be_dropped";

drop type "public"."user_type__old_version_to_be_dropped";

alter table "public"."pkg_profile" add column "status" payment_status not null default 'pending'::payment_status;

alter table "public"."plan" add column "billingDay" smallint;

alter table "public"."plan" add column "new_id" uuid default gen_random_uuid();

alter table "public"."plan" alter column "active" set default true;

alter table "public"."profile" add column "asId" character varying;

alter table "public"."profile" add column "status" status_type not null default 'pending'::status_type;

alter table "public"."subscription_asaas" drop column "externalReference";

alter table "public"."subscription_asaas" add column "cardBrand" character varying;

alter table "public"."subscription_asaas" add column "cardNumber" character varying;

alter table "public"."subscription_asaas" add column "planId" character varying;

CREATE UNIQUE INDEX pkg_profile_profileid_pkgid_active_unique ON public.pkg_profile USING btree ("profileId", "pkgId", active);

CREATE UNIQUE INDEX school_settings_pkey ON public.school_settings USING btree (id, "schoolId");

CREATE UNIQUE INDEX school_settings_school_id_key ON public.school_settings USING btree ("schoolId");

alter table "public"."school_settings" add constraint "school_settings_pkey" PRIMARY KEY using index "school_settings_pkey";

alter table "public"."pkg_profile" add constraint "pkg_profile_profileid_pkgid_active_unique" UNIQUE using index "pkg_profile_profileid_pkgid_active_unique";

alter table "public"."school_settings" add constraint "school_settings_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."school_settings" validate constraint "school_settings_schoolId_fkey";

alter table "public"."school_settings" add constraint "school_settings_school_id_key" UNIQUE using index "school_settings_school_id_key";

alter table "public"."subscription_asaas" add constraint "subscription_asaas_externalReference_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) ON DELETE CASCADE not valid;

alter table "public"."subscription_asaas" validate constraint "subscription_asaas_externalReference_fkey";

grant delete on table "public"."school_settings" to "anon";

grant insert on table "public"."school_settings" to "anon";

grant references on table "public"."school_settings" to "anon";

grant select on table "public"."school_settings" to "anon";

grant trigger on table "public"."school_settings" to "anon";

grant truncate on table "public"."school_settings" to "anon";

grant update on table "public"."school_settings" to "anon";

grant delete on table "public"."school_settings" to "authenticated";

grant insert on table "public"."school_settings" to "authenticated";

grant references on table "public"."school_settings" to "authenticated";

grant select on table "public"."school_settings" to "authenticated";

grant trigger on table "public"."school_settings" to "authenticated";

grant truncate on table "public"."school_settings" to "authenticated";

grant update on table "public"."school_settings" to "authenticated";

grant delete on table "public"."school_settings" to "service_role";

grant insert on table "public"."school_settings" to "service_role";

grant references on table "public"."school_settings" to "service_role";

grant select on table "public"."school_settings" to "service_role";

grant trigger on table "public"."school_settings" to "service_role";

grant truncate on table "public"."school_settings" to "service_role";

grant update on table "public"."school_settings" to "service_role";

