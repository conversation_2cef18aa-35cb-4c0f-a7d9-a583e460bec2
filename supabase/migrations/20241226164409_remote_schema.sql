alter table "public"."plan_profile" alter column "planId" set data type uuid using "planId"::uuid;

alter table "public"."plan_profile" alter column "profileId" drop default;

alter table "public"."subscription_asaas" alter column "planId" set data type uuid using "planId"::uuid;

alter table "public"."plan_profile" add constraint "plan_profile_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) not valid;

alter table "public"."plan_profile" validate constraint "plan_profile_planId_fkey";

alter table "public"."subscription_asaas" add constraint "subscription_asaas_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) not valid;

alter table "public"."subscription_asaas" validate constraint "subscription_asaas_planId_fkey";


