create table "public"."teacher_schedule" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "scheduleId" uuid not null,
    "profileId" uuid not null,
    "day" date not null
);


alter table "public"."teacher_schedule" enable row level security;

CREATE UNIQUE INDEX teacher_schedule_pkey ON public.teacher_schedule USING btree (id);

alter table "public"."teacher_schedule" add constraint "teacher_schedule_pkey" PRIMARY KEY using index "teacher_schedule_pkey";

alter table "public"."teacher_schedule" add constraint "teacher_schedule_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."teacher_schedule" validate constraint "teacher_schedule_profileId_fkey";

alter table "public"."teacher_schedule" add constraint "teacher_schedule_scheduleId_fkey" FOREIGN KEY ("scheduleId") REFERENCES schedule(id) ON DELETE CASCADE not valid;

alter table "public"."teacher_schedule" validate constraint "teacher_schedule_scheduleId_fkey";

grant delete on table "public"."teacher_schedule" to "anon";

grant insert on table "public"."teacher_schedule" to "anon";

grant references on table "public"."teacher_schedule" to "anon";

grant select on table "public"."teacher_schedule" to "anon";

grant trigger on table "public"."teacher_schedule" to "anon";

grant truncate on table "public"."teacher_schedule" to "anon";

grant update on table "public"."teacher_schedule" to "anon";

grant delete on table "public"."teacher_schedule" to "authenticated";

grant insert on table "public"."teacher_schedule" to "authenticated";

grant references on table "public"."teacher_schedule" to "authenticated";

grant select on table "public"."teacher_schedule" to "authenticated";

grant trigger on table "public"."teacher_schedule" to "authenticated";

grant truncate on table "public"."teacher_schedule" to "authenticated";

grant update on table "public"."teacher_schedule" to "authenticated";

grant delete on table "public"."teacher_schedule" to "service_role";

grant insert on table "public"."teacher_schedule" to "service_role";

grant references on table "public"."teacher_schedule" to "service_role";

grant select on table "public"."teacher_schedule" to "service_role";

grant trigger on table "public"."teacher_schedule" to "service_role";

grant truncate on table "public"."teacher_schedule" to "service_role";

grant update on table "public"."teacher_schedule" to "service_role";


