alter table "public"."debt_root" drop constraint "debt_root_pkey";

drop index if exists "public"."debt_root_pkey";

alter table "public"."debt_root" drop column "newId";

alter table "public"."debt_root" add column "id" uuid not null default gen_random_uuid();

CREATE UNIQUE INDEX debt_root_pkey ON public.debt_root USING btree (id);

alter table "public"."debt_root" add constraint "debt_root_pkey" PRIMARY KEY using index "debt_root_pkey";
