alter table "public"."booking" drop constraint "booking_service_fk";

alter table "public"."pack_service" drop constraint "pack_service_serviceId_fkey";

drop function if exists "public"."verify_pkg_expiration"();

drop view if exists "public"."attendance_monthly";

drop view if exists "public"."attendance_yearly";

alter table "public"."profile" drop column "mpId";

alter table "public"."subscription" add column "billingDay" smallint;

alter table "public"."booking" add constraint "booking_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) ON DELETE CASCADE not valid;

alter table "public"."booking" validate constraint "booking_serviceId_fkey";

alter table "public"."pack_service" add constraint "pack_service_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) ON DELETE CASCADE not valid;

alter table "public"."pack_service" validate constraint "pack_service_serviceId_fkey";
