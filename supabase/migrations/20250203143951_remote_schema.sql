create type "public"."pack_profile_status" as enum ('active', 'used', 'expired', 'pending');

alter table "public"."pack_profile" drop constraint if exists "pkg_profile_pkgId_fkey";

alter table "public"."pack_profile" drop constraint if exists "pkg_profile_profileid_pkgid_active_unique";

drop index if exists "public"."pkg_profile_profileid_pkgid_active_unique";

alter type "public"."payment_method" rename to "payment_method__old_version_to_be_dropped";

create type "public"."payment_method" as enum ('CREDIT_CARD', 'PIX', 'BOLETO');

alter table "public"."debt" alter column method type "public"."payment_method" using method::text::"public"."payment_method";

alter table "public"."debt_root" alter column method type "public"."payment_method" using method::text::"public"."payment_method";

alter table "public"."sale" alter column method type "public"."payment_method" using method::text::"public"."payment_method";

alter table "public"."sale_root" alter column method type "public"."payment_method" using method::text::"public"."payment_method";
