create table "public"."base_rank" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "name" character varying
);

alter table "public"."base_rank" enable row level security;

CREATE UNIQUE INDEX base_rank_pkey ON public.base_rank USING btree (id);

alter table "public"."base_rank" add constraint "base_rank_pkey" PRIMARY KEY using index "base_rank_pkey";

grant delete on table "public"."base_rank" to "anon";

grant insert on table "public"."base_rank" to "anon";

grant references on table "public"."base_rank" to "anon";

grant select on table "public"."base_rank" to "anon";

grant trigger on table "public"."base_rank" to "anon";

grant truncate on table "public"."base_rank" to "anon";

grant update on table "public"."base_rank" to "anon";

grant delete on table "public"."base_rank" to "authenticated";

grant insert on table "public"."base_rank" to "authenticated";

grant references on table "public"."base_rank" to "authenticated";

grant select on table "public"."base_rank" to "authenticated";

grant trigger on table "public"."base_rank" to "authenticated";

grant truncate on table "public"."base_rank" to "authenticated";

grant update on table "public"."base_rank" to "authenticated";

grant delete on table "public"."base_rank" to "service_role";

grant insert on table "public"."base_rank" to "service_role";

grant references on table "public"."base_rank" to "service_role";

grant select on table "public"."base_rank" to "service_role";

grant trigger on table "public"."base_rank" to "service_role";

grant truncate on table "public"."base_rank" to "service_role";

grant update on table "public"."base_rank" to "service_role";

