drop trigger if exists "payment" on "public"."payment";

alter table "public"."booking" alter column "status" drop default;

alter type "public"."booking_status" rename to "booking_status__old_version_to_be_dropped";

create type "public"."booking_status" as enum ('pending', 'approved', 'canceled', 'expired', 'used', 'missed', 'bailed');

alter table "public"."booking" alter column status type "public"."booking_status" using status::text::"public"."booking_status";

alter table "public"."booking" alter column "status" set default 'pending'::booking_status;

drop type "public"."booking_status__old_version_to_be_dropped";

alter table "public"."payment_notification" drop column "entity";

alter table "public"."payment_notification" drop column "planId";

alter table "public"."payment_notification" drop column "storeId";

create or replace view "public"."booking_counts" as  SELECT b."userId",
    b."planId",
    count(*) AS total,
    sum(
        CASE
            WHEN (b.status = 'approved'::booking_status) THEN 1
            ELSE 0
        END) AS approved,
    sum(
        CASE
            WHEN (b.status = 'canceled'::booking_status) THEN 1
            ELSE 0
        END) AS canceled,
    sum(
        CASE
            WHEN (b.status = 'expired'::booking_status) THEN 1
            ELSE 0
        END) AS expired,
    sum(
        CASE
            WHEN (b.status = 'pending'::booking_status) THEN 1
            ELSE 0
        END) AS pending,
    sum(
        CASE
            WHEN (b.status = 'used'::booking_status) THEN 1
            ELSE 0
        END) AS used,
    sum(
        CASE
            WHEN (b.status = 'missed'::booking_status) THEN 1
            ELSE 0
        END) AS missed,
    sum(
        CASE
            WHEN (b.status = 'bailed'::booking_status) THEN 1
            ELSE 0
        END) AS bailed
   FROM booking b
  GROUP BY b."userId", b."planId";


CREATE TRIGGER payment AFTER INSERT OR UPDATE ON public.payment FOR EACH ROW EXECUTE FUNCTION new_payment();


