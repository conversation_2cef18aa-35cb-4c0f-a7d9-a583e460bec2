create table "public"."pack_service" (
    "id" bigint generated by default as identity not null,
    "createdAt" timestamp with time zone not null default now(),
    "packId" uuid not null,
    "serviceId" uuid not null
);


alter table "public"."pack_service" enable row level security;

CREATE UNIQUE INDEX pack_service_pkey ON public.pack_service USING btree (id);

alter table "public"."pack_service" add constraint "pack_service_pkey" PRIMARY KEY using index "pack_service_pkey";

alter table "public"."pack_service" add constraint "pack_service_packId_fkey" FOREIGN KEY ("packId") REFERENCES pkg(id) not valid;

alter table "public"."pack_service" validate constraint "pack_service_packId_fkey";

alter table "public"."pack_service" add constraint "pack_service_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) not valid;

alter table "public"."pack_service" validate constraint "pack_service_serviceId_fkey";

grant delete on table "public"."pack_service" to "anon";

grant insert on table "public"."pack_service" to "anon";

grant references on table "public"."pack_service" to "anon";

grant select on table "public"."pack_service" to "anon";

grant trigger on table "public"."pack_service" to "anon";

grant truncate on table "public"."pack_service" to "anon";

grant update on table "public"."pack_service" to "anon";

grant delete on table "public"."pack_service" to "authenticated";

grant insert on table "public"."pack_service" to "authenticated";

grant references on table "public"."pack_service" to "authenticated";

grant select on table "public"."pack_service" to "authenticated";

grant trigger on table "public"."pack_service" to "authenticated";

grant truncate on table "public"."pack_service" to "authenticated";

grant update on table "public"."pack_service" to "authenticated";

grant delete on table "public"."pack_service" to "service_role";

grant insert on table "public"."pack_service" to "service_role";

grant references on table "public"."pack_service" to "service_role";

grant select on table "public"."pack_service" to "service_role";

grant trigger on table "public"."pack_service" to "service_role";

grant truncate on table "public"."pack_service" to "service_role";

grant update on table "public"."pack_service" to "service_role";


