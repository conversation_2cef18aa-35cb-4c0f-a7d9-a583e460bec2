

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "extensions";



COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."app_role" AS ENUM (
    'admin',
    'student'
);


ALTER TYPE "public"."app_role" OWNER TO "postgres";


CREATE TYPE "public"."booking_status" AS ENUM (
    'pending',
    'approved',
    'canceled',
    'expired'
);


ALTER TYPE "public"."booking_status" OWNER TO "postgres";


CREATE TYPE "public"."equipment_status" AS ENUM (
    'available',
    'unavailable'
);


ALTER TYPE "public"."equipment_status" OWNER TO "postgres";


CREATE TYPE "public"."lesson_progress" AS ENUM (
    'complete',
    'in_progress',
    'not_started'
);


ALTER TYPE "public"."lesson_progress" OWNER TO "postgres";


COMMENT ON TYPE "public"."lesson_progress" IS 'Lesson progress status';



CREATE TYPE "public"."subscription_periodicity" AS ENUM (
    'monthly'
);


ALTER TYPE "public"."subscription_periodicity" OWNER TO "postgres";


COMMENT ON TYPE "public"."subscription_periodicity" IS 'De quanto em quanto tempo a assinatura deve ser cobrada';



CREATE TYPE "public"."subscription_type" AS ENUM (
    'free',
    'payed'
);


ALTER TYPE "public"."subscription_type" OWNER TO "postgres";


CREATE TYPE "public"."user_type" AS ENUM (
    'admin',
    'student',
    'instructor'
);


ALTER TYPE "public"."user_type" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."custom_access_token_hook"("event" "jsonb") RETURNS "jsonb"
    LANGUAGE "plpgsql" STABLE
    AS $$
  declare
    claims jsonb;
    user_role public.app_role;
  begin
    select role into user_role from public.user_roles where user_id = (event->>'user_id')::uuid;

    claims := event->'claims';

    if user_role is not null then
      claims := jsonb_set(claims, '{user_role}', to_jsonb(user_role));
    else
      claims := jsonb_set(claims, '{user_role}', 'null');
    end if;

    event := jsonb_set(event, '{claims}', claims);

    return event;
  end;
$$;


ALTER FUNCTION "public"."custom_access_token_hook"("event" "jsonb") OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."attendance" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "date" "date" NOT NULL,
    "userId" "uuid" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL
);


ALTER TABLE "public"."attendance" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."profile" (
    "id" "uuid" DEFAULT "auth"."uid"() NOT NULL,
    "email" character varying(100) NOT NULL,
    "name" character varying(100) NOT NULL,
    "schoolId" "uuid" NOT NULL,
    "type" "public"."user_type" DEFAULT 'student'::"public"."user_type" NOT NULL,
    "birthdate" "date",
    "phone" character varying(100),
    "street" character varying(100),
    "neighborhood" character varying(100),
    "city" character varying(100),
    "state" character varying(100),
    "cep" character varying(100),
    "faceImage" character varying,
    "profileImage" character varying,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL
);


ALTER TABLE "public"."profile" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."attendance_monthly" AS
 SELECT "a"."userId",
    "u"."name",
    EXTRACT(year FROM "a"."date") AS "year",
    EXTRACT(month FROM "a"."date") AS "month",
    "count"(*) AS "total"
   FROM ("public"."attendance" "a"
     JOIN "public"."profile" "u" ON (("a"."userId" = "u"."id")))
  GROUP BY "a"."userId", "u"."name", (EXTRACT(year FROM "a"."date")), (EXTRACT(month FROM "a"."date"))
  ORDER BY ("count"(*)) DESC;


ALTER TABLE "public"."attendance_monthly" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."attendance_yearly" AS
 SELECT "a"."userId",
    "u"."name",
    EXTRACT(year FROM "a"."date") AS "year",
    "count"(*) AS "total"
   FROM ("public"."attendance" "a"
     JOIN "public"."profile" "u" ON (("a"."userId" = "u"."id")))
  GROUP BY "a"."userId", "u"."name", (EXTRACT(year FROM "a"."date"))
  ORDER BY ("count"(*)) DESC;


ALTER TABLE "public"."attendance_yearly" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."authorized_payment" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "preapprovalId" character varying NOT NULL,
    "type" character varying NOT NULL,
    "status" character varying NOT NULL,
    "dateCreated" timestamp with time zone NOT NULL,
    "lastModified" timestamp with time zone NOT NULL,
    "transactionAmount" smallint NOT NULL,
    "currencyId" character varying,
    "reason" character varying,
    "paymentId" character varying NOT NULL,
    "retryAttempt" smallint,
    "nextRetryDate" timestamp with time zone,
    "debtDate" timestamp with time zone,
    "paymentMethodId" character varying NOT NULL,
    "authorizedPaymentId" character varying NOT NULL
);


ALTER TABLE "public"."authorized_payment" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."authorized_payment_notification" (
    "id" bigint NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "authorizationPaymentId" character varying,
    "applicationId" character varying NOT NULL,
    "action" character varying NOT NULL,
    "date" timestamp with time zone NOT NULL,
    "entity" character varying NOT NULL,
    "type" character varying NOT NULL,
    "version" smallint NOT NULL
);


ALTER TABLE "public"."authorized_payment_notification" OWNER TO "postgres";


ALTER TABLE "public"."authorized_payment_notification" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."authorized_payment_notification_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."booking" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "serviceId" "uuid" NOT NULL,
    "userId" "uuid" NOT NULL,
    "cancelable" boolean DEFAULT false,
    "status" "public"."booking_status" DEFAULT 'pending'::"public"."booking_status",
    "details" character varying(255),
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "scheduleId" "uuid" NOT NULL,
    "day" "date" NOT NULL,
    "code" "text" NOT NULL,
    CONSTRAINT "booking_code_check" CHECK (("char_length"("code") = 5))
);


ALTER TABLE "public"."booking" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."booking_equipment" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "bookingId" "uuid" NOT NULL,
    "equipmentId" "uuid" NOT NULL
);


ALTER TABLE "public"."booking_equipment" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."cancelation_policy" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "hour" "text" NOT NULL,
    "percentage" smallint NOT NULL,
    "serviceId" "uuid" DEFAULT "gen_random_uuid"() NOT NULL
);


ALTER TABLE "public"."cancelation_policy" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."course" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(100) NOT NULL,
    "description" character varying(255) NOT NULL,
    "instructorId" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "published" boolean DEFAULT false NOT NULL,
    "price" numeric,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "schoolId" "uuid" NOT NULL,
    "featureVideo" "uuid" NOT NULL,
    "slug" character varying(100) NOT NULL,
    "featureImg" character varying(100) NOT NULL
);


ALTER TABLE "public"."course" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."equipment" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(100) NOT NULL,
    "schoolId" "uuid" NOT NULL,
    "type" character varying(100),
    "status" "public"."equipment_status" DEFAULT 'available'::"public"."equipment_status" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "quantity" smallint NOT NULL
);


ALTER TABLE "public"."equipment" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."error" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "value" "text" NOT NULL
);


ALTER TABLE "public"."error" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."face" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "email" character varying(100) NOT NULL,
    "description" "text"[] NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL
);


ALTER TABLE "public"."face" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."lesson" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(100) NOT NULL,
    "description" character varying(255) NOT NULL,
    "slug" character varying(100) NOT NULL,
    "sequence" smallint NOT NULL,
    "courseId" "uuid" NOT NULL,
    "moduleId" "uuid" NOT NULL,
    "videoId" "uuid",
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL
);


ALTER TABLE "public"."lesson" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."module" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" character varying(100) NOT NULL,
    "description" character varying(255) NOT NULL,
    "courseId" "uuid" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL
);


ALTER TABLE "public"."module" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."mp_application" (
    "id" character varying NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "name" character varying NOT NULL
);


ALTER TABLE "public"."mp_application" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."payment" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT "now"() NOT NULL,
    "status" character varying NOT NULL,
    "statusDetails" character varying,
    "paymentId" character varying NOT NULL
);


ALTER TABLE "public"."payment" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."payment_notification" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "action" character varying,
    "date" timestamp without time zone,
    "entity" character varying,
    "type" character varying,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "paymentId" character varying NOT NULL,
    "storeId" character varying
);


ALTER TABLE "public"."payment_notification" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."plan" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "planId" character varying(100) NOT NULL,
    "name" character varying(100),
    "schoolId" "uuid",
    "price" double precision,
    "frequency" character varying(100)
);


ALTER TABLE "public"."plan" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."progress" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "courseId" "uuid",
    "lessonId" "uuid",
    "userId" "uuid" DEFAULT "gen_random_uuid"(),
    "status" "public"."lesson_progress",
    "lessonSequence" smallint
);


ALTER TABLE "public"."progress" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."schedule" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "serviceId" "uuid" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "hour" "text" NOT NULL,
    "limit" smallint,
    "number" smallint NOT NULL,
    "active" boolean DEFAULT true,
    CONSTRAINT "schedule_hour_check" CHECK (("char_length"("hour") = 5))
);


ALTER TABLE "public"."schedule" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."school" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "name" character varying(100) NOT NULL,
    "domain" character varying(100) NOT NULL
);


ALTER TABLE "public"."school" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."service" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "title" character varying(100) NOT NULL,
    "description" "text",
    "schoolId" "uuid" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "limit" integer,
    "cancelationPolicy" "text" NOT NULL
);


ALTER TABLE "public"."service" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."service_equipment" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "serviceId" "uuid" NOT NULL,
    "equipmentId" "uuid" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "active" boolean DEFAULT true NOT NULL
);


ALTER TABLE "public"."service_equipment" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."subscription" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL,
    "endsAt" "date",
    "active" boolean DEFAULT false NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "userId" "uuid" DEFAULT "auth"."uid"() NOT NULL,
    "planId" character varying,
    "cardId" character varying,
    "subscriptionId" character varying,
    "paymentMethodId" character varying,
    "invited" boolean DEFAULT false,
    "type" "public"."subscription_type" DEFAULT 'payed'::"public"."subscription_type" NOT NULL,
    "applicationid" "uuid",
    "billingday" integer,
    "billingdayproportional" boolean,
    "collectorid" "uuid",
    "reason" "text",
    "externalreference" integer,
    "backurl" "text",
    "initpoint" "text",
    "frequency" integer,
    "frequencytype" "text",
    "hasbillingdata" boolean,
    "startdate" "date",
    "payerid" "uuid",
    "nextpaymentdate" "date",
    "status" "text",
    "transactionamount" integer
);


ALTER TABLE "public"."subscription" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_roles" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "role" "public"."app_role" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL
);


ALTER TABLE "public"."user_roles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."video" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "publicPlaybackId" character varying(100) NOT NULL,
    "duration" double precision NOT NULL,
    "videoId" character varying(100) NOT NULL,
    "name" character varying(100) NOT NULL,
    "schoolId" "uuid" NOT NULL,
    "createdAt" timestamp with time zone DEFAULT ("now"() AT TIME ZONE 'utc'::"text") NOT NULL
);


ALTER TABLE "public"."video" OWNER TO "postgres";


ALTER TABLE ONLY "public"."attendance"
    ADD CONSTRAINT "attendance_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."authorized_payment_notification"
    ADD CONSTRAINT "authorized_payment_notification_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."authorized_payment"
    ADD CONSTRAINT "authorized_payment_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."booking_equipment"
    ADD CONSTRAINT "booking_equipment_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."booking_equipment"
    ADD CONSTRAINT "booking_id_equipment_id" UNIQUE ("bookingId", "equipmentId");



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "booking_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "booking_service_user" UNIQUE ("day", "scheduleId", "serviceId", "userId");



ALTER TABLE ONLY "public"."cancelation_policy"
    ADD CONSTRAINT "cancelation_policy_hour_key" UNIQUE ("hour");



ALTER TABLE ONLY "public"."cancelation_policy"
    ADD CONSTRAINT "cancelation_policy_percentage_key" UNIQUE ("percentage");



ALTER TABLE ONLY "public"."cancelation_policy"
    ADD CONSTRAINT "cancelation_policy_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "code_unique" UNIQUE ("code");



ALTER TABLE ONLY "public"."course"
    ADD CONSTRAINT "course_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."course"
    ADD CONSTRAINT "course_slug" UNIQUE ("slug", "schoolId");



ALTER TABLE ONLY "public"."face"
    ADD CONSTRAINT "email_unique" UNIQUE ("email");



ALTER TABLE ONLY "public"."equipment"
    ADD CONSTRAINT "equipment_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."error"
    ADD CONSTRAINT "error_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."face"
    ADD CONSTRAINT "face_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."lesson"
    ADD CONSTRAINT "lesson_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."lesson"
    ADD CONSTRAINT "lesson_slug" UNIQUE ("slug", "courseId");



ALTER TABLE ONLY "public"."module"
    ADD CONSTRAINT "module_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."mp_application"
    ADD CONSTRAINT "mp_application_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."mp_application"
    ADD CONSTRAINT "mp_application_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payment_notification"
    ADD CONSTRAINT "payment_notification_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payment"
    ADD CONSTRAINT "payment_paymentId_key" UNIQUE ("paymentId");



ALTER TABLE ONLY "public"."payment"
    ADD CONSTRAINT "payment_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."plan"
    ADD CONSTRAINT "planId_unique" UNIQUE ("planId");



ALTER TABLE ONLY "public"."plan"
    ADD CONSTRAINT "plan_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."progress"
    ADD CONSTRAINT "progress_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."schedule"
    ADD CONSTRAINT "schedule_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."school"
    ADD CONSTRAINT "school_domain_key" UNIQUE ("domain");



ALTER TABLE ONLY "public"."school"
    ADD CONSTRAINT "school_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."service_equipment"
    ADD CONSTRAINT "service_equipment_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."service"
    ADD CONSTRAINT "service_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."subscription"
    ADD CONSTRAINT "subscription_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."subscription"
    ADD CONSTRAINT "subscription_userId_key" UNIQUE ("userId");



ALTER TABLE ONLY "public"."attendance"
    ADD CONSTRAINT "user_date" UNIQUE ("userId", "date");



ALTER TABLE ONLY "public"."profile"
    ADD CONSTRAINT "user_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."profile"
    ADD CONSTRAINT "user_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_user_id_role_key" UNIQUE ("user_id", "role");



ALTER TABLE ONLY "public"."video"
    ADD CONSTRAINT "video_id_key" UNIQUE ("id");



ALTER TABLE ONLY "public"."video"
    ADD CONSTRAINT "video_pkey" PRIMARY KEY ("id");



CREATE UNIQUE INDEX "payment_paymentid_status_idx" ON "public"."payment" USING "btree" ("paymentId", "status");



ALTER TABLE ONLY "public"."attendance"
    ADD CONSTRAINT "attendance_user_fk" FOREIGN KEY ("userId") REFERENCES "public"."profile"("id");



ALTER TABLE ONLY "public"."authorized_payment_notification"
    ADD CONSTRAINT "authorized_payment_notification_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "public"."mp_application"("id");



ALTER TABLE ONLY "public"."authorized_payment"
    ADD CONSTRAINT "authorized_payment_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "public"."payment"("paymentId") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."authorized_payment"
    ADD CONSTRAINT "authorized_payment_paymentId_fkey1" FOREIGN KEY ("paymentId") REFERENCES "public"."payment"("paymentId");



ALTER TABLE ONLY "public"."booking_equipment"
    ADD CONSTRAINT "booking_equipment_bookingId_fkey" FOREIGN KEY ("bookingId") REFERENCES "public"."booking"("id");



ALTER TABLE ONLY "public"."booking_equipment"
    ADD CONSTRAINT "booking_equipment_equipmentId_fkey" FOREIGN KEY ("equipmentId") REFERENCES "public"."equipment"("id");



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "booking_scheduleId_fkey" FOREIGN KEY ("scheduleId") REFERENCES "public"."schedule"("id");



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "booking_service_fk" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id");



ALTER TABLE ONLY "public"."booking"
    ADD CONSTRAINT "booking_user_fk" FOREIGN KEY ("userId") REFERENCES "public"."profile"("id");



ALTER TABLE ONLY "public"."cancelation_policy"
    ADD CONSTRAINT "cancelation_policy_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."course"
    ADD CONSTRAINT "course_featureVideo_fkey" FOREIGN KEY ("featureVideo") REFERENCES "public"."video"("id");



ALTER TABLE ONLY "public"."course"
    ADD CONSTRAINT "course_instructorId_fkey" FOREIGN KEY ("instructorId") REFERENCES "public"."profile"("id");



ALTER TABLE ONLY "public"."course"
    ADD CONSTRAINT "course_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id");



ALTER TABLE ONLY "public"."equipment"
    ADD CONSTRAINT "equipment_school_fk" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id");



ALTER TABLE ONLY "public"."face"
    ADD CONSTRAINT "face_user_fk" FOREIGN KEY ("email") REFERENCES "public"."profile"("email");



ALTER TABLE ONLY "public"."lesson"
    ADD CONSTRAINT "lesson_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES "public"."course"("id");



ALTER TABLE ONLY "public"."lesson"
    ADD CONSTRAINT "lesson_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES "public"."module"("id");



ALTER TABLE ONLY "public"."lesson"
    ADD CONSTRAINT "lesson_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "public"."video"("id");



ALTER TABLE ONLY "public"."module"
    ADD CONSTRAINT "module_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES "public"."course"("id");



ALTER TABLE ONLY "public"."plan"
    ADD CONSTRAINT "plan_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id");



ALTER TABLE ONLY "public"."progress"
    ADD CONSTRAINT "progress_course_id_fkey" FOREIGN KEY ("courseId") REFERENCES "public"."course"("id");



ALTER TABLE ONLY "public"."progress"
    ADD CONSTRAINT "progress_lesson_id_fkey" FOREIGN KEY ("lessonId") REFERENCES "public"."lesson"("id");



ALTER TABLE ONLY "public"."progress"
    ADD CONSTRAINT "progress_user_id_fkey" FOREIGN KEY ("userId") REFERENCES "public"."profile"("id");



ALTER TABLE ONLY "public"."schedule"
    ADD CONSTRAINT "schedule_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id");



ALTER TABLE ONLY "public"."service_equipment"
    ADD CONSTRAINT "service_equipment_equipmentId_fk" FOREIGN KEY ("equipmentId") REFERENCES "public"."equipment"("id");



ALTER TABLE ONLY "public"."service_equipment"
    ADD CONSTRAINT "service_equipment_service_fk" FOREIGN KEY ("serviceId") REFERENCES "public"."service"("id");



ALTER TABLE ONLY "public"."service"
    ADD CONSTRAINT "service_school_fk" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id");



ALTER TABLE ONLY "public"."subscription"
    ADD CONSTRAINT "subscription_plan_fk" FOREIGN KEY ("planId") REFERENCES "public"."plan"("planId");



ALTER TABLE ONLY "public"."subscription"
    ADD CONSTRAINT "subscription_useId_fk" FOREIGN KEY ("userId") REFERENCES "public"."profile"("id");



ALTER TABLE ONLY "public"."profile"
    ADD CONSTRAINT "user_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."profile"
    ADD CONSTRAINT "user_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id");



ALTER TABLE ONLY "public"."video"
    ADD CONSTRAINT "video_schoolId_fkey1" FOREIGN KEY ("schoolId") REFERENCES "public"."school"("id");



ALTER TABLE "public"."attendance" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."authorized_payment" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."authorized_payment_notification" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."booking" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."booking_equipment" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."cancelation_policy" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."course" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."equipment" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."error" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."face" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."lesson" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."module" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."mp_application" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."payment" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."payment_notification" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."plan" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."progress" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."schedule" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."school" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."service" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."service_equipment" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."subscription" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."profile" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_roles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."video" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";





GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";
GRANT USAGE ON SCHEMA "public" TO "supabase_auth_admin";


REVOKE ALL ON FUNCTION "public"."custom_access_token_hook"("event" "jsonb") FROM PUBLIC;
GRANT ALL ON FUNCTION "public"."custom_access_token_hook"("event" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."custom_access_token_hook"("event" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."custom_access_token_hook"("event" "jsonb") TO "service_role";
GRANT ALL ON FUNCTION "public"."custom_access_token_hook"("event" "jsonb") TO "supabase_auth_admin";


GRANT ALL ON TABLE "public"."attendance" TO "anon";
GRANT ALL ON TABLE "public"."attendance" TO "authenticated";
GRANT ALL ON TABLE "public"."attendance" TO "service_role";



GRANT ALL ON TABLE "public"."profile" TO "anon";
GRANT ALL ON TABLE "public"."profile" TO "authenticated";
GRANT ALL ON TABLE "public"."profile" TO "service_role";



GRANT ALL ON TABLE "public"."attendance_monthly" TO "anon";
GRANT ALL ON TABLE "public"."attendance_monthly" TO "authenticated";
GRANT ALL ON TABLE "public"."attendance_monthly" TO "service_role";



GRANT ALL ON TABLE "public"."attendance_yearly" TO "anon";
GRANT ALL ON TABLE "public"."attendance_yearly" TO "authenticated";
GRANT ALL ON TABLE "public"."attendance_yearly" TO "service_role";



GRANT ALL ON TABLE "public"."authorized_payment" TO "anon";
GRANT ALL ON TABLE "public"."authorized_payment" TO "authenticated";
GRANT ALL ON TABLE "public"."authorized_payment" TO "service_role";



GRANT ALL ON TABLE "public"."authorized_payment_notification" TO "anon";
GRANT ALL ON TABLE "public"."authorized_payment_notification" TO "authenticated";
GRANT ALL ON TABLE "public"."authorized_payment_notification" TO "service_role";



GRANT ALL ON SEQUENCE "public"."authorized_payment_notification_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."authorized_payment_notification_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."authorized_payment_notification_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."booking" TO "anon";
GRANT ALL ON TABLE "public"."booking" TO "authenticated";
GRANT ALL ON TABLE "public"."booking" TO "service_role";



GRANT ALL ON TABLE "public"."booking_equipment" TO "anon";
GRANT ALL ON TABLE "public"."booking_equipment" TO "authenticated";
GRANT ALL ON TABLE "public"."booking_equipment" TO "service_role";



GRANT ALL ON TABLE "public"."cancelation_policy" TO "anon";
GRANT ALL ON TABLE "public"."cancelation_policy" TO "authenticated";
GRANT ALL ON TABLE "public"."cancelation_policy" TO "service_role";



GRANT ALL ON TABLE "public"."course" TO "anon";
GRANT ALL ON TABLE "public"."course" TO "authenticated";
GRANT ALL ON TABLE "public"."course" TO "service_role";



GRANT ALL ON TABLE "public"."equipment" TO "anon";
GRANT ALL ON TABLE "public"."equipment" TO "authenticated";
GRANT ALL ON TABLE "public"."equipment" TO "service_role";



GRANT ALL ON TABLE "public"."error" TO "anon";
GRANT ALL ON TABLE "public"."error" TO "authenticated";
GRANT ALL ON TABLE "public"."error" TO "service_role";



GRANT ALL ON TABLE "public"."face" TO "anon";
GRANT ALL ON TABLE "public"."face" TO "authenticated";
GRANT ALL ON TABLE "public"."face" TO "service_role";



GRANT ALL ON TABLE "public"."lesson" TO "anon";
GRANT ALL ON TABLE "public"."lesson" TO "authenticated";
GRANT ALL ON TABLE "public"."lesson" TO "service_role";



GRANT ALL ON TABLE "public"."module" TO "anon";
GRANT ALL ON TABLE "public"."module" TO "authenticated";
GRANT ALL ON TABLE "public"."module" TO "service_role";



GRANT ALL ON TABLE "public"."mp_application" TO "anon";
GRANT ALL ON TABLE "public"."mp_application" TO "authenticated";
GRANT ALL ON TABLE "public"."mp_application" TO "service_role";



GRANT ALL ON TABLE "public"."payment" TO "anon";
GRANT ALL ON TABLE "public"."payment" TO "authenticated";
GRANT ALL ON TABLE "public"."payment" TO "service_role";



GRANT ALL ON TABLE "public"."payment_notification" TO "anon";
GRANT ALL ON TABLE "public"."payment_notification" TO "authenticated";
GRANT ALL ON TABLE "public"."payment_notification" TO "service_role";



GRANT ALL ON TABLE "public"."plan" TO "anon";
GRANT ALL ON TABLE "public"."plan" TO "authenticated";
GRANT ALL ON TABLE "public"."plan" TO "service_role";



GRANT ALL ON TABLE "public"."progress" TO "anon";
GRANT ALL ON TABLE "public"."progress" TO "authenticated";
GRANT ALL ON TABLE "public"."progress" TO "service_role";



GRANT ALL ON TABLE "public"."schedule" TO "anon";
GRANT ALL ON TABLE "public"."schedule" TO "authenticated";
GRANT ALL ON TABLE "public"."schedule" TO "service_role";



GRANT ALL ON TABLE "public"."school" TO "anon";
GRANT ALL ON TABLE "public"."school" TO "authenticated";
GRANT ALL ON TABLE "public"."school" TO "service_role";



GRANT ALL ON TABLE "public"."service" TO "anon";
GRANT ALL ON TABLE "public"."service" TO "authenticated";
GRANT ALL ON TABLE "public"."service" TO "service_role";



GRANT ALL ON TABLE "public"."service_equipment" TO "anon";
GRANT ALL ON TABLE "public"."service_equipment" TO "authenticated";
GRANT ALL ON TABLE "public"."service_equipment" TO "service_role";



GRANT ALL ON TABLE "public"."subscription" TO "anon";
GRANT ALL ON TABLE "public"."subscription" TO "authenticated";
GRANT ALL ON TABLE "public"."subscription" TO "service_role";



GRANT ALL ON TABLE "public"."user_roles" TO "anon";
GRANT ALL ON TABLE "public"."user_roles" TO "authenticated";
GRANT ALL ON TABLE "public"."user_roles" TO "service_role";
GRANT ALL ON TABLE "public"."user_roles" TO "supabase_auth_admin";



GRANT ALL ON TABLE "public"."video" TO "anon";
GRANT ALL ON TABLE "public"."video" TO "authenticated";
GRANT ALL ON TABLE "public"."video" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






RESET ALL;

--
-- Dumped schema changes for auth and storage
--

