alter table "public"."authorized_payment_notification" drop constraint "authorized_payment_notification_pkey";

drop index if exists "public"."authorized_payment_notification_pkey";

alter table "public"."authorized_payment_notification" alter column "new_id" set not null;

CREATE UNIQUE INDEX authorized_payment_notification_pkey ON public.authorized_payment_notification USING btree (new_id);

alter table "public"."authorized_payment_notification" add constraint "authorized_payment_notification_pkey" PRIMARY KEY using index "authorized_payment_notification_pkey";


