create table "public"."debt_root" (
    "id" subscription_periodicity not null,
    "createdAt" timestamp with time zone not null default now(),
    "value" numeric,
    "name" character varying,
    "type" character varying,
    "description" character varying,
    "paid" boolean,
    "fixed" boolean,
    "repetitions" smallint,
    "method" payment_method,
    "billingDay" character varying,
    "schoolId" uuid
);

alter table "public"."debt_root" enable row level security;

CREATE UNIQUE INDEX debt_root_pkey ON public.debt_root USING btree (id);

alter table "public"."debt_root" add constraint "debt_root_pkey" PRIMARY KEY using index "debt_root_pkey";

alter table "public"."debt_root" add constraint "debt_root_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) ON DELETE CASCADE not valid;

alter table "public"."debt_root" validate constraint "debt_root_schoolId_fkey";

grant delete on table "public"."debt_root" to "anon";

grant insert on table "public"."debt_root" to "anon";

grant references on table "public"."debt_root" to "anon";

grant select on table "public"."debt_root" to "anon";

grant trigger on table "public"."debt_root" to "anon";

grant truncate on table "public"."debt_root" to "anon";

grant update on table "public"."debt_root" to "anon";

grant delete on table "public"."debt_root" to "authenticated";

grant insert on table "public"."debt_root" to "authenticated";

grant references on table "public"."debt_root" to "authenticated";

grant select on table "public"."debt_root" to "authenticated";

grant trigger on table "public"."debt_root" to "authenticated";

grant truncate on table "public"."debt_root" to "authenticated";

grant update on table "public"."debt_root" to "authenticated";

grant delete on table "public"."debt_root" to "service_role";

grant insert on table "public"."debt_root" to "service_role";

grant references on table "public"."debt_root" to "service_role";

grant select on table "public"."debt_root" to "service_role";

grant trigger on table "public"."debt_root" to "service_role";

grant truncate on table "public"."debt_root" to "service_role";

grant update on table "public"."debt_root" to "service_role"
