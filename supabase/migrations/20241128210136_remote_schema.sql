revoke delete on table "public"."payment_plan" from "anon";

revoke insert on table "public"."payment_plan" from "anon";

revoke references on table "public"."payment_plan" from "anon";

revoke select on table "public"."payment_plan" from "anon";

revoke trigger on table "public"."payment_plan" from "anon";

revoke truncate on table "public"."payment_plan" from "anon";

revoke update on table "public"."payment_plan" from "anon";

revoke delete on table "public"."payment_plan" from "authenticated";

revoke insert on table "public"."payment_plan" from "authenticated";

revoke references on table "public"."payment_plan" from "authenticated";

revoke select on table "public"."payment_plan" from "authenticated";

revoke trigger on table "public"."payment_plan" from "authenticated";

revoke truncate on table "public"."payment_plan" from "authenticated";

revoke update on table "public"."payment_plan" from "authenticated";

revoke delete on table "public"."payment_plan" from "service_role";

revoke insert on table "public"."payment_plan" from "service_role";

revoke references on table "public"."payment_plan" from "service_role";

revoke select on table "public"."payment_plan" from "service_role";

revoke trigger on table "public"."payment_plan" from "service_role";

revoke truncate on table "public"."payment_plan" from "service_role";

revoke update on table "public"."payment_plan" from "service_role";

revoke delete on table "public"."payment_profile" from "anon";

revoke insert on table "public"."payment_profile" from "anon";

revoke references on table "public"."payment_profile" from "anon";

revoke select on table "public"."payment_profile" from "anon";

revoke trigger on table "public"."payment_profile" from "anon";

revoke truncate on table "public"."payment_profile" from "anon";

revoke update on table "public"."payment_profile" from "anon";

revoke delete on table "public"."payment_profile" from "authenticated";

revoke insert on table "public"."payment_profile" from "authenticated";

revoke references on table "public"."payment_profile" from "authenticated";

revoke select on table "public"."payment_profile" from "authenticated";

revoke trigger on table "public"."payment_profile" from "authenticated";

revoke truncate on table "public"."payment_profile" from "authenticated";

revoke update on table "public"."payment_profile" from "authenticated";

revoke delete on table "public"."payment_profile" from "service_role";

revoke insert on table "public"."payment_profile" from "service_role";

revoke references on table "public"."payment_profile" from "service_role";

revoke select on table "public"."payment_profile" from "service_role";

revoke trigger on table "public"."payment_profile" from "service_role";

revoke truncate on table "public"."payment_profile" from "service_role";

revoke update on table "public"."payment_profile" from "service_role";

alter table "public"."payment_plan" drop constraint "payment_plan_paymentId_key";

alter table "public"."payment_plan" drop constraint "payment_plan_planId_fkey";

alter table "public"."payment_profile" drop constraint "payment_profile_paymentId_key";

alter table "public"."payment_profile" drop constraint "payment_profile_profileId_fkey";

alter table "public"."payment_plan" drop constraint "payment_plan_pkey";

alter table "public"."payment_profile" drop constraint "payment_profile_pkey";

drop index if exists "public"."payment_plan_paymentId_key";

drop index if exists "public"."payment_plan_pkey";

drop index if exists "public"."payment_profile_paymentId_key";

drop index if exists "public"."payment_profile_pkey";

drop table "public"."payment_plan";

drop table "public"."payment_profile";


