alter table "public"."plan_profile" drop constraint "plan_profile_pkey";

drop index if exists "public"."plan_profile_pkey";

alter table "public"."plan_profile" drop column "id";

alter table "public"."plan_profile" alter column "new_id" set not null;

CREATE UNIQUE INDEX plan_profile_pkey ON public.plan_profile USING btree (new_id);

alter table "public"."plan_profile" add constraint "plan_profile_pkey" PRIMARY KEY using index "plan_profile_pkey";


