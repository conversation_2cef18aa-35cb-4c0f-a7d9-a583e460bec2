alter table "public"."face" drop constraint "email_unique";

alter table "public"."pkg" drop constraint "package_schoolId_fkey";

alter table "public"."profile" drop constraint "user_schoolId_fkey";

alter table "public"."service" drop constraint "service_school_fk";

alter table "public"."lesson" drop constraint "lesson_courseId_fkey";

alter table "public"."lesson" drop constraint "lesson_moduleId_fkey";

alter table "public"."module" drop constraint "module_courseId_fkey";

alter table "public"."plan" drop constraint "plan_schoolId_fkey";

drop index if exists "public"."email_unique";

create table "public"."user_pwa" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "deviceId" character varying,
    "sub" json,
    "schoolId" uuid,
    "profileId" uuid
);

alter table "public"."user_pwa" enable row level security;

alter table "public"."school_settings" alter column "closedWeekDays" set data type text[] using "closedWeekDays"::text[];

CREATE UNIQUE INDEX profile_email_type ON public.profile USING btree (email, type);

CREATE UNIQUE INDEX user_pwa_pkey ON public.user_pwa USING btree (id);

alter table "public"."user_pwa" add constraint "user_pwa_pkey" PRIMARY KEY using index "user_pwa_pkey";

alter table "public"."pkg" add constraint "pkg_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) ON DELETE CASCADE not valid;

alter table "public"."pkg" validate constraint "pkg_schoolId_fkey";

alter table "public"."profile" add constraint "profile_email_type" UNIQUE using index "profile_email_type";

alter table "public"."profile" add constraint "profile_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) ON DELETE CASCADE not valid;

alter table "public"."profile" validate constraint "profile_schoolId_fkey";

alter table "public"."service" add constraint "service_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) ON DELETE CASCADE not valid;

alter table "public"."service" validate constraint "service_schoolId_fkey";

alter table "public"."user_pwa" add constraint "user_pwa_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."user_pwa" validate constraint "user_pwa_profileId_fkey";

alter table "public"."user_pwa" add constraint "user_pwa_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) ON DELETE CASCADE not valid;

alter table "public"."user_pwa" validate constraint "user_pwa_schoolId_fkey";

alter table "public"."lesson" add constraint "lesson_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES course(id) ON DELETE CASCADE not valid;

alter table "public"."lesson" validate constraint "lesson_courseId_fkey";

alter table "public"."lesson" add constraint "lesson_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES module(id) ON DELETE CASCADE not valid;

alter table "public"."lesson" validate constraint "lesson_moduleId_fkey";

alter table "public"."module" add constraint "module_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES course(id) ON DELETE CASCADE not valid;

alter table "public"."module" validate constraint "module_courseId_fkey";

alter table "public"."plan" add constraint "plan_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) ON DELETE CASCADE not valid;

alter table "public"."plan" validate constraint "plan_schoolId_fkey";

grant delete on table "public"."user_pwa" to "anon";

grant insert on table "public"."user_pwa" to "anon";

grant references on table "public"."user_pwa" to "anon";

grant select on table "public"."user_pwa" to "anon";

grant trigger on table "public"."user_pwa" to "anon";

grant truncate on table "public"."user_pwa" to "anon";

grant update on table "public"."user_pwa" to "anon";

grant delete on table "public"."user_pwa" to "authenticated";

grant insert on table "public"."user_pwa" to "authenticated";

grant references on table "public"."user_pwa" to "authenticated";

grant select on table "public"."user_pwa" to "authenticated";

grant trigger on table "public"."user_pwa" to "authenticated";

grant truncate on table "public"."user_pwa" to "authenticated";

grant update on table "public"."user_pwa" to "authenticated";

grant delete on table "public"."user_pwa" to "service_role";

grant insert on table "public"."user_pwa" to "service_role";

grant references on table "public"."user_pwa" to "service_role";

grant select on table "public"."user_pwa" to "service_role";

grant trigger on table "public"."user_pwa" to "service_role";

grant truncate on table "public"."user_pwa" to "service_role";

grant update on table "public"."user_pwa" to "service_role";
