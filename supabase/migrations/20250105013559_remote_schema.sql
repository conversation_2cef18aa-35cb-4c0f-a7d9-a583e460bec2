create type "public"."sports" as enum ('jiu-jitsu', 'muay-thai', 'karate', 'judo');

create table "public"."rank" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "sport" sports not null,
    "name" character varying not null,
    "color" character varying,
    "nextRank" uuid
);


alter table "public"."rank" enable row level security;

create table "public"."rank_school" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "schoolId" uuid not null,
    "classes" smallint not null,
    "rankId" uuid not null
);


alter table "public"."rank_school" enable row level security;

CREATE UNIQUE INDEX rank_pkey ON public.rank USING btree (id);

CREATE UNIQUE INDEX rank_school_pkey ON public.rank_school USING btree (id);

alter table "public"."rank" add constraint "rank_pkey" PRIMARY KEY using index "rank_pkey";

alter table "public"."rank_school" add constraint "rank_school_pkey" PRIMARY KEY using index "rank_school_pkey";

alter table "public"."rank" add constraint "rank_nextRank_fkey" FOREIGN KEY ("nextRank") REFERENCES rank(id) not valid;

alter table "public"."rank" validate constraint "rank_nextRank_fkey";

alter table "public"."rank_school" add constraint "rank_school_rankId_fkey" FOREIGN KEY ("rankId") REFERENCES rank(id) not valid;

alter table "public"."rank_school" validate constraint "rank_school_rankId_fkey";

alter table "public"."rank_school" add constraint "rank_school_schoolId_fkey" FOREIGN KEY ("schoolId") REFERENCES school(id) not valid;

alter table "public"."rank_school" validate constraint "rank_school_schoolId_fkey";

grant delete on table "public"."rank" to "anon";

grant insert on table "public"."rank" to "anon";

grant references on table "public"."rank" to "anon";

grant select on table "public"."rank" to "anon";

grant trigger on table "public"."rank" to "anon";

grant truncate on table "public"."rank" to "anon";

grant update on table "public"."rank" to "anon";

grant delete on table "public"."rank" to "authenticated";

grant insert on table "public"."rank" to "authenticated";

grant references on table "public"."rank" to "authenticated";

grant select on table "public"."rank" to "authenticated";

grant trigger on table "public"."rank" to "authenticated";

grant truncate on table "public"."rank" to "authenticated";

grant update on table "public"."rank" to "authenticated";

grant delete on table "public"."rank" to "service_role";

grant insert on table "public"."rank" to "service_role";

grant references on table "public"."rank" to "service_role";

grant select on table "public"."rank" to "service_role";

grant trigger on table "public"."rank" to "service_role";

grant truncate on table "public"."rank" to "service_role";

grant update on table "public"."rank" to "service_role";

grant delete on table "public"."rank_school" to "anon";

grant insert on table "public"."rank_school" to "anon";

grant references on table "public"."rank_school" to "anon";

grant select on table "public"."rank_school" to "anon";

grant trigger on table "public"."rank_school" to "anon";

grant truncate on table "public"."rank_school" to "anon";

grant update on table "public"."rank_school" to "anon";

grant delete on table "public"."rank_school" to "authenticated";

grant insert on table "public"."rank_school" to "authenticated";

grant references on table "public"."rank_school" to "authenticated";

grant select on table "public"."rank_school" to "authenticated";

grant trigger on table "public"."rank_school" to "authenticated";

grant truncate on table "public"."rank_school" to "authenticated";

grant update on table "public"."rank_school" to "authenticated";

grant delete on table "public"."rank_school" to "service_role";

grant insert on table "public"."rank_school" to "service_role";

grant references on table "public"."rank_school" to "service_role";

grant select on table "public"."rank_school" to "service_role";

grant trigger on table "public"."rank_school" to "service_role";

grant truncate on table "public"."rank_school" to "service_role";

grant update on table "public"."rank_school" to "service_role";
