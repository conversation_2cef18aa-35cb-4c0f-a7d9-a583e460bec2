revoke delete on table "public"."plan_profile" from "anon";

revoke insert on table "public"."plan_profile" from "anon";

revoke references on table "public"."plan_profile" from "anon";

revoke select on table "public"."plan_profile" from "anon";

revoke trigger on table "public"."plan_profile" from "anon";

revoke truncate on table "public"."plan_profile" from "anon";

revoke update on table "public"."plan_profile" from "anon";

revoke delete on table "public"."plan_profile" from "authenticated";

revoke insert on table "public"."plan_profile" from "authenticated";

revoke references on table "public"."plan_profile" from "authenticated";

revoke select on table "public"."plan_profile" from "authenticated";

revoke trigger on table "public"."plan_profile" from "authenticated";

revoke truncate on table "public"."plan_profile" from "authenticated";

revoke update on table "public"."plan_profile" from "authenticated";

revoke delete on table "public"."plan_profile" from "service_role";

revoke insert on table "public"."plan_profile" from "service_role";

revoke references on table "public"."plan_profile" from "service_role";

revoke select on table "public"."plan_profile" from "service_role";

revoke trigger on table "public"."plan_profile" from "service_role";

revoke truncate on table "public"."plan_profile" from "service_role";

revoke update on table "public"."plan_profile" from "service_role";

revoke delete on table "public"."subscription_asaas" from "anon";

revoke insert on table "public"."subscription_asaas" from "anon";

revoke references on table "public"."subscription_asaas" from "anon";

revoke select on table "public"."subscription_asaas" from "anon";

revoke trigger on table "public"."subscription_asaas" from "anon";

revoke truncate on table "public"."subscription_asaas" from "anon";

revoke update on table "public"."subscription_asaas" from "anon";

revoke delete on table "public"."subscription_asaas" from "authenticated";

revoke insert on table "public"."subscription_asaas" from "authenticated";

revoke references on table "public"."subscription_asaas" from "authenticated";

revoke select on table "public"."subscription_asaas" from "authenticated";

revoke trigger on table "public"."subscription_asaas" from "authenticated";

revoke truncate on table "public"."subscription_asaas" from "authenticated";

revoke update on table "public"."subscription_asaas" from "authenticated";

revoke delete on table "public"."subscription_asaas" from "service_role";

revoke insert on table "public"."subscription_asaas" from "service_role";

revoke references on table "public"."subscription_asaas" from "service_role";

revoke select on table "public"."subscription_asaas" from "service_role";

revoke trigger on table "public"."subscription_asaas" from "service_role";

revoke truncate on table "public"."subscription_asaas" from "service_role";

revoke update on table "public"."subscription_asaas" from "service_role";

alter table "public"."plan_profile" drop constraint "plan_profile_planId_fkey";

alter table "public"."plan_profile" drop constraint "plan_profile_profileId_fkey";

alter table "public"."subscription_asaas" drop constraint "subscription_asaas_planId_fkey";

alter table "public"."subscription_asaas" drop constraint "subscription_asaas_profileId_fkey";

alter table "public"."plan_profile" drop constraint "plan_profile_pkey";

alter table "public"."subscription_asaas" drop constraint "subscription_asaas_pkey";

drop index if exists "public"."plan_profile_pkey";

drop index if exists "public"."subscription_asaas_pkey";

drop table "public"."plan_profile";

drop table "public"."subscription_asaas";

create table "public"."subscription" (
    "id" character varying not null,
    "createdAt" date not null default now(),
    "object" character varying,
    "customer" character varying not null,
    "paymentLink" character varying,
    "billingType" billing_type,
    "cycle" cycle not null,
    "value" numeric not null,
    "nextDueDate" date,
    "endDate" date,
    "description" character varying,
    "fineValue" numeric,
    "interestValue" numeric,
    "deleted" boolean,
    "maxPayments" smallint,
    "planId" uuid,
    "profileId" uuid,
    "status" status not null default 'ACTIVE'::status,
    "cardNumber" character varying,
    "cardBrand" character varying,
    "paymentStatus" payment_status
);


alter table "public"."subscription" enable row level security;

CREATE UNIQUE INDEX subscription_asaas_pkey ON public.subscription USING btree (id);

alter table "public"."subscription" add constraint "subscription_asaas_pkey" PRIMARY KEY using index "subscription_asaas_pkey";

alter table "public"."subscription" add constraint "subscription_asaas_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) not valid;

alter table "public"."subscription" validate constraint "subscription_asaas_planId_fkey";

alter table "public"."subscription" add constraint "subscription_asaas_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."subscription" validate constraint "subscription_asaas_profileId_fkey";

grant delete on table "public"."subscription" to "anon";

grant insert on table "public"."subscription" to "anon";

grant references on table "public"."subscription" to "anon";

grant select on table "public"."subscription" to "anon";

grant trigger on table "public"."subscription" to "anon";

grant truncate on table "public"."subscription" to "anon";

grant update on table "public"."subscription" to "anon";

grant delete on table "public"."subscription" to "authenticated";

grant insert on table "public"."subscription" to "authenticated";

grant references on table "public"."subscription" to "authenticated";

grant select on table "public"."subscription" to "authenticated";

grant trigger on table "public"."subscription" to "authenticated";

grant truncate on table "public"."subscription" to "authenticated";

grant update on table "public"."subscription" to "authenticated";

grant delete on table "public"."subscription" to "service_role";

grant insert on table "public"."subscription" to "service_role";

grant references on table "public"."subscription" to "service_role";

grant select on table "public"."subscription" to "service_role";

grant trigger on table "public"."subscription" to "service_role";

grant truncate on table "public"."subscription" to "service_role";

grant update on table "public"."subscription" to "service_role";
