revoke delete on table "public"."pack_service" from "anon";

revoke insert on table "public"."pack_service" from "anon";

revoke references on table "public"."pack_service" from "anon";

revoke select on table "public"."pack_service" from "anon";

revoke trigger on table "public"."pack_service" from "anon";

revoke truncate on table "public"."pack_service" from "anon";

revoke update on table "public"."pack_service" from "anon";

revoke delete on table "public"."pack_service" from "authenticated";

revoke insert on table "public"."pack_service" from "authenticated";

revoke references on table "public"."pack_service" from "authenticated";

revoke select on table "public"."pack_service" from "authenticated";

revoke trigger on table "public"."pack_service" from "authenticated";

revoke truncate on table "public"."pack_service" from "authenticated";

revoke update on table "public"."pack_service" from "authenticated";

revoke delete on table "public"."pack_service" from "service_role";

revoke insert on table "public"."pack_service" from "service_role";

revoke references on table "public"."pack_service" from "service_role";

revoke select on table "public"."pack_service" from "service_role";

revoke trigger on table "public"."pack_service" from "service_role";

revoke truncate on table "public"."pack_service" from "service_role";

revoke update on table "public"."pack_service" from "service_role";

revoke delete on table "public"."plan_service" from "anon";

revoke insert on table "public"."plan_service" from "anon";

revoke references on table "public"."plan_service" from "anon";

revoke select on table "public"."plan_service" from "anon";

revoke trigger on table "public"."plan_service" from "anon";

revoke truncate on table "public"."plan_service" from "anon";

revoke update on table "public"."plan_service" from "anon";

revoke delete on table "public"."plan_service" from "authenticated";

revoke insert on table "public"."plan_service" from "authenticated";

revoke references on table "public"."plan_service" from "authenticated";

revoke select on table "public"."plan_service" from "authenticated";

revoke trigger on table "public"."plan_service" from "authenticated";

revoke truncate on table "public"."plan_service" from "authenticated";

revoke update on table "public"."plan_service" from "authenticated";

revoke delete on table "public"."plan_service" from "service_role";

revoke insert on table "public"."plan_service" from "service_role";

revoke references on table "public"."plan_service" from "service_role";

revoke select on table "public"."plan_service" from "service_role";

revoke trigger on table "public"."plan_service" from "service_role";

revoke truncate on table "public"."plan_service" from "service_role";

revoke update on table "public"."plan_service" from "service_role";

alter table "public"."pack_service" drop constraint "pack_service_packId_fkey";

alter table "public"."pack_service" drop constraint "pack_service_serviceId_fkey";

alter table "public"."plan_service" drop constraint "plan_service_planId_fkey";

alter table "public"."plan_service" drop constraint "plan_service_serviceId_fkey";

alter table "public"."pack_service" drop constraint "pack_service_pkey";

alter table "public"."plan_service" drop constraint "plan_service_pkey";

drop index if exists "public"."pack_service_pkey";

drop index if exists "public"."plan_service_pkey";

drop table "public"."pack_service";

drop table "public"."plan_service";

create table "public"."service_pack" (
    "id" bigint generated by default as identity not null,
    "createdAt" timestamp with time zone not null default now(),
    "packId" uuid not null,
    "serviceId" uuid not null
);


alter table "public"."service_pack" enable row level security;

create table "public"."service_plan" (
    "id" bigint generated by default as identity not null,
    "createdAt" timestamp with time zone not null default now(),
    "planId" uuid not null,
    "serviceId" uuid not null
);


alter table "public"."service_plan" enable row level security;

create table "public"."service_sport" (
    "id" uuid not null default gen_random_uuid(),
    "createdAt" timestamp with time zone not null default now(),
    "sportId" uuid not null,
    "serviceId" uuid not null
);


alter table "public"."service_sport" enable row level security;

create table "public"."service_student" (
    "id" bigint generated by default as identity not null,
    "createdAt" timestamp with time zone not null default now(),
    "profileId" uuid not null,
    "serviceId" uuid not null
);


alter table "public"."service_student" enable row level security;

alter table "public"."rank_student" alter column "profileId" set not null;

alter table "public"."rank_student" alter column "rankId" set not null;

CREATE UNIQUE INDEX service_profile_pkey ON public.service_student USING btree (id);

CREATE UNIQUE INDEX service_sport_pkey ON public.service_sport USING btree (id);

CREATE UNIQUE INDEX pack_service_pkey ON public.service_pack USING btree (id);

CREATE UNIQUE INDEX plan_service_pkey ON public.service_plan USING btree (id);

alter table "public"."service_pack" add constraint "pack_service_pkey" PRIMARY KEY using index "pack_service_pkey";

alter table "public"."service_plan" add constraint "plan_service_pkey" PRIMARY KEY using index "plan_service_pkey";

alter table "public"."service_sport" add constraint "service_sport_pkey" PRIMARY KEY using index "service_sport_pkey";

alter table "public"."service_student" add constraint "service_profile_pkey" PRIMARY KEY using index "service_profile_pkey";

alter table "public"."service_pack" add constraint "pack_service_packId_fkey" FOREIGN KEY ("packId") REFERENCES pkg(id) ON DELETE CASCADE not valid;

alter table "public"."service_pack" validate constraint "pack_service_packId_fkey";

alter table "public"."service_pack" add constraint "pack_service_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) ON DELETE CASCADE not valid;

alter table "public"."service_pack" validate constraint "pack_service_serviceId_fkey";

alter table "public"."service_plan" add constraint "plan_service_planId_fkey" FOREIGN KEY ("planId") REFERENCES plan(id) ON DELETE CASCADE not valid;

alter table "public"."service_plan" validate constraint "plan_service_planId_fkey";

alter table "public"."service_plan" add constraint "plan_service_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) ON DELETE CASCADE not valid;

alter table "public"."service_plan" validate constraint "plan_service_serviceId_fkey";

alter table "public"."service_sport" add constraint "service_sport_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) ON DELETE CASCADE not valid;

alter table "public"."service_sport" validate constraint "service_sport_serviceId_fkey";

alter table "public"."service_sport" add constraint "service_sport_sportId_fkey" FOREIGN KEY ("sportId") REFERENCES sport(id) ON DELETE CASCADE not valid;

alter table "public"."service_sport" validate constraint "service_sport_sportId_fkey";

alter table "public"."service_student" add constraint "service_profile_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."service_student" validate constraint "service_profile_profileId_fkey";

alter table "public"."service_student" add constraint "service_profile_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES service(id) ON DELETE CASCADE not valid;

alter table "public"."service_student" validate constraint "service_profile_serviceId_fkey";

grant delete on table "public"."service_pack" to "anon";

grant insert on table "public"."service_pack" to "anon";

grant references on table "public"."service_pack" to "anon";

grant select on table "public"."service_pack" to "anon";

grant trigger on table "public"."service_pack" to "anon";

grant truncate on table "public"."service_pack" to "anon";

grant update on table "public"."service_pack" to "anon";

grant delete on table "public"."service_pack" to "authenticated";

grant insert on table "public"."service_pack" to "authenticated";

grant references on table "public"."service_pack" to "authenticated";

grant select on table "public"."service_pack" to "authenticated";

grant trigger on table "public"."service_pack" to "authenticated";

grant truncate on table "public"."service_pack" to "authenticated";

grant update on table "public"."service_pack" to "authenticated";

grant delete on table "public"."service_pack" to "service_role";

grant insert on table "public"."service_pack" to "service_role";

grant references on table "public"."service_pack" to "service_role";

grant select on table "public"."service_pack" to "service_role";

grant trigger on table "public"."service_pack" to "service_role";

grant truncate on table "public"."service_pack" to "service_role";

grant update on table "public"."service_pack" to "service_role";

grant delete on table "public"."service_plan" to "anon";

grant insert on table "public"."service_plan" to "anon";

grant references on table "public"."service_plan" to "anon";

grant select on table "public"."service_plan" to "anon";

grant trigger on table "public"."service_plan" to "anon";

grant truncate on table "public"."service_plan" to "anon";

grant update on table "public"."service_plan" to "anon";

grant delete on table "public"."service_plan" to "authenticated";

grant insert on table "public"."service_plan" to "authenticated";

grant references on table "public"."service_plan" to "authenticated";

grant select on table "public"."service_plan" to "authenticated";

grant trigger on table "public"."service_plan" to "authenticated";

grant truncate on table "public"."service_plan" to "authenticated";

grant update on table "public"."service_plan" to "authenticated";

grant delete on table "public"."service_plan" to "service_role";

grant insert on table "public"."service_plan" to "service_role";

grant references on table "public"."service_plan" to "service_role";

grant select on table "public"."service_plan" to "service_role";

grant trigger on table "public"."service_plan" to "service_role";

grant truncate on table "public"."service_plan" to "service_role";

grant update on table "public"."service_plan" to "service_role";

grant delete on table "public"."service_sport" to "anon";

grant insert on table "public"."service_sport" to "anon";

grant references on table "public"."service_sport" to "anon";

grant select on table "public"."service_sport" to "anon";

grant trigger on table "public"."service_sport" to "anon";

grant truncate on table "public"."service_sport" to "anon";

grant update on table "public"."service_sport" to "anon";

grant delete on table "public"."service_sport" to "authenticated";

grant insert on table "public"."service_sport" to "authenticated";

grant references on table "public"."service_sport" to "authenticated";

grant select on table "public"."service_sport" to "authenticated";

grant trigger on table "public"."service_sport" to "authenticated";

grant truncate on table "public"."service_sport" to "authenticated";

grant update on table "public"."service_sport" to "authenticated";

grant delete on table "public"."service_sport" to "service_role";

grant insert on table "public"."service_sport" to "service_role";

grant references on table "public"."service_sport" to "service_role";

grant select on table "public"."service_sport" to "service_role";

grant trigger on table "public"."service_sport" to "service_role";

grant truncate on table "public"."service_sport" to "service_role";

grant update on table "public"."service_sport" to "service_role";

grant delete on table "public"."service_student" to "anon";

grant insert on table "public"."service_student" to "anon";

grant references on table "public"."service_student" to "anon";

grant select on table "public"."service_student" to "anon";

grant trigger on table "public"."service_student" to "anon";

grant truncate on table "public"."service_student" to "anon";

grant update on table "public"."service_student" to "anon";

grant delete on table "public"."service_student" to "authenticated";

grant insert on table "public"."service_student" to "authenticated";

grant references on table "public"."service_student" to "authenticated";

grant select on table "public"."service_student" to "authenticated";

grant trigger on table "public"."service_student" to "authenticated";

grant truncate on table "public"."service_student" to "authenticated";

grant update on table "public"."service_student" to "authenticated";

grant delete on table "public"."service_student" to "service_role";

grant insert on table "public"."service_student" to "service_role";

grant references on table "public"."service_student" to "service_role";

grant select on table "public"."service_student" to "service_role";

grant trigger on table "public"."service_student" to "service_role";

grant truncate on table "public"."service_student" to "service_role";

grant update on table "public"."service_student" to "service_role";
