create type "public"."billing_type" as enum ('UNDEFINED', 'B<PERSON>ETO', 'CREDIT_CARD', 'PIX');

create type "public"."cycle" as enum ('WEEKLY', 'BIWEEKLY', 'MONTHLY', 'BIMONTHLY', 'QUARTERLY', 'SEMIANNUALLY', 'YEARLY');

create type "public"."status" as enum ('ACTIVE', 'EXPIRED', 'INACTIVE');

create table "public"."subscription_asaas" (
    "id" character varying not null,
    "createdAt" date not null default now(),
    "object" character varying,
    "customer" character varying not null,
    "paymentLink" character varying,
    "billingType" billing_type,
    "cycle" cycle not null,
    "value" numeric not null,
    "nextDueDate" date,
    "endDate" date,
    "description" character varying,
    "fineValue" numeric,
    "interestValue" numeric,
    "deleted" boolean,
    "maxPayments" smallint,
    "externalReference" character varying,
    "profileId" uuid,
    "status" status not null default 'ACTIVE'::status
);


alter table "public"."subscription_asaas" enable row level security;

CREATE UNIQUE INDEX subscription_asaas_pkey ON public.subscription_asaas USING btree (id);

alter table "public"."subscription_asaas" add constraint "subscription_asaas_pkey" PRIMARY KEY using index "subscription_asaas_pkey";

alter table "public"."subscription_asaas" add constraint "subscription_asaas_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) ON DELETE CASCADE not valid;

alter table "public"."subscription_asaas" validate constraint "subscription_asaas_profileId_fkey";

set check_function_bodies = off;

grant delete on table "public"."subscription_asaas" to "anon";

grant insert on table "public"."subscription_asaas" to "anon";

grant references on table "public"."subscription_asaas" to "anon";

grant select on table "public"."subscription_asaas" to "anon";

grant trigger on table "public"."subscription_asaas" to "anon";

grant truncate on table "public"."subscription_asaas" to "anon";

grant update on table "public"."subscription_asaas" to "anon";

grant delete on table "public"."subscription_asaas" to "authenticated";

grant insert on table "public"."subscription_asaas" to "authenticated";

grant references on table "public"."subscription_asaas" to "authenticated";

grant select on table "public"."subscription_asaas" to "authenticated";

grant trigger on table "public"."subscription_asaas" to "authenticated";

grant truncate on table "public"."subscription_asaas" to "authenticated";

grant update on table "public"."subscription_asaas" to "authenticated";

grant delete on table "public"."subscription_asaas" to "service_role";

grant insert on table "public"."subscription_asaas" to "service_role";

grant references on table "public"."subscription_asaas" to "service_role";

grant select on table "public"."subscription_asaas" to "service_role";

grant trigger on table "public"."subscription_asaas" to "service_role";

grant truncate on table "public"."subscription_asaas" to "service_role";

grant update on table "public"."subscription_asaas" to "service_role";


