alter table "public"."payment_plan_notification" add column "profileId" uuid;

alter table "public"."plan" alter column "expires" set data type date using "expires"::date;

alter table "public"."plan" alter column "name" set not null;

alter table "public"."payment_plan_notification" add constraint "payment_plan_notification_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES profile(id) not valid;

alter table "public"."payment_plan_notification" validate constraint "payment_plan_notification_profileId_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.new_payment_plan_notification()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    response RECORD;
    request_body jsonb;
BEGIN
    SELECT content::json
    INTO response
    FROM http((
        'GET',
        format(
            'https://goiurjvdlpbuvyvdwgvy.supabase.co/functions/v1/new_payment_plan_notification?paymentId=%s&profileId=%s',
            NEW.id,
            NEW."profileId"
        ),
        ARRAY[http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdvaXVyanZkbHBidXZ5dmR3Z3Z5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyNTE4NzAwMCwiZXhwIjoyMDQwNzYzMDAwfQ.etdhhLw755d9dGklsIymna9w01p3F1QWSGEFyT6BBxg'),
        http_header('Content-Type', 'application/json')
        ],
        '{"name":"Functions"}',
        NULL
    )::http_request);
    
    RAISE NOTICE 'Response: %', response;    
    RETURN NEW; 
END;$function$
;


