alter table "public"."subscription" drop constraint "subscription_userId_key";

drop view if exists "public"."bookings_count";

drop index if exists "public"."subscription_userId_key";

alter table "public"."payment_plan" add column "subscriptionId" character varying;

alter table "public"."subscription" alter column "semaphore" set not null;

alter table "public"."payment_plan" add constraint "payment_plan_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES subscription(id) not valid;

alter table "public"."payment_plan" validate constraint "payment_plan_subscriptionId_fkey";


