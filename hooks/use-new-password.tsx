'use client';

import { zodResolver, } from '@hookform/resolvers/zod';
import { useForm, } from 'react-hook-form';
import { z, } from 'zod';
import { createClient, } from '@/utils/supabase/client';
import { requiredMessage, } from '@/utils/supabase/constants';

const useNewPasswordForm = () => {
  const supabase = createClient();

  const formSchema = z.object({
    password: z.string({ required_error: requiredMessage, }).min(7, { message: 'Senha precisa ter mínimo 7 dígitos.', }),
    passwordConfirmation: z.string({ required_error: requiredMessage, }),
    }).refine((data) => data.password === data.passwordConfirmation, {
      message: 'Senhas digitadas não são iguais.',
      path: ['passwordConfirmation',],
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });

  const updatePassword = async () => {
    const { data: { user: authUser, }, } = await supabase.auth.getUser();
    const { data, error, } = await supabase.auth.updateUser({
      email: authUser?.email,
      password: form.getValues('password'),
    });
    console.log('🚀 ~ updatePassword ~ error:', error);
    console.log('🚀 ~ updatePassword ~ data:', data);
    if (data) return { data, status: 200, };
    else return { message: 'Não foi possível salvar senha.', status: 400, };
  };

  return {
    form,
    errors: {
      password: form.formState.errors.password?.message,
      passwordConfirmation: form.formState.errors.passwordConfirmation?.message,
    },
    updatePassword,
  };
};

export default useNewPasswordForm;