import { type ClassValue, clsx, } from 'clsx';
import { format, } from 'date-fns/format';
import { twMerge, } from 'tailwind-merge';
import { ptBR, } from 'date-fns/locale';
import { parse, addHours, addMinutes, subMonths, isSameMonth, isSameYear, isYesterday, parseISO, isToday, isSameWeek, isPast, isFuture, } from 'date-fns';
import { GroupedData, Profile, Schedule, TeacherSchedule, } from '@/utils/constants/types';
import axios from 'axios';
import { Task, } from '@/utils/constants';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
export const getDateInExtenseIfSameWeek = ({ date, ignoreYear, }: { date: string, ignoreYear?: boolean }) => { // Add ignoreYear parameter
  let scheduledDate = parseISO(date);
  console.log('🚀 ~ scheduledDate:', scheduledDate);

  if (isYesterday(scheduledDate)) {
    return 'Ontem';
  }

  if (isToday(scheduledDate)) {
    return 'Hoje';
  }

  const today = new Date();

  if (ignoreYear) {  // Check the ignoreYear flag
    const todayWithoutYear = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const scheduledDateWithoutYear = new Date(today.getFullYear(), scheduledDate.getMonth(), scheduledDate.getDate());
    console.log('🚀 ~ scheduledDateWithoutYear:', scheduledDateWithoutYear);
    console.log('🚀 ~ todayWithoutYear:', todayWithoutYear);

    if (isSameWeek(todayWithoutYear, scheduledDateWithoutYear, { weekStartsOn: 1, }) && !isPast(scheduledDateWithoutYear)) {
      return format(scheduledDateWithoutYear, 'EEEE', { locale: ptBR, });
    } else {
      return format(scheduledDateWithoutYear, 'dd/MM');
    }
  } else { // If ignoreYear is false, do the original comparison
    if (isSameWeek(today, scheduledDate, { weekStartsOn: 1, }) && !isPast(scheduledDate)) {
      return format(scheduledDate, 'EEEE', { locale: ptBR, });
    } else {
      return format(scheduledDate, 'dd/MM');
    }
  }
};

export const formatDate = (input: string) => {
  const cleaned = input.replace(/\D/g, '');
  let formatted = '';
  for (let i = 0; i < cleaned.length && i < 8; i++) {
    if (i === 2 || i === 4) formatted += '/';
    formatted += cleaned[i];
  }
  return formatted;
};

export const formatFromBrDate = (date: string) => {
  return format(parse(date, 'dd/MM/yyyy', new Date()), 'yyyy-MM-dd');
};

export const formatCep = (cep: string) => {
  const inputValue = cep.replace(/\D/g, '');
  return inputValue.replace(/(\d{5})(\d{3})/, '$1-$2');
};

export const formatPhoneNumber = (value: string) => {
  const numbers = value.replace(/\D/g, '');
  const char = { 0: '(', 2: ')', 7: '-', };
  let formatted = '';

  for (let i = 0; i < numbers.length && i < 11; i++) {
    if (i in char) formatted += char[i as keyof typeof char];
    formatted += numbers[i];
  }

  return formatted;
};

export const generateRandomString = () => {
  // Create arrays for letters and numbers
  const letters = 'abcdefghijklmnopqrstuvwxyz'.split('');
  const numbers = '0123456789'.split('');

  // Initialize empty string
  let randomString = '';

  // Generate first 3 letters
  for (let i = 0; i < 3; i++) {
    randomString += letters[Math.floor(Math.random() * letters.length)];
  }

  // Generate last 3 numbers
  for (let i = 0; i < 2; i++) {
    randomString += numbers[Math.floor(Math.random() * numbers.length)];
  }

  return randomString;
};

export const groupBookings = (data: { day: string; schedule: Schedule }[]): GroupedData => {
  return data.reduce((acc, item) => {
    const { day, schedule: { id, }, } = item;

    if (!acc[day]) acc[day] = {};

    if (!acc[day][id]) acc[day][id] = 0;

    acc[day][id]++;

    return acc;
  }, {} as GroupedData);
};

export const groupTeachersSchedule = (data: TeacherSchedule[]): GroupedData => {
  return data.reduce((acc, schedule) => {
    const { day, scheduleId, limit, } = schedule;

    if (!acc[day]) acc[day] = {};

    if (!acc[day][scheduleId]) acc[day][scheduleId] = 0;

    acc[day][scheduleId] += limit;

    return acc;
  }, {} as GroupedData);
};

export const compareAndFilter = (obj1: GroupedData, obj2: GroupedData) => {
  const result: GroupedData = {};

  for (const day in obj1) {
    if (obj1.hasOwnProperty(day)) {
      if (obj2.hasOwnProperty(day)) {
        result[day] = {};
        for (const scheduleId in obj1[day]) {
          if (obj1[day].hasOwnProperty(scheduleId)) {
            if (!obj2[day].hasOwnProperty(scheduleId) || obj1[day][scheduleId] > obj2[day][scheduleId]) {
              result[day][scheduleId] = obj1[day][scheduleId];
            }
          }
        }
        if (Object.keys(result[day]).length === 0) {
          delete result[day]; // Remove empty day objects
        }
      } else {
        result[day] = { ...obj1[day], }; // If day doesn't exist in obj2, copy all from obj1
      }
    }
  }

  return result as GroupedData;
};

export const ax = axios.create({
  validateStatus: (status) => status < 500, // Allow 4xx errors
});

export const calculateTimeDifferenceInHours = (givenDate) => {
  // Get the current date and time
  const now = new Date();
  now.setUTCHours(now.getUTCHours() - 3);

  // Calculate the time difference in milliseconds
  const timeDifferenceMs = now - givenDate;

  // Convert milliseconds to hours
  return timeDifferenceMs / (1000 * 60 * 60);
};

export const groupedHours = (data) => {
  return data.reduce((acc, item) => {
    const existingNumber = acc.find(number => number.number === item.number);

    if (existingNumber) {
      existingNumber.hours.push(item.hour);
    } else {
      acc.push({ number: item.number, hours: [item.hour,], limit: item.limit, });
    }

    return acc;
  }, [] as { number: number; hours: string[]; limit: number }[])
  .sort((a, b) => a.number - b.number);
};

export const formatScheduleData = (schedules: Schedule[]) => {
  const formattedSchedules: { [day: number]: string[] } = {};

  for (const schedule of schedules) {
    if (!formattedSchedules[schedule.number]) {
      formattedSchedules[schedule.number] = [];
    }
    formattedSchedules[schedule.number].push(schedule.hour);
  }

  for (const day in formattedSchedules) {
    formattedSchedules[day].sort((a, b) => {
      const [hoursA, minutesA,] = a.split(':').map(Number);
      const [hoursB, minutesB,] = b.split(':').map(Number);

      if (hoursA !== hoursB) {
        return hoursA - hoursB;
      } else {
        return minutesA - minutesB;
      }
    });
  }

  return formattedSchedules;
};

export const getDayAndHourGlobal = (booking: { day: string, schedule: { hour: string } }) => {
  const [hours, minutes,] = booking.schedule.hour.split(':');
  const formattedDay = addMinutes(addHours(booking.day, parseInt(hours)), parseInt(minutes));
  return formattedDay;
};

export const groupTransactionsByMonth = (paymentsPackThisYear: Task[], paymentsPackLastYear?: Task[]) => {
  const chartData = [];

  // Loop through all months (January to December)
  for (let month = 0; month < 12; month++) {
    const monthName = new Date(2024, month, 1).toLocaleDateString('pt-BR', { month: 'long', }); // Adjust format as needed

    let lastYearSum = 0;
    let currentYearSum = 0;

    if (paymentsPackLastYear) {
      // Find sum of transactionAmount for last year
      for (const payment of paymentsPackLastYear) {
        const paymentMonth = new Date(payment.paymentDate).getMonth();
        if (paymentMonth === month) {
          lastYearSum += payment.netValue;
        }
      }
    }

    // Find sum of transactionAmount for current year
    for (const payment of paymentsPackThisYear) {
      const paymentMonth = new Date(payment.paymentDate).getMonth();
      if (paymentMonth === month) {
        currentYearSum += payment.netValue;
      }
    }

    if (paymentsPackLastYear) {
      // Add data for this month to chartData
      chartData.push({ month: monthName, lastYear: lastYearSum, current: currentYearSum, });
    } else {
      chartData.push({ month: monthName, current: currentYearSum, });
    }
  }

  return chartData;
};

export const isLastMonth = (date) => {
  const today = new Date();
  const lastMonthStart = subMonths(today, 1);

  return isSameMonth(date, lastMonthStart) && isSameYear(date, lastMonthStart);
};

export const validateCPF = (cpf: string) => {

  if (!cpf || cpf.length === 0) return true;

  // Remove todos os caracteres não numéricos
  cpf = cpf.replace(/\D/g, '');

  // Verifica se o CPF tem 11 dígitos
  if (cpf.length !== 11) {
    return false;
  }

  // Calcula os dígitos verificadores
  let soma = 0;
  let resto;
  for (let i = 1; i <= 9; i++) {
    soma += parseInt(cpf.substring(i - 1, i)) * (11 - i);
  }
  resto = (soma * 10) % 11;
  if (resto === 10 || resto === 11) {
    resto = 0;
  }
  if (resto !== parseInt(cpf.substring(9, 10))) {
    return false;
  }

  soma = 0;
  for (let i = 1; i <= 10; i++) {
    soma += parseInt(cpf.substring(i - 1, i)) * (12 - i);
  }
  resto = (soma * 10) % 11;
  if (resto === 10 || resto === 11) {
    resto = 0;
  }
  if (resto !== parseInt(cpf.substring(10, 11))) {
    return false;

  }

  // Se chegou até aqui, o CPF é válido
  return true;
};

export const parseBrazilianReal = (realValue: string) => {
  const cleanValue = realValue.replace(/[R$.,\s]/g, '');
  return cleanValue.replace(/(\d{2})$/, '.$1');
};

export const formatToBrazilianReal = (numericValue: number | string) => {
  const formattedValue = numericValue.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL', });
  return formattedValue;
};

export const recurrencyLabel = (frequency: number, frequencyType: string) => {
  switch (true) {
    case frequency === 1 && frequencyType === 'months':
      return 'Mensal';
    case frequency === 7 && frequencyType === 'days':
      return 'Semanal';
    case frequency === 15 && frequencyType === 'days':
      return 'Quinzenal';
    case frequency === 2 && frequencyType === 'months':
      return 'Bimestral';
    case frequency === 3 && frequencyType === 'months':
      return 'Trimestral';
    case frequency === 4 && frequencyType === 'months':
      return 'Quadrimestral';
    case frequency === 6 && frequencyType === 'months':
      return 'Semestral';
    case frequency === 12 && frequencyType === 'months':
      return 'Anual';
    default:
      return 'Mensal';
  }
};

export const getCycle = (frequency: number, frequencyType: string) => {
  switch (true) {
    case frequency === 1 && frequencyType === 'months':
      return 'MONTHLY';
    case frequency === 7 && frequencyType === 'days':
      return 'WEEKLY';
    case frequency === 15 && frequencyType === 'days':
      return 'BIWEEKLY';
    case frequency === 2 && frequencyType === 'months':
      return 'BIMONTHLY';
    case frequency === 4 && frequencyType === 'months':
      return 'QUARTERLY';
    case frequency === 6 && frequencyType === 'months':
      return 'SEMIANNUALLY';
    case frequency === 12 && frequencyType === 'months':
      return 'YEARLY';
    default:
      return 'MONTHLY';
  }
};

export const nextDateWithDay = (day: number) => {
  /**
   * Given a day of the month (1-31), finds the date of the next occurrence of that day.
   * @param {number} day The day of the month (1-31).
   * @returns {string|null} A string representing the date in the format "YYYY-MM-DD" or null if the input is invalid.
   */
  if (day < 1 || day > 28) {
    return null; // Handle invalid input
  }

  const today = new Date();
  let currentYear = today.getFullYear();
  let currentMonth = today.getMonth() + 1; // Month is 0-indexed

  // Try the current month first
  try {
    const nextDate = new Date(currentYear, currentMonth - 1, day); // Month is 0-indexed in Date constructor
    if (nextDate >= today) {
      const year = nextDate.getFullYear();
      const month = String(nextDate.getMonth() + 1).padStart(2, '0'); // Add leading zero if needed
      const day = String(nextDate.getDate()).padStart(2, '0'); // Add leading zero if needed
      return `${year}-${month}-${day}`;
    }
  } catch (error) {
    // Handle invalid dates for the current month
  }

  // If the day is in the past of the current month, try next months
  for (let monthOffset = 1; monthOffset <= 12; monthOffset++) {
    let nextMonth = (currentMonth + monthOffset - 1) % 12 + 1;
    let nextYear = currentYear + Math.floor((currentMonth + monthOffset - 1) / 12);
    try {
      const nextDate = new Date(nextYear, nextMonth - 1, day);
      const year = nextDate.getFullYear();
      const month = String(nextDate.getMonth() + 1).padStart(2, '0'); // Add leading zero if needed
      const formattedDay = String(nextDate.getDate()).padStart(2, '0'); // Add leading zero if needed
      return `${year}-${month}-${formattedDay}`;
    } catch (error) {
      // Handle invalid dates like 30 of february
    }
  }

  return null; // If no date is found after trying all months
};

export const toPercentage = (numerator: number, denominator: number) => {
  if (denominator === 0) {
    return 'Cannot divide by zero'; // Handle division by zero
  }
  const decimal = numerator / denominator;
  const percentage = decimal * 100;
  return percentage.toFixed(2) + '%';
};

export const handleCpfChange = (value: string) => {
  let cpf = value.replace(/[^\d]/g, '');
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
};

export const getHoursOfSchedule = (schedules: { [day: number]: string[] }) => {
  const uniqueHours = new Set();

  for (const day in schedules) {
    if (schedules[day]) {
      for (const hour of schedules[day]) {
        uniqueHours.add(hour);
      }
    }
  }

  // Sort the hours correctly
  const sortedHours = Array.from(uniqueHours).sort((a, b) => {
    const [hoursA, minutesA,] = a.split(':').map(Number);
    const [hoursB, minutesB,] = b.split(':').map(Number);

    if (hoursA !== hoursB) {
      return hoursA - hoursB;
    }
    return minutesA - minutesB;
  });

  return sortedHours.join(', ');
};

export const groupData = (data: (TeacherSchedule & { schedule: Schedule, profile: Profile })[]): { profiles: Profile[], schedules: Schedule[] } => {
  const uniqueProfiles = {} as Profile[];
  const uniqueSchedules = {} as Schedule[];

  data.forEach(item => {
    const profileId = item.profile.id;
    const scheduleId = item.schedule.id;

    if (!uniqueProfiles[profileId]) {
      uniqueProfiles[profileId] = item.profile;
    }

    let scheduleDateAndHour = `${item.day}T${item.schedule.hour}.0Z`;
    scheduleDateAndHour = parseISO(scheduleDateAndHour);
    scheduleDateAndHour = addHours(scheduleDateAndHour, 3);

    if (!uniqueSchedules[scheduleId] && isFuture(scheduleDateAndHour)) {
      uniqueSchedules[scheduleId] = item.schedule;
    }
  });

  // Convert objects to arrays if needed:
  const profilesArray = Object.values(uniqueProfiles);
  const schedulesArray = Object.values(uniqueSchedules);

  return {
    profiles: profilesArray,
    schedules: schedulesArray,
  };
};

export const getToday = (): Date => {
  const today = new Date();
  today.setUTCHours(today.getUTCHours() - 3);
  return today;
};

export const addLeadingZero = (timeStr: string) => {
  const parts = timeStr.split(':');
  if (parts.length === 2 && parts[0].length === 1 && /^\d$/.test(parts[0])) {
    return '0' + timeStr;
  }
  return timeStr;
};

export const getHoursMinusThree = () => {
  const now = new Date();
  let currentHours = now.getHours();

  // Subtract 3 hours
  currentHours -= 3;

  // Handle negative hours (wrap around to the previous day)
  if (currentHours < 0) {
    currentHours += 24;
  }

  return currentHours;
};