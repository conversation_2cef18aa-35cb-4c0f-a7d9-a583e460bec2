type ErrorMessages = {
  [key: string]: string
}

const errorMessages: ErrorMessages = {
  'duplicate key value violates unique constraint "subscription_pkey"': 'Inscrição já existe.',
  'duplicate key value violates unique constraint "user_email_key"': 'Usuário já cadastrado.',
  'duplicate key value violates unique constraint "custom_user_email_key"': 'Usuário já cadastrado.',
  'duplicate key value violates unique constraint "booking_service_user"': 'Voce ja tem agendamento nessa data e horario.',
  'New password should be different from the old password.': 'Senha nova não pode ser igual a senha antiga.',
  'duplicate key value violates unique constraint "course_slug"': 'Já existe um curso com esse nome.',
  'duplicate key value violates unique constraint "lesson_slug"': 'Já existe uma aula com esse nome.',
  'duplicate key value violates unique constraint "pkg_profile_profileid_pkgid_active_unique"': 'Usuário tem esse pacote ativo.',
  2067: 'CPF inválido.',
  4049: 'Valor inválido.',
  cc_rejected_bad_filled_security_code: 'Código de segurança inválido',
  cc_rejected_bad_filled_other: 'Revise os dados.',
  bank_error: 'Erro com o banco.',
  cc_rejected_bad_filled_card_number: 'Revise o número do cartão.',
  cc_rejected_bad_filled_date: 'Revise a data de vencimento.',
  cc_rejected_card_error: 'Não conseguimos processar o pagamento.',
  cc_rejected_duplicated_payment: 'Você já efetuou um pagamento com esse valor. Caso precise pagar novamente, utilize outro cartão ou outra forma de pagamento.',
  cc_rejected_high_risk: 'Seu pagamento foi recusado.',
  cc_rejected_insufficient_amount: 'Saldo insuficiente.',
  cc_rejected_invalid_installments: 'Não aceitamos parcelamentos.',
  cc_rejected_max_attempts: 'Você atingiu o limite de tentativas permitido.',
  rejected_by_bank: 'Recusado pelo banco.',
  insufficient_amount: 'Pagamento rejeitado por valores insuficientes.',
  cc_rejected_card_type_not_allowed: 'Sem função de crédito no cartão fornecido.',
  invalid_credentials: 'Email ou senha incorretos.',
};

export default errorMessages;