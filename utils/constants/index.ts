import {
  CalendarRange,
  ShoppingCart,
  BookOpen,
  UserPlus,
  SquareUserRound,
  ListChecks,
  ChartBar,
  LayoutDashboard,
  PencilLine,
  Package,
  Repeat,
  HandPlatter,
  ArrowDownUp,
  Webhook,
  Newspaper,
  LucideIcon,
} from 'lucide-react';
import { Variants, } from './types';

export const subscriptions = {
  full: 3,
  medium: 2,
  basic: 1,
};

export const LESSON_STATUS = {
  COMPLETE: 'complete',
  NOT_STARTED: 'not_started',
  IN_PROGRESS: 'in_progress',
};

export const adminAccessible = [
  '/convidar',
];

export const daysOfWeek = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sabado',];
export const daysOfWeekStartingMonday = ['Segunda', 'Terça', 'Quarta', 'Quinta', 'Sex<PERSON>', 'Sabado', 'Domingo',];
export const hours = ['05:00', '05:30', '05:45', '06:00', '06:30', '06:45', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:15', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00',];
export const months = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro',];

export const linksAdmin: { icon: LucideIcon, href: string, label: string }[] = [
  {
    icon: LayoutDashboard,
    href: '/painel',
    label: 'Painel',
  },
  {
    icon: ArrowDownUp,
    href: '/fluxo',
    label: 'Fluxo de caixa',
  },
  {
    icon: BookOpen,
    href: '/cursos',
    label: 'Meus Cursos',
  },
  {
    icon: SquareUserRound,
    href: '/alunos',
    label: 'Alunos',
  },
  {
    icon: SquareUserRound,
    href: '/professores',
    label: 'Professores',
  },
  {
    icon: ListChecks,
    href: '/presenca',
    label: 'Presença',
  },
  {
    icon: ChartBar,
    href: '/frequencia',
    label: 'Frequência',
  },
  {
    icon: CalendarRange,
    href: '/agendamentos',
    label: 'Agendamentos',
  },
  {
    icon: UserPlus,
    href: '/convidar',
    label: 'Convidar',
  },
  {
    icon: HandPlatter,
    href: '/servicos',
    label: 'Serviços',
  },
  {
    icon: Repeat,
    href: '/planos',
    label: 'Planos',
  },
  {
    icon: Package,
    href: '/pacotes',
    label: 'Pacotes',
  },
  {
    icon: CalendarRange,
    href: '/escala',
    label: 'Escala',
  },
  {
    icon: Webhook,
    href: '/graduacao',
    label: 'Graduação',
  },
  {
    icon: Newspaper,
    href: '/contratos',
    label: 'Contratos',
  },
  {
    icon: PencilLine,
    href: '/criar-venda',
    label: 'Venda Avulsa',
  },
  {
    icon: PencilLine,
    href: '/criar-despesa',
    label: 'Criar Despesa',
  },
  {
    icon: PencilLine,
    href: '/criar-curso',
    label: 'Criar Curso',
  },
  {
    icon: PencilLine,
    href: '/criar-pacote',
    label: 'Criar Pacote',
  },
  {
    icon: PencilLine,
    href: '/criar-prospecto',
    label: 'Criar Prospecto',
  },
];

export const linksStudent: { icon: LucideIcon, href: string, label: string } = [
  {
    icon: BookOpen,
    href: '/meus-cursos',
    label: 'Meus Cursos',
  },
  {
    icon: ShoppingCart,
    href: '/cursos',
    label: 'Cursos',
  },
  {
    icon: CalendarRange,
    href: '/agendar',
    label: 'Agendar',
  },
  {
    icon: CalendarRange,
    href: '/meus-agendamentos',
    label: 'Agendamentos',
  },
  {
    icon: Repeat,
    href: '/planos',
    label: 'Assinar Planos',
  },
  {
    icon: Package,
    href: '/pacotes',
    label: 'Comprar Pacotes',
  },
];

export const linksTeacher: { icon: LucideIcon, href: string, label: string } = [
  {
    icon: BookOpen,
    href: '/meus-cursos',
    label: 'Meus Cursos',
  },
  {
    icon: ShoppingCart,
    href: '/cursos',
    label: 'Cursos',
  },
  {
    icon: ListChecks,
    href: '/presenca',
    label: 'Presença',
  },
  {
    icon: CalendarRange,
    href: '/agendar',
    label: 'Agendar',
  },
  {
    icon: CalendarRange,
    href: '/agendamentos',
    label: 'Agendamentos',
  },
  {
    icon: Repeat,
    href: '/planos',
    label: 'Planos',
  },
  {
    icon: Package,
    href: '/pacotes',
    label: 'Pacotes',
  },
];

export const UNKNOWN_ERROR = 'Algum erro desconhecido aconteceu.';
export const UNAUTHORIZED = 'Sem permissão de acesso.';
export const MISSING_PARAMS = 'Faltam informações.';

export const bookingStatuses: { [key: string]: {
  color: Variants,
  value: 'approved' | 'bailed' | 'canceled' | 'expired' | 'missed' | 'pending' | 'used',
  label: string,
} } = {
  pending: {
    color: 'yellow',
    value: 'pending',
    label: 'Pendente',
  },
  approved: {
    color: 'green',
    value: 'approved',
    label: 'Confirmado',
  },
  bailed: {
    color: 'red',
    value: 'bailed',
    label: 'Desistência',
  },
  canceled: {
    color: 'red',
    value: 'canceled',
    label: 'Cancelado',
  },
  expired: {
    color: 'secondary',
    value: 'expired',
    label: 'Expirado',
  },
  missed: {
    color: 'purple',
    value: 'missed',
    label: 'Falta',
  },
  used: {
    color: 'blue',
    value: 'used',
    label: 'Usado',
  },
};

export const paymentStatuses: { [key: string]: {
  color: Variants,
  value: 'received' | 'confirmed' | 'pending' | 'overdue' | 'paid',
  label: string,
} } = {
  pending: {
    color: 'yellow',
    value: 'pending',
    label: 'Pendente',
  },
  received: {
    color: 'green',
    value: 'received',
    label: 'Recebido',
  },
  paid: {
    color: 'green',
    value: 'paid',
    label: 'Pago',
  },
  confirmed: {
    color: 'green',
    value: 'confirmed',
    label: 'Confirmado',
  },
  overdue: {
    color: 'red',
    value: 'overdue',
    label: 'Vencido',
  },
};

export const studentStatuses: { [key: string]: {
  color: Variants,
  value: 'pending' | 'active' | 'inactive',
  label: string,
} } = {
  pending: {
    color: 'yellow',
    value: 'pending',
    label: 'Pendente',
  },
  inactive: {
    color: 'red',
    value: 'inactive',
    label: 'Inativo',
  },
  active: {
    color: 'green',
    value: 'active',
    label: 'Ativo',
  },
};

export const panelVisualizeLabels = {
  status: 'Status',
  transactionAmount: 'Valor',
  dateApproved: 'Data',
  type: 'Tipo',
  profile: 'Aluno',
  product: 'Nome',
};

export const panelFlowVisualizeLabels = {
  status: 'Status',
  netValue: 'Valor',
  paymentDate: 'Data',
  type: 'Tipo',
  name: 'Pagamento',
};

export const bookingsPanelLabels = {
  code: 'Código',
  profile: 'Aluno',
  status: 'Status',
  day: 'Dia',
  schedule: 'Hora',
};

export const studentsLabels = {
  name: 'Aluno',
  phone: 'Telefone',
  birthdate: 'Nascimento',
  status: 'Status',
};

import { z, } from 'zod';

// We're keeping a simple non-relational schema here.
// IRL, you will have a schema for your data models.
export const taskSchema = z.object({
  code: z.string(),
  profile: z.object({
    name: z.string(),
  }),
  status: z.string(),
  day: z.string(),
  schedule: z.object({
    hour: z.string(),
  }),
});

export type Task = z.infer<typeof taskSchema>