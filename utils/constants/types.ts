import { Tables, } from '@/database.types';

export type UserType = 'student' | 'admin' | 'teacher';
export type ProgressTypes = 'complete' | 'not_started' | 'in_progress';
export type Subdomains = 'dev' | 'raphaelpaiva' | 'fassola' | 'pntr' | 'globalfight' | 'bl' | 'pedrosagat' | 'snakes' | 'prod';
export type Cycles = 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY' | 'BIMONTHLY' | 'QUARTERLY' | 'SEMIANNUALLY' | 'YEARLY';
export type BillingType = 'UNDEFINED' | 'BOLETO' | 'CREDIT_CARD' | 'PIX';
export type Params = {
  params: {
    domain: Subdomains
  }
};
export type SchoolInfo = {
  [key in Subdomains]: {
    name: string,
    id: string,
    domain: string
  };
};

export type CreditCardMP = {
  additional_info: {
    request_public: string;
    api_client_application: string;
    api_client_scope: string;
  };
  cardholder: {
    name: string;
    identification: {
      number: string;
      type: 'CPF';
    };
  };
  customer_id: string;
  date_created: string; // Date string in ISO format (e.g., "2024-10-03T12:35:50.000-04:00")
  date_last_updated: string; // Date string in ISO format (e.g., "2024-12-07T19:51:05.540-04:00")
  expiration_month: number;
  expiration_year: number;
  first_six_digits: string;
  id: string;
  issuer: {
    id: number;
    name: string;
  };
  last_four_digits: string;
  live_mode: boolean;
  payment_method: {
    id: string;
    name: string;
    payment_type_id: string;
    thumbnail: string;
    secure_thumbnail: string;
  };
  security_code: {
    length: number;
    card_location: string;
  };
  user_id: string;
}

export type CreditCardToken = {
  id: string,
  cardHolderName: string,
  expirationYear: string,
  expirationMonth: string,
  lastFourDigits: string
}

export type GroupedData = {
  [day: string]: {
    [id: number]: number ;
  };
}

export type EquipmentCheckbox = Tables<'equipment'> & { selected: boolean }

export type ProfileCheckbox = Tables<'profile'> & { selected: boolean }

export type Variants = 'blue' | 'red' | 'green' | 'purple' | 'yellow' | 'default' | 'secondary' | 'destructive' | 'outline' | null | undefined;

/** Database basic tipes */

export type Attendance = Tables<'attendance'>

export type BaseRank = Tables<'base_rank'>

export type BookingEquipment = Tables<'booking_equipment'>

export type Booking = Tables<'booking'>

export type Category = Tables<'category'>

export type Course = Tables<'course'>

export type Contract = Tables<'contract'>

export type Debt = Tables<'debt'>

export type DebtRoot = Tables<'debt_root'>

export type Equipment = Tables<'equipment'>

export type Face = Tables<'face'>

export type Lead = Tables<'lead'>

export type Lesson = Tables<'lesson'>

export type Module = Tables<'module'>

export type Progress = Tables<'progress'>

export type Profile = Tables<'profile'>

export type Pack = Tables<'pack'>

export type PackProfile = Tables<'pack_profile'>

export type PackService = Tables<'service_pack'>

export type Payment = Tables<'payment'>

export type Plan = Tables<'plan'>

export type PlanService = Tables<'service_plan'>

export type Rank = Tables<'rank'>

export type RankPoint = Tables<'rank_point'>

export type RankSchool = Tables<'rank_school'>

export type RankStudent = Tables<'rank_student'>

export type Sale = Tables<'sale'>

export type SaleRoot = Tables<'sale_root'>

export type School = Tables<'school'>

export type SchoolSettings = Tables<'school_settings'>

export type Service = Tables<'service'>

export type ServiceStudent = Tables<'service_student'>

export type ServiceEquipment = Tables<'service_equipment'>

export type ServiceTeacher = Tables<'service_teacher'>

export type Schedule = Tables<'schedule'>

export type Subscription = Tables<'subscription'>

export type Sport = Tables<'sport'>

export type SportSchool = Tables<'sport_school'>

export type SportProfile = Tables<'sport_profile'>

export type TeacherSchedule = Tables<'teacher_schedule'>

export type Video = Tables<'video'>

export type CustomSubscription = Subscription & { plan: Plan }

/** MP types */

export interface MercadoPagoPlan {
  id: string;
  back_url: string;
  collector_id: number;
  application_id: number;
  reason: string;
  status: 'active' | string; // Allow for potential future statuses
  external_reference: string;
  date_created: string; // ISO 8601 date string
  last_modified: string; // ISO 8601 date string
  init_point: string;
  auto_recurring: MercadoPagoAutoRecurring;
  payment_methods_allowed: MercadoPagoPaymentMethodsAllowed;
  repetitions?: number;
}

export interface MercadoPagoAutoRecurring {
  frequency: number;
  frequency_type: 'months'; // Mercado Pago currently only supports months
  transaction_amount: number;
  currency_id: string; // Typically "BRL" for Brazilian Real
  repetitions: number;
  billing_day: number;
  billing_day_proportional: boolean;
  transaction_amount_proportional?: number; // Optional field
}

export interface MercadoPagoPaymentMethodsAllowed {
  payment_types: Array<{ id: string }>; // Array of objects with "id" property
  payment_methods: Array<string>; // Array of payment method strings (optional)
}
