export default {
  APPROVED: 'approved',
  PENDING: 'in_process',
  REJECTED: 'rejected',
  REJECT_REASONS: {
    cc_rejected_bad_filled_card_number: 'Revise o número do cartão.',
    cc_rejected_bad_filled_date: 'Revise a data de vencimento.',
    cc_rejected_bad_filled_other: 'Revise os dados.',
    cc_rejected_bad_filled_security_code: 'Revise o código de segurança do cartão.',
    cc_rejected_blacklist: 'Não pudemos processar seu pagamento.',
    cc_rejected_call_for_authorize: 'Você deve autorizar ao operadora o pagamento do valor ao Mercado Pago.',
    cc_rejected_card_disabled: 'Ligue para a operadora para ativar seu cartão. O telefone está no verso do seu cartão.',
    cc_rejected_card_error: 'Não conseguimos processar seu pagamento.',
    cc_rejected_duplicated_payment: 'Você já efetuou um pagamento com esse valor. Caso precise pagar novamente, utilize outro cartão ou outra forma de pagamento.',
    cc_rejected_high_risk: 'Escolha outra forma de pagamento. Recomendamos meios de pagamento em dinheiro.',
    cc_rejected_insufficient_amount: 'Sem saldo suficiente.',
    cc_rejected_invalid_installments: 'O método de pagamento não permite parcelas.',
    cc_rejected_max_attempts: 'Escolha outro cartão ou outra forma de pagamento.',
    cc_rejected_other_reason: 'Não foi possível processar o pagamento',
    cc_rejected_card_type_not_allowed: 'O pagamento foi rejeitado porque o usuário não tem a função crédito habilitada em seu cartão multiplo (débito e crédito).',
  },
};