import { faCircleCheck, faCircleDollarToSlot, faCircleExclamation, faCircleXmark, } from '@fortawesome/free-solid-svg-icons';

export const URLS = {
  ATTENDANCE: '/api/attendance',
  ATTENDANCE_REMOVE: '/api/attendance/remove',
  ATTENDANCE_TODAY: '/api/attendance/today',
  BOOKING_STATUS: '/api/booking/status',
  BOOKING_CANCEL: '/api/booking/cancel',
  BOOKING_UPDATE: '/api/booking/update',
  BOOK_SERVICE: '/api/booking/create',
  BRASIL_ABERTO: 'https://brasilapi.com.br/api/cep/v1',
  CARD: '/api/card',
  COURSE: {
    LESSON: '/api/course/lesson',
    PROGRESS: '/api/course/progress',
  },
  EQUIPMENTS: '/api/equipments',
  EQUIPMENTS_AVAILABLE: '/api/equipments/available',
  FACE_API: 'https://justadudewhohacks.github.io/face-api.js/models/',
  SCHEDULE: '/api/schedule',
  SCHEDULE_TEACHER: '/api/schedule/teacher',
  SCHEDULE_AVAILABLE: '/api/schedule/available',
  SERVICE: '/api/service',
  SERVICE_CREATE: '/api/service/create',
  SERVICE_UPDATE: '/api/service/update',
  SUBSCRIPTION: '/api/subscription',
  SUBSCRIPTION_MP: '/api/subscription/mp',
  STUDENTS: '/api/students',
  STUDENT_STATUS: '/api/students/status',
  USER: {
    BASE: '/api/user',
    FACE: '/api/user/face',
    UPDATE: '/api/user/update',
    EMAIL: '/api/user/emails',
    TEACHERS: '/api/user/teachers',
  },
  PURCHASE: '/api/plans/purchase',
};

export const weekDaysDictionary = {
  Domingo: 0,
  Segunda: 1,
  Terça: 2,
  Quarta: 3,
  Quinta: 4,
  Sexta: 5,
  Sabado: 6,
};

export const weekDaysDictionaryExtense = {
  domingo: 0,
  'segunda-feira': 1,
  'terça-feira': 2,
  'quarta-feira': 3,
  'quinta-feira': 4,
  'sexta-feira': 5,
  'sábado': 6,
};

export const BOOKING_STATUS = {
  APPROVED: 'Confirmado',
  CANCELED: 'Cancelado',
  PENDING: 'Pendente',
  EXPIRED: 'Vencido',
};

export const bookingStatusAdminEditableOnly = ['approved', 'missed', 'pending', 'used',];

export const SubscriptionType = {
  free: 'Gratuito',
  payed: 'Pago',
};

export const subscriptionStatuses: { [type: string]: { label: string, color: string, icon: any } } = {
  ACTIVE: {
    icon: faCircleCheck,
    label: 'Assinatura com um meio de pagamento válido.',
    color: '#4ade80',
  },
  EXPIRED: {
    icon: faCircleXmark,
    label: 'Assinatura finalizada. Este status é irreversível.',
    color: '#f87171',
  },
  INACTIVE: {
    icon: faCircleExclamation,
    label: 'Assinatura sem um meio de pagamento.',
    color : '#fb923c',
  },
};

export const semaphoreStatus = {
  green: {
    icon: faCircleCheck,
    label: 'Todas as cobranças feitas.',
    color: '#4ade80',
  },
  yellow: {
    icon: faCircleExclamation,
    label: 'Estamos tentando cobrar uma fatura.',
    color: '#fb923c',
  },
  red: {
    icon: faCircleXmark,
    label: 'Uma fatura não foi cobrada.',
    color: '#f87171',
  },
  blank: {
    icon: faCircleDollarToSlot,
    label: 'Cobrança com desconto.',
    color: 'secondary',
  },
};

export const equipmentGroups = {
  'Jiu Jitsu': [
    'Kimono',
    'Faixa',
  ],
  Surf: [
    'Estrepe',
    'Parafina',
    'Prancha',
  ],
  Boxe: [
    'Luva',
    'Capacete',
    'Caneleira',
  ],
  'Beach Tênis': [
    'Raquete',
  ],
};

export const accountedForBookingStatus = ['approved', 'bailed', 'expired', 'missed', 'pending', 'used', ];

export const CancelationWindow = [
  { label: 'Qualquer momento antes da aula', hour: '0', },
  { label: '1 hora de antecedência', hour: '1', },
  { label: '2 horas de antecedência', hour: '2', },
  { label: '6 horas de antecedência', hour: '6', },
  { label: '12 horas de antecedência', hour: '12', },
  { label: '24 horas de antecedência', hour: '24', },
  { label: 'Sem reembolso', hour: '20000', },
];

type PaymentMethods = { [key: string]: string }

export const paymentMethodsPt: PaymentMethods = {
  BOLETO: 'Boleto',
  CREDIT_CARD: 'Cartão',
  PIX: 'Pix',
};

export const requiredMessage = 'Campo obrigatório.';