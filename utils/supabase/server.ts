import { createServerClient, } from '@supabase/ssr';
import { createClient as createClientSupabase, } from '@supabase/supabase-js';
import { cookies, } from 'next/headers';
import { Database, } from '@/database.types';

export function createClient() {
  const cookieStore = cookies();

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options, }) =>
              cookieStore.set(name, value, options)
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );
}

export function createClientAdmin() {
  return createClientSupabase(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_ROLE_KEY! );
}