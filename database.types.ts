export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      attendance: {
        Row: {
          confirmed: boolean | null
          createdAt: string
          date: string
          id: string
          scheduleId: string
          serviceId: string
          userId: string
        }
        Insert: {
          confirmed?: boolean | null
          createdAt?: string
          date: string
          id?: string
          scheduleId: string
          serviceId: string
          userId: string
        }
        Update: {
          confirmed?: boolean | null
          createdAt?: string
          date?: string
          id?: string
          scheduleId?: string
          serviceId?: string
          userId?: string
        }
        Relationships: [
          {
            foreignKeyName: "attendance_scheduleId_fkey"
            columns: ["scheduleId"]
            isOneToOne: false
            referencedRelation: "schedule"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_userId_fkey"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
        ]
      }
      base_rank: {
        Row: {
          color: string | null
          createdAt: string
          id: string
          name: string | null
          sportId: string | null
          stripes: number | null
        }
        Insert: {
          color?: string | null
          createdAt?: string
          id?: string
          name?: string | null
          sportId?: string | null
          stripes?: number | null
        }
        Update: {
          color?: string | null
          createdAt?: string
          id?: string
          name?: string | null
          sportId?: string | null
          stripes?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "base_rank_sportId_fkey"
            columns: ["sportId"]
            isOneToOne: false
            referencedRelation: "sport"
            referencedColumns: ["id"]
          },
        ]
      }
      booking: {
        Row: {
          cancelable: boolean | null
          code: string
          createdAt: string
          day: string
          details: string | null
          id: string
          leadId: string | null
          packId: string | null
          packProfileId: number | null
          scheduleId: string
          serviceId: string
          status: Database["public"]["Enums"]["booking_status"]
          teacherId: string | null
          teacherScheduleId: string | null
          userId: string | null
        }
        Insert: {
          cancelable?: boolean | null
          code: string
          createdAt?: string
          day: string
          details?: string | null
          id?: string
          leadId?: string | null
          packId?: string | null
          packProfileId?: number | null
          scheduleId: string
          serviceId: string
          status?: Database["public"]["Enums"]["booking_status"]
          teacherId?: string | null
          teacherScheduleId?: string | null
          userId?: string | null
        }
        Update: {
          cancelable?: boolean | null
          code?: string
          createdAt?: string
          day?: string
          details?: string | null
          id?: string
          leadId?: string | null
          packId?: string | null
          packProfileId?: number | null
          scheduleId?: string
          serviceId?: string
          status?: Database["public"]["Enums"]["booking_status"]
          teacherId?: string | null
          teacherScheduleId?: string | null
          userId?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "booking_leadId_fkey"
            columns: ["leadId"]
            isOneToOne: false
            referencedRelation: "lead"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "booking_packageId_fkey"
            columns: ["packId"]
            isOneToOne: false
            referencedRelation: "pack"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "booking_packProfileId_fkey"
            columns: ["packProfileId"]
            isOneToOne: false
            referencedRelation: "pack_profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "booking_scheduleId_fkey"
            columns: ["scheduleId"]
            isOneToOne: false
            referencedRelation: "schedule"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "booking_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "booking_teacherId_fkey"
            columns: ["teacherId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "booking_teacherScheduleId_fkey"
            columns: ["teacherScheduleId"]
            isOneToOne: false
            referencedRelation: "teacher_schedule"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "booking_userId_fkey"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
        ]
      }
      booking_equipment: {
        Row: {
          bookingId: string
          createdAt: string
          equipmentId: string
          id: string
        }
        Insert: {
          bookingId: string
          createdAt?: string
          equipmentId: string
          id?: string
        }
        Update: {
          bookingId?: string
          createdAt?: string
          equipmentId?: string
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "booking_equipment_bookingId_fkey"
            columns: ["bookingId"]
            isOneToOne: false
            referencedRelation: "booking"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "booking_equipment_equipmentId_fkey"
            columns: ["equipmentId"]
            isOneToOne: false
            referencedRelation: "equipment"
            referencedColumns: ["id"]
          },
        ]
      }
      cancelation_policy: {
        Row: {
          createdAt: string
          hour: number
          id: string
          packId: string | null
          percentage: number
          serviceId: string
        }
        Insert: {
          createdAt?: string
          hour: number
          id?: string
          packId?: string | null
          percentage?: number
          serviceId?: string
        }
        Update: {
          createdAt?: string
          hour?: number
          id?: string
          packId?: string | null
          percentage?: number
          serviceId?: string
        }
        Relationships: [
          {
            foreignKeyName: "cancelation_policy_packId_fkey"
            columns: ["packId"]
            isOneToOne: false
            referencedRelation: "pack"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cancelation_policy_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
        ]
      }
      category: {
        Row: {
          createdAt: string
          id: string
          name: string
          schoolId: string | null
        }
        Insert: {
          createdAt?: string
          id?: string
          name: string
          schoolId?: string | null
        }
        Update: {
          createdAt?: string
          id?: string
          name?: string
          schoolId?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "debt_category_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      contract: {
        Row: {
          createdAt: string
          end: string | null
          id: string
          originalUrl: string | null
          planId: string | null
          schoolId: string | null
          signed: boolean | null
          start: string | null
          studentId: string | null
          subscriptionId: string | null
        }
        Insert: {
          createdAt?: string
          end?: string | null
          id: string
          originalUrl?: string | null
          planId?: string | null
          schoolId?: string | null
          signed?: boolean | null
          start?: string | null
          studentId?: string | null
          subscriptionId?: string | null
        }
        Update: {
          createdAt?: string
          end?: string | null
          id?: string
          originalUrl?: string | null
          planId?: string | null
          schoolId?: string | null
          signed?: boolean | null
          start?: string | null
          studentId?: string | null
          subscriptionId?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "contract_planId_fkey"
            columns: ["planId"]
            isOneToOne: false
            referencedRelation: "plan"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contract_schoolid_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contract_studentId_fkey"
            columns: ["studentId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contract_subscriptionid_fkey"
            columns: ["subscriptionId"]
            isOneToOne: false
            referencedRelation: "subscription"
            referencedColumns: ["id"]
          },
        ]
      }
      course: {
        Row: {
          createdAt: string
          description: string | null
          featureImg: string | null
          id: string
          instructorId: string | null
          name: string
          price: number | null
          published: boolean
          schoolId: string | null
          slug: string
        }
        Insert: {
          createdAt?: string
          description?: string | null
          featureImg?: string | null
          id?: string
          instructorId?: string | null
          name: string
          price?: number | null
          published?: boolean
          schoolId?: string | null
          slug: string
        }
        Update: {
          createdAt?: string
          description?: string | null
          featureImg?: string | null
          id?: string
          instructorId?: string | null
          name?: string
          price?: number | null
          published?: boolean
          schoolId?: string | null
          slug?: string
        }
        Relationships: [
          {
            foreignKeyName: "course_instructorId_fkey"
            columns: ["instructorId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "course_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      debt: {
        Row: {
          billingDate: string | null
          createdAt: string
          debtRootId: string | null
          description: string | null
          id: string
          method: Database["public"]["Enums"]["payment_method"] | null
          name: string
          paid: boolean | null
          paidDate: string | null
          schoolId: string | null
          type: string | null
          value: number
        }
        Insert: {
          billingDate?: string | null
          createdAt?: string
          debtRootId?: string | null
          description?: string | null
          id?: string
          method?: Database["public"]["Enums"]["payment_method"] | null
          name: string
          paid?: boolean | null
          paidDate?: string | null
          schoolId?: string | null
          type?: string | null
          value: number
        }
        Update: {
          billingDate?: string | null
          createdAt?: string
          debtRootId?: string | null
          description?: string | null
          id?: string
          method?: Database["public"]["Enums"]["payment_method"] | null
          name?: string
          paid?: boolean | null
          paidDate?: string | null
          schoolId?: string | null
          type?: string | null
          value?: number
        }
        Relationships: [
          {
            foreignKeyName: "debt_debtRootId_fkey"
            columns: ["debtRootId"]
            isOneToOne: false
            referencedRelation: "debt_root"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "debt_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      debt_category: {
        Row: {
          categoryId: string | null
          createdAt: string
          debtRootId: string | null
          id: string
          schoolId: string | null
        }
        Insert: {
          categoryId?: string | null
          createdAt?: string
          debtRootId?: string | null
          id?: string
          schoolId?: string | null
        }
        Update: {
          categoryId?: string | null
          createdAt?: string
          debtRootId?: string | null
          id?: string
          schoolId?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "debt_category_categoryId_fkey"
            columns: ["categoryId"]
            isOneToOne: false
            referencedRelation: "category"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "debt_category_debtRootId_fkey"
            columns: ["debtRootId"]
            isOneToOne: false
            referencedRelation: "debt_root"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "debt_category_schoolId_fkey1"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      debt_root: {
        Row: {
          billingDate: string | null
          billingDay: string | null
          createdAt: string
          description: string | null
          fixed: boolean | null
          id: string
          method: Database["public"]["Enums"]["payment_method"] | null
          name: string | null
          paid: boolean | null
          repetitions: number | null
          schoolId: string | null
          type: string | null
          value: number | null
        }
        Insert: {
          billingDate?: string | null
          billingDay?: string | null
          createdAt?: string
          description?: string | null
          fixed?: boolean | null
          id?: string
          method?: Database["public"]["Enums"]["payment_method"] | null
          name?: string | null
          paid?: boolean | null
          repetitions?: number | null
          schoolId?: string | null
          type?: string | null
          value?: number | null
        }
        Update: {
          billingDate?: string | null
          billingDay?: string | null
          createdAt?: string
          description?: string | null
          fixed?: boolean | null
          id?: string
          method?: Database["public"]["Enums"]["payment_method"] | null
          name?: string | null
          paid?: boolean | null
          repetitions?: number | null
          schoolId?: string | null
          type?: string | null
          value?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "debt_root_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      equipment: {
        Row: {
          createdAt: string
          id: string
          name: string
          quantity: number
          schoolId: string
          status: Database["public"]["Enums"]["equipment_status"]
          type: string | null
        }
        Insert: {
          createdAt?: string
          id?: string
          name: string
          quantity: number
          schoolId: string
          status?: Database["public"]["Enums"]["equipment_status"]
          type?: string | null
        }
        Update: {
          createdAt?: string
          id?: string
          name?: string
          quantity?: number
          schoolId?: string
          status?: Database["public"]["Enums"]["equipment_status"]
          type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "equipment_school_fk"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      error: {
        Row: {
          createdAt: string
          id: string
          value: string
        }
        Insert: {
          createdAt?: string
          id?: string
          value: string
        }
        Update: {
          createdAt?: string
          id?: string
          value?: string
        }
        Relationships: []
      }
      face: {
        Row: {
          createdAt: string
          description: string[]
          email: string
          id: string
        }
        Insert: {
          createdAt?: string
          description: string[]
          email: string
          id?: string
        }
        Update: {
          createdAt?: string
          description?: string[]
          email?: string
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "face_email_fkey"
            columns: ["email"]
            isOneToOne: true
            referencedRelation: "profile"
            referencedColumns: ["email"]
          },
        ]
      }
      lead: {
        Row: {
          createdAt: string
          email: string | null
          id: string
          name: string
          phone: string | null
          schoolId: string
        }
        Insert: {
          createdAt?: string
          email?: string | null
          id?: string
          name: string
          phone?: string | null
          schoolId: string
        }
        Update: {
          createdAt?: string
          email?: string | null
          id?: string
          name?: string
          phone?: string | null
          schoolId?: string
        }
        Relationships: [
          {
            foreignKeyName: "lead_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      lesson: {
        Row: {
          courseId: string
          createdAt: string
          description: string
          id: string
          moduleId: string
          name: string
          sequence: number
          slug: string
          videoId: string | null
        }
        Insert: {
          courseId: string
          createdAt?: string
          description: string
          id?: string
          moduleId: string
          name: string
          sequence: number
          slug: string
          videoId?: string | null
        }
        Update: {
          courseId?: string
          createdAt?: string
          description?: string
          id?: string
          moduleId?: string
          name?: string
          sequence?: number
          slug?: string
          videoId?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "lesson_courseId_fkey"
            columns: ["courseId"]
            isOneToOne: false
            referencedRelation: "course"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "lesson_moduleId_fkey"
            columns: ["moduleId"]
            isOneToOne: false
            referencedRelation: "module"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "lesson_videoId_fkey"
            columns: ["videoId"]
            isOneToOne: false
            referencedRelation: "video"
            referencedColumns: ["id"]
          },
        ]
      }
      log: {
        Row: {
          createdAt: string
          id: number
          value: string
        }
        Insert: {
          createdAt?: string
          id?: number
          value: string
        }
        Update: {
          createdAt?: string
          id?: number
          value?: string
        }
        Relationships: []
      }
      module: {
        Row: {
          courseId: string
          createdAt: string
          description: string
          id: string
          name: string
        }
        Insert: {
          courseId: string
          createdAt?: string
          description: string
          id?: string
          name: string
        }
        Update: {
          courseId?: string
          createdAt?: string
          description?: string
          id?: string
          name?: string
        }
        Relationships: [
          {
            foreignKeyName: "module_courseId_fkey"
            columns: ["courseId"]
            isOneToOne: false
            referencedRelation: "course"
            referencedColumns: ["id"]
          },
        ]
      }
      pack: {
        Row: {
          active: boolean
          createdAt: string
          expires: number | null
          id: string
          installments: number | null
          name: string
          price: number
          schoolId: string
          use: number | null
        }
        Insert: {
          active?: boolean
          createdAt?: string
          expires?: number | null
          id?: string
          installments?: number | null
          name: string
          price: number
          schoolId?: string
          use?: number | null
        }
        Update: {
          active?: boolean
          createdAt?: string
          expires?: number | null
          id?: string
          installments?: number | null
          name?: string
          price?: number
          schoolId?: string
          use?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "pkg_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      pack_profile: {
        Row: {
          active: boolean
          createdAt: string
          expireDate: string | null
          id: number
          packId: string
          paymentId: string | null
          paymentStatus: Database["public"]["Enums"]["payment_status"] | null
          profileId: string
          purchaseStatus:
            | Database["public"]["Enums"]["pack_profile_status"]
            | null
          saleId: string | null
          used: number
        }
        Insert: {
          active?: boolean
          createdAt?: string
          expireDate?: string | null
          id?: number
          packId?: string
          paymentId?: string | null
          paymentStatus?: Database["public"]["Enums"]["payment_status"] | null
          profileId?: string
          purchaseStatus?:
            | Database["public"]["Enums"]["pack_profile_status"]
            | null
          saleId?: string | null
          used?: number
        }
        Update: {
          active?: boolean
          createdAt?: string
          expireDate?: string | null
          id?: number
          packId?: string
          paymentId?: string | null
          paymentStatus?: Database["public"]["Enums"]["payment_status"] | null
          profileId?: string
          purchaseStatus?:
            | Database["public"]["Enums"]["pack_profile_status"]
            | null
          saleId?: string | null
          used?: number
        }
        Relationships: [
          {
            foreignKeyName: "pack_profile_packId_fkey"
            columns: ["packId"]
            isOneToOne: false
            referencedRelation: "pack"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pack_profile_paymentId_fkey"
            columns: ["paymentId"]
            isOneToOne: false
            referencedRelation: "payment"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pack_profile_saleId_fkey"
            columns: ["saleId"]
            isOneToOne: false
            referencedRelation: "sale_root"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pkg_profile_profileId_fkey"
            columns: ["profileId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
        ]
      }
      payment: {
        Row: {
          createdAt: string
          dateCreated: string | null
          description: string | null
          dueDate: string | null
          externalReference: string | null
          id: string
          installmentNumber: number | null
          invoiceUrl: string | null
          netValue: number | null
          packId: string | null
          paymentDate: string | null
          paymentType: Database["public"]["Enums"]["payment_method"] | null
          planId: string | null
          profileAsId: string | null
          status: Database["public"]["Enums"]["payment_status"] | null
          subscriptionId: string | null
          value: number
        }
        Insert: {
          createdAt?: string
          dateCreated?: string | null
          description?: string | null
          dueDate?: string | null
          externalReference?: string | null
          id: string
          installmentNumber?: number | null
          invoiceUrl?: string | null
          netValue?: number | null
          packId?: string | null
          paymentDate?: string | null
          paymentType?: Database["public"]["Enums"]["payment_method"] | null
          planId?: string | null
          profileAsId?: string | null
          status?: Database["public"]["Enums"]["payment_status"] | null
          subscriptionId?: string | null
          value: number
        }
        Update: {
          createdAt?: string
          dateCreated?: string | null
          description?: string | null
          dueDate?: string | null
          externalReference?: string | null
          id?: string
          installmentNumber?: number | null
          invoiceUrl?: string | null
          netValue?: number | null
          packId?: string | null
          paymentDate?: string | null
          paymentType?: Database["public"]["Enums"]["payment_method"] | null
          planId?: string | null
          profileAsId?: string | null
          status?: Database["public"]["Enums"]["payment_status"] | null
          subscriptionId?: string | null
          value?: number
        }
        Relationships: [
          {
            foreignKeyName: "payment_packId_fkey"
            columns: ["packId"]
            isOneToOne: false
            referencedRelation: "pack"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payment_planId_fkey"
            columns: ["planId"]
            isOneToOne: false
            referencedRelation: "plan"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payment_profileAsId_fkey"
            columns: ["profileAsId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["asId"]
          },
        ]
      }
      plan: {
        Row: {
          active: boolean | null
          billingDay: number | null
          createdAt: string
          display: boolean
          expires: string | null
          fine: number | null
          fineType: Database["public"]["Enums"]["fine_type"] | null
          frequency: number
          frequencyType: Database["public"]["Enums"]["frequency_type"]
          id: string
          interest: number | null
          name: string
          price: number
          schoolId: string
        }
        Insert: {
          active?: boolean | null
          billingDay?: number | null
          createdAt?: string
          display?: boolean
          expires?: string | null
          fine?: number | null
          fineType?: Database["public"]["Enums"]["fine_type"] | null
          frequency: number
          frequencyType: Database["public"]["Enums"]["frequency_type"]
          id?: string
          interest?: number | null
          name: string
          price: number
          schoolId: string
        }
        Update: {
          active?: boolean | null
          billingDay?: number | null
          createdAt?: string
          display?: boolean
          expires?: string | null
          fine?: number | null
          fineType?: Database["public"]["Enums"]["fine_type"] | null
          frequency?: number
          frequencyType?: Database["public"]["Enums"]["frequency_type"]
          id?: string
          interest?: number | null
          name?: string
          price?: number
          schoolId?: string
        }
        Relationships: [
          {
            foreignKeyName: "plan_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      product: {
        Row: {
          active: boolean | null
          categoryId: string | null
          createdAt: string
          description: string | null
          id: string
          image: string | null
          name: string | null
          price: number | null
          schoolId: string | null
          sku: string | null
          sportId: string | null
          stock: boolean | null
        }
        Insert: {
          active?: boolean | null
          categoryId?: string | null
          createdAt?: string
          description?: string | null
          id?: string
          image?: string | null
          name?: string | null
          price?: number | null
          schoolId?: string | null
          sku?: string | null
          sportId?: string | null
          stock?: boolean | null
        }
        Update: {
          active?: boolean | null
          categoryId?: string | null
          createdAt?: string
          description?: string | null
          id?: string
          image?: string | null
          name?: string | null
          price?: number | null
          schoolId?: string | null
          sku?: string | null
          sportId?: string | null
          stock?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "product_categoryid_fkey"
            columns: ["categoryId"]
            isOneToOne: false
            referencedRelation: "category"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_schoolid_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_sportid_fkey"
            columns: ["sportId"]
            isOneToOne: false
            referencedRelation: "sport"
            referencedColumns: ["id"]
          },
        ]
      }
      product_variation: {
        Row: {
          color: Database["public"]["Enums"]["color_type"] | null
          id: number
          image: Json | null
          material: string | null
          price: number
          productId: string
          size: Database["public"]["Enums"]["size_type"] | null
          sku: string | null
          stock: number
        }
        Insert: {
          color?: Database["public"]["Enums"]["color_type"] | null
          id?: number
          image?: Json | null
          material?: string | null
          price: number
          productId: string
          size?: Database["public"]["Enums"]["size_type"] | null
          sku?: string | null
          stock: number
        }
        Update: {
          color?: Database["public"]["Enums"]["color_type"] | null
          id?: number
          image?: Json | null
          material?: string | null
          price?: number
          productId?: string
          size?: Database["public"]["Enums"]["size_type"] | null
          sku?: string | null
          stock?: number
        }
        Relationships: [
          {
            foreignKeyName: "product_variation_productid_fkey"
            columns: ["productId"]
            isOneToOne: false
            referencedRelation: "product"
            referencedColumns: ["id"]
          },
        ]
      }
      profile: {
        Row: {
          active: boolean
          asId: string | null
          birthdate: string | null
          cep: string | null
          city: string | null
          cpf: string
          createdAt: string
          email: string
          faceImage: string[] | null
          id: string
          name: string
          neighborhood: string | null
          phone: string | null
          profileImage: string | null
          schoolId: string
          state: string | null
          status: Database["public"]["Enums"]["status_type"]
          street: string | null
          type: Database["public"]["Enums"]["user_type"]
        }
        Insert: {
          active?: boolean
          asId?: string | null
          birthdate?: string | null
          cep?: string | null
          city?: string | null
          cpf?: string
          createdAt?: string
          email: string
          faceImage?: string[] | null
          id?: string
          name: string
          neighborhood?: string | null
          phone?: string | null
          profileImage?: string | null
          schoolId: string
          state?: string | null
          status?: Database["public"]["Enums"]["status_type"]
          street?: string | null
          type?: Database["public"]["Enums"]["user_type"]
        }
        Update: {
          active?: boolean
          asId?: string | null
          birthdate?: string | null
          cep?: string | null
          city?: string | null
          cpf?: string
          createdAt?: string
          email?: string
          faceImage?: string[] | null
          id?: string
          name?: string
          neighborhood?: string | null
          phone?: string | null
          profileImage?: string | null
          schoolId?: string
          state?: string | null
          status?: Database["public"]["Enums"]["status_type"]
          street?: string | null
          type?: Database["public"]["Enums"]["user_type"]
        }
        Relationships: [
          {
            foreignKeyName: "profile_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      progress: {
        Row: {
          courseId: string | null
          createdAt: string
          id: string
          lessonId: string | null
          lessonSequence: number | null
          status: Database["public"]["Enums"]["lesson_progress"] | null
          userId: string | null
        }
        Insert: {
          courseId?: string | null
          createdAt?: string
          id?: string
          lessonId?: string | null
          lessonSequence?: number | null
          status?: Database["public"]["Enums"]["lesson_progress"] | null
          userId?: string | null
        }
        Update: {
          courseId?: string | null
          createdAt?: string
          id?: string
          lessonId?: string | null
          lessonSequence?: number | null
          status?: Database["public"]["Enums"]["lesson_progress"] | null
          userId?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "progress_course_id_fkey"
            columns: ["courseId"]
            isOneToOne: false
            referencedRelation: "course"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "progress_lesson_id_fkey"
            columns: ["lessonId"]
            isOneToOne: false
            referencedRelation: "lesson"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "progress_user_id_fkey"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
        ]
      }
      rank: {
        Row: {
          baseRankId: string | null
          color: string | null
          createdAt: string
          id: string
          name: string
          nextRank: string | null
          sportId: string | null
          stripe: number | null
        }
        Insert: {
          baseRankId?: string | null
          color?: string | null
          createdAt?: string
          id?: string
          name: string
          nextRank?: string | null
          sportId?: string | null
          stripe?: number | null
        }
        Update: {
          baseRankId?: string | null
          color?: string | null
          createdAt?: string
          id?: string
          name?: string
          nextRank?: string | null
          sportId?: string | null
          stripe?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "rank_baseRankId_fkey"
            columns: ["baseRankId"]
            isOneToOne: false
            referencedRelation: "base_rank"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rank_nextRank_fkey"
            columns: ["nextRank"]
            isOneToOne: false
            referencedRelation: "rank"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rank_sportId_fkey"
            columns: ["sportId"]
            isOneToOne: false
            referencedRelation: "sport"
            referencedColumns: ["id"]
          },
        ]
      }
      rank_point: {
        Row: {
          createdAt: string
          id: string
          sportId: string | null
          userId: string
        }
        Insert: {
          createdAt?: string
          id?: string
          sportId?: string | null
          userId: string
        }
        Update: {
          createdAt?: string
          id?: string
          sportId?: string | null
          userId?: string
        }
        Relationships: [
          {
            foreignKeyName: "rank_point_sportId_fkey"
            columns: ["sportId"]
            isOneToOne: false
            referencedRelation: "sport"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rank_point_userId_fkey"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
        ]
      }
      rank_school: {
        Row: {
          classes: number
          createdAt: string
          id: string
          rankId: string
          schoolId: string
        }
        Insert: {
          classes?: number
          createdAt?: string
          id?: string
          rankId: string
          schoolId: string
        }
        Update: {
          classes?: number
          createdAt?: string
          id?: string
          rankId?: string
          schoolId?: string
        }
        Relationships: [
          {
            foreignKeyName: "rank_school_rankId_fkey"
            columns: ["rankId"]
            isOneToOne: false
            referencedRelation: "rank"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rank_school_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      rank_student: {
        Row: {
          createdAt: string
          id: string
          profileId: string
          rankId: string
          sportId: string | null
        }
        Insert: {
          createdAt?: string
          id?: string
          profileId: string
          rankId: string
          sportId?: string | null
        }
        Update: {
          createdAt?: string
          id?: string
          profileId?: string
          rankId?: string
          sportId?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "rank_student_profileId_fkey"
            columns: ["profileId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rank_student_rankId_fkey"
            columns: ["rankId"]
            isOneToOne: false
            referencedRelation: "rank"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rank_student_sportId_fkey"
            columns: ["sportId"]
            isOneToOne: false
            referencedRelation: "sport"
            referencedColumns: ["id"]
          },
        ]
      }
      sale: {
        Row: {
          billingDate: string
          clientId: string | null
          createdAt: string
          description: string | null
          id: string
          method: Database["public"]["Enums"]["payment_method"] | null
          name: string
          packId: string | null
          saleRootId: string
          schoolId: string
          value: number
        }
        Insert: {
          billingDate: string
          clientId?: string | null
          createdAt?: string
          description?: string | null
          id?: string
          method?: Database["public"]["Enums"]["payment_method"] | null
          name: string
          packId?: string | null
          saleRootId: string
          schoolId: string
          value: number
        }
        Update: {
          billingDate?: string
          clientId?: string | null
          createdAt?: string
          description?: string | null
          id?: string
          method?: Database["public"]["Enums"]["payment_method"] | null
          name?: string
          packId?: string | null
          saleRootId?: string
          schoolId?: string
          value?: number
        }
        Relationships: [
          {
            foreignKeyName: "sale_clientId_fkey"
            columns: ["clientId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sale_packId_fkey"
            columns: ["packId"]
            isOneToOne: false
            referencedRelation: "pack"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sale_saleRootId_fkey"
            columns: ["saleRootId"]
            isOneToOne: false
            referencedRelation: "sale_root"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sale_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      sale_root: {
        Row: {
          billingDate: string
          billingDay: number | null
          clientId: string | null
          createdAt: string
          description: string | null
          id: string
          installments: number | null
          method: Database["public"]["Enums"]["payment_method"] | null
          name: string
          packId: string | null
          schoolId: string
          value: number
        }
        Insert: {
          billingDate: string
          billingDay?: number | null
          clientId?: string | null
          createdAt?: string
          description?: string | null
          id?: string
          installments?: number | null
          method?: Database["public"]["Enums"]["payment_method"] | null
          name: string
          packId?: string | null
          schoolId: string
          value: number
        }
        Update: {
          billingDate?: string
          billingDay?: number | null
          clientId?: string | null
          createdAt?: string
          description?: string | null
          id?: string
          installments?: number | null
          method?: Database["public"]["Enums"]["payment_method"] | null
          name?: string
          packId?: string | null
          schoolId?: string
          value?: number
        }
        Relationships: [
          {
            foreignKeyName: "sale_root_clientId_fkey"
            columns: ["clientId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sale_root_packId_fkey"
            columns: ["packId"]
            isOneToOne: false
            referencedRelation: "pack"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sale_root_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      schedule: {
        Row: {
          active: boolean | null
          createdAt: string
          hour: string
          id: string
          limit: number | null
          number: number
          serviceId: string
        }
        Insert: {
          active?: boolean | null
          createdAt?: string
          hour: string
          id?: string
          limit?: number | null
          number: number
          serviceId: string
        }
        Update: {
          active?: boolean | null
          createdAt?: string
          hour?: string
          id?: string
          limit?: number | null
          number?: number
          serviceId?: string
        }
        Relationships: [
          {
            foreignKeyName: "schedule_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
        ]
      }
      school: {
        Row: {
          cnpj: string | null
          createdAt: string
          domain: string
          id: string
          name: string
        }
        Insert: {
          cnpj?: string | null
          createdAt?: string
          domain: string
          id?: string
          name: string
        }
        Update: {
          cnpj?: string | null
          createdAt?: string
          domain?: string
          id?: string
          name?: string
        }
        Relationships: []
      }
      school_settings: {
        Row: {
          chargedays: number[] | null
          closed: string[] | null
          closedWeekDays: number[] | null
          createdAt: string
          id: string
          schoolId: string
        }
        Insert: {
          chargedays?: number[] | null
          closed?: string[] | null
          closedWeekDays?: number[] | null
          createdAt?: string
          id?: string
          schoolId: string
        }
        Update: {
          chargedays?: number[] | null
          closed?: string[] | null
          closedWeekDays?: number[] | null
          createdAt?: string
          id?: string
          schoolId?: string
        }
        Relationships: [
          {
            foreignKeyName: "school_settings_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: true
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      service: {
        Row: {
          createdAt: string
          description: string | null
          duration: number
          id: string
          limit: number | null
          policy: number | null
          randomTeacher: boolean | null
          schoolId: string
          sportId: string | null
          title: string
        }
        Insert: {
          createdAt?: string
          description?: string | null
          duration?: number
          id?: string
          limit?: number | null
          policy?: number | null
          randomTeacher?: boolean | null
          schoolId: string
          sportId?: string | null
          title: string
        }
        Update: {
          createdAt?: string
          description?: string | null
          duration?: number
          id?: string
          limit?: number | null
          policy?: number | null
          randomTeacher?: boolean | null
          schoolId?: string
          sportId?: string | null
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "service_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "service_sportId_fkey"
            columns: ["sportId"]
            isOneToOne: false
            referencedRelation: "sport"
            referencedColumns: ["id"]
          },
        ]
      }
      service_equipment: {
        Row: {
          active: boolean
          createdAt: string
          equipmentId: string
          id: string
          serviceId: string
        }
        Insert: {
          active?: boolean
          createdAt?: string
          equipmentId: string
          id?: string
          serviceId: string
        }
        Update: {
          active?: boolean
          createdAt?: string
          equipmentId?: string
          id?: string
          serviceId?: string
        }
        Relationships: [
          {
            foreignKeyName: "service_equipment_equipmentId_fk"
            columns: ["equipmentId"]
            isOneToOne: false
            referencedRelation: "equipment"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "service_equipment_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
        ]
      }
      service_pack: {
        Row: {
          createdAt: string
          id: number
          packId: string
          serviceId: string
        }
        Insert: {
          createdAt?: string
          id?: number
          packId: string
          serviceId: string
        }
        Update: {
          createdAt?: string
          id?: number
          packId?: string
          serviceId?: string
        }
        Relationships: [
          {
            foreignKeyName: "pack_service_packId_fkey"
            columns: ["packId"]
            isOneToOne: false
            referencedRelation: "pack"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pack_service_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
        ]
      }
      service_plan: {
        Row: {
          createdAt: string
          id: number
          planId: string
          serviceId: string
        }
        Insert: {
          createdAt?: string
          id?: number
          planId: string
          serviceId: string
        }
        Update: {
          createdAt?: string
          id?: number
          planId?: string
          serviceId?: string
        }
        Relationships: [
          {
            foreignKeyName: "plan_service_planId_fkey"
            columns: ["planId"]
            isOneToOne: false
            referencedRelation: "plan"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "plan_service_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
        ]
      }
      service_student: {
        Row: {
          createdAt: string
          id: number
          profileId: string
          serviceId: string
        }
        Insert: {
          createdAt?: string
          id?: number
          profileId: string
          serviceId: string
        }
        Update: {
          createdAt?: string
          id?: number
          profileId?: string
          serviceId?: string
        }
        Relationships: [
          {
            foreignKeyName: "service_profile_profileId_fkey"
            columns: ["profileId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "service_profile_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
        ]
      }
      service_teacher: {
        Row: {
          createdAt: string
          id: string
          profileId: string
          serviceId: string
        }
        Insert: {
          createdAt?: string
          id?: string
          profileId: string
          serviceId: string
        }
        Update: {
          createdAt?: string
          id?: string
          profileId?: string
          serviceId?: string
        }
        Relationships: [
          {
            foreignKeyName: "service_instructor_profileId_fkey"
            columns: ["profileId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "service_instructor_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
        ]
      }
      sport: {
        Row: {
          createdAt: string
          id: string
          name: string
        }
        Insert: {
          createdAt?: string
          id?: string
          name: string
        }
        Update: {
          createdAt?: string
          id?: string
          name?: string
        }
        Relationships: []
      }
      sport_profile: {
        Row: {
          createdAt: string
          id: string
          profileId: string
          sportId: string
        }
        Insert: {
          createdAt?: string
          id?: string
          profileId: string
          sportId: string
        }
        Update: {
          createdAt?: string
          id?: string
          profileId?: string
          sportId?: string
        }
        Relationships: [
          {
            foreignKeyName: "sport_profile_profileId_fkey"
            columns: ["profileId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sport_profile_sportId_fkey"
            columns: ["sportId"]
            isOneToOne: false
            referencedRelation: "sport"
            referencedColumns: ["id"]
          },
        ]
      }
      sport_school: {
        Row: {
          createdAt: string
          id: string
          schoolId: string
          sportId: string
        }
        Insert: {
          createdAt?: string
          id?: string
          schoolId: string
          sportId: string
        }
        Update: {
          createdAt?: string
          id?: string
          schoolId?: string
          sportId?: string
        }
        Relationships: [
          {
            foreignKeyName: "sport_school_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sport_school_sportId_fkey"
            columns: ["sportId"]
            isOneToOne: false
            referencedRelation: "sport"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription: {
        Row: {
          billingDay: number | null
          billingType: Database["public"]["Enums"]["billing_type"] | null
          cardBrand: string | null
          cardNumber: string | null
          createdAt: string
          customer: string
          cycle: Database["public"]["Enums"]["cycle"]
          deleted: boolean | null
          description: string | null
          endDate: string | null
          fineValue: number | null
          id: string
          interestValue: number | null
          maxPayments: number | null
          nextDueDate: string | null
          object: string | null
          paymentLink: string | null
          paymentStatus: Database["public"]["Enums"]["payment_status"] | null
          planId: string | null
          profileId: string | null
          status: Database["public"]["Enums"]["status"]
          value: number
        }
        Insert: {
          billingDay?: number | null
          billingType?: Database["public"]["Enums"]["billing_type"] | null
          cardBrand?: string | null
          cardNumber?: string | null
          createdAt?: string
          customer: string
          cycle: Database["public"]["Enums"]["cycle"]
          deleted?: boolean | null
          description?: string | null
          endDate?: string | null
          fineValue?: number | null
          id: string
          interestValue?: number | null
          maxPayments?: number | null
          nextDueDate?: string | null
          object?: string | null
          paymentLink?: string | null
          paymentStatus?: Database["public"]["Enums"]["payment_status"] | null
          planId?: string | null
          profileId?: string | null
          status?: Database["public"]["Enums"]["status"]
          value: number
        }
        Update: {
          billingDay?: number | null
          billingType?: Database["public"]["Enums"]["billing_type"] | null
          cardBrand?: string | null
          cardNumber?: string | null
          createdAt?: string
          customer?: string
          cycle?: Database["public"]["Enums"]["cycle"]
          deleted?: boolean | null
          description?: string | null
          endDate?: string | null
          fineValue?: number | null
          id?: string
          interestValue?: number | null
          maxPayments?: number | null
          nextDueDate?: string | null
          object?: string | null
          paymentLink?: string | null
          paymentStatus?: Database["public"]["Enums"]["payment_status"] | null
          planId?: string | null
          profileId?: string | null
          status?: Database["public"]["Enums"]["status"]
          value?: number
        }
        Relationships: [
          {
            foreignKeyName: "subscription_asaas_planId_fkey"
            columns: ["planId"]
            isOneToOne: false
            referencedRelation: "plan"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_asaas_profileId_fkey"
            columns: ["profileId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
        ]
      }
      teacher_schedule: {
        Row: {
          createdAt: string
          day: string
          id: string
          profileId: string
          scheduleId: string
          serviceId: string
        }
        Insert: {
          createdAt?: string
          day: string
          id?: string
          profileId: string
          scheduleId: string
          serviceId: string
        }
        Update: {
          createdAt?: string
          day?: string
          id?: string
          profileId?: string
          scheduleId?: string
          serviceId?: string
        }
        Relationships: [
          {
            foreignKeyName: "teacher_schedule_profileId_fkey"
            columns: ["profileId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teacher_schedule_scheduleId_fkey"
            columns: ["scheduleId"]
            isOneToOne: false
            referencedRelation: "schedule"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teacher_schedule_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
        ]
      }
      user_pwa: {
        Row: {
          createdAt: string
          deviceId: string | null
          id: string
          profileId: string | null
          schoolId: string | null
          sub: Json | null
        }
        Insert: {
          createdAt?: string
          deviceId?: string | null
          id?: string
          profileId?: string | null
          schoolId?: string | null
          sub?: Json | null
        }
        Update: {
          createdAt?: string
          deviceId?: string | null
          id?: string
          profileId?: string | null
          schoolId?: string | null
          sub?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "user_pwa_profileId_fkey"
            columns: ["profileId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_pwa_schoolId_fkey"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
      video: {
        Row: {
          createdAt: string
          duration: number | null
          id: string
          name: string | null
          publicPlaybackId: string | null
          schoolId: string | null
          videoId: string | null
        }
        Insert: {
          createdAt?: string
          duration?: number | null
          id?: string
          name?: string | null
          publicPlaybackId?: string | null
          schoolId?: string | null
          videoId?: string | null
        }
        Update: {
          createdAt?: string
          duration?: number | null
          id?: string
          name?: string | null
          publicPlaybackId?: string | null
          schoolId?: string | null
          videoId?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "video_schoolId_fkey1"
            columns: ["schoolId"]
            isOneToOne: false
            referencedRelation: "school"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      attendance_count: {
        Row: {
          count: number | null
          month: number | null
          serviceId: string | null
          year: number | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance_monthly: {
        Row: {
          month: number | null
          name: string | null
          profileImage: string | null
          serviceId: string | null
          total: number | null
          userId: string | null
          year: number | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_userId_fkey"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance_weekly: {
        Row: {
          name: string | null
          profileImage: string | null
          serviceId: string | null
          total: number | null
          userId: string | null
          week: number | null
          year: number | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_userId_fkey"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance_yearly: {
        Row: {
          name: string | null
          profileImage: string | null
          serviceId: string | null
          total: number | null
          userId: string | null
          year: number | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_serviceId_fkey"
            columns: ["serviceId"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_userId_fkey"
            columns: ["userId"]
            isOneToOne: false
            referencedRelation: "profile"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      create_teacher_schedule_next_month: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      friends_attendance: {
        Args: {
          service: string
          exclude: string
          schedule: string
          day: string
        }
        Returns: {
          id: string
          name: string
          profileImage: string
        }[]
      }
      get_all_bookings:
        | {
            Args: Record<PropertyKey, never>
            Returns: {
              id: string
              code: string
              day: string
              status: Database["public"]["Enums"]["booking_status"]
              equipment: Json
              service: Json
              schedule: Json
              lead: Json
              profile: Json
            }[]
          }
        | {
            Args: {
              school: string
            }
            Returns: {
              id: string
              code: string
              day: string
              status: Database["public"]["Enums"]["booking_status"]
              equipment: Json
              service: Json
              schedule: Json
              lead: Json
              profile: Json
            }[]
          }
      get_attendance_by_date:
        | {
            Args: Record<PropertyKey, never>
            Returns: Json
          }
        | {
            Args: {
              service: string
            }
            Returns: Json
          }
      get_attendance_by_date_and_service: {
        Args: {
          service: string
        }
        Returns: Json
      }
      get_available_schedules:
        | {
            Args: Record<PropertyKey, never>
            Returns: {
              id: string
              schedule: Json
              profile: Json
              day: string
              service: Json
              limit: number
            }[]
          }
        | {
            Args: {
              selectedDay: string
              selectedServiceId: string
            }
            Returns: {
              id: string
              schedule: Json
              profile: Json
              day: string
            }[]
          }
      get_recent_attendances: {
        Args: {
          service: string
        }
        Returns: Json
      }
      get_unique_teacher_schedules: {
        Args: {
          service: string
          startDate: string
          endDate: string
        }
        Returns: {
          profileId: string
          profileName: string
          scheduleId: string
          scheduleHour: string
          day: string
        }[]
      }
      get_unique_teacher_schedules_by_service: {
        Args: {
          service: string
        }
        Returns: {
          day: string
          service_id: string
          schedule_id: string
          profile_id: string
          schedule: unknown
        }[]
      }
      get_upcoming_birthdays:
        | {
            Args: Record<PropertyKey, never>
            Returns: {
              id: string
              name: string
              email: string
              birthdate: string
            }[]
          }
        | {
            Args: {
              school: string
            }
            Returns: {
              id: string
              name: string
              email: string
              birthdate: string
            }[]
          }
      get_user_info: {
        Args: {
          userid: string
          sportid: string
        }
        Returns: Json
      }
      insert_teacher_schedules: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      mark_expired_bookings: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      verify_pack_expiration: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      app_role: "admin" | "student" | "master"
      billing_type: "UNDEFINED" | "BOLETO" | "CREDIT_CARD" | "PIX"
      booking_status:
        | "pending"
        | "approved"
        | "canceled"
        | "expired"
        | "used"
        | "missed"
        | "bailed"
      color_type:
        | "red"
        | "blue"
        | "black"
        | "white"
        | "green"
        | "multicolor"
        | "custom_color"
      cycle:
        | "WEEKLY"
        | "BIWEEKLY"
        | "MONTHLY"
        | "BIMONTHLY"
        | "QUARTERLY"
        | "SEMIANNUALLY"
        | "YEARLY"
      equipment_status: "available" | "unavailable"
      fine_type: "FIXED" | "PERCENTAGE"
      frequency_type: "months" | "days" | "years"
      lesson_progress: "complete" | "in_progress" | "not_started"
      pack_profile_status: "active" | "used" | "expired" | "pending"
      payment_method: "CREDIT_CARD" | "PIX" | "BOLETO"
      payment_status: "pending" | "confirmed" | "overdue" | "received"
      semaphore: "green" | "yellow" | "red" | "blank"
      size_type: "s" | "m" | "l" | "xl" | "30x32" | "custom_size"
      sports: "jiu-jitsu" | "muay-thai" | "karate" | "judo" | "surf"
      status: "ACTIVE" | "EXPIRED" | "INACTIVE"
      status_type: "pending" | "active" | "inactive"
      subscription_periodicity: "monthly"
      subscription_status: "authorized" | "pending" | "paused"
      subscription_type: "free" | "payed"
      user_type: "admin" | "student" | "teacher" | "master"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
