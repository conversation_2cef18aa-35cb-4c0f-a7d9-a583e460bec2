{"name": "meu-mestre", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.1", "@fortawesome/free-solid-svg-icons": "^6.7.1", "@fortawesome/react-fontawesome": "^0.2.2", "@hookform/resolvers": "^3.9.0", "@mux/mux-node": "^9.0.1", "@mux/mux-player-react": "^2.9.0", "@mux/mux-uploader-react": "^1.1.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.2.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "@tanstack/react-table": "^8.20.5", "@tensorflow/tfjs-node": "^4.21.0", "@vercel/speed-insights": "^1.1.0", "axios": "^1.7.2", "canvas": "^2.11.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "debounce": "^2.1.1", "encoding": "^0.1.13", "face-api.js": "^0.22.2", "fastify-multipart": "^5.3.1", "framer-motion": "^12.4.3", "html2canvas": "^1.4.1", "jose": "^5.9.4", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.416.0", "multer": "^1.4.5-lts.1", "next": "14.2.5", "next-themes": "^0.4.6", "qrcode": "^1.5.4", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.52.2", "react-to-pdf": "^1.0.1", "recharts": "^2.14.1", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "typedarray-to-buffer": "^4.0.0", "uuid": "^11.0.3", "vaul": "^1.1.2", "web-push": "^3.6.7", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/typedarray-to-buffer": "^4.0.4", "eslint": "^8", "eslint-config-next": "14.2.5", "eslint-config-recommended": "^4.1.0", "postcss": "^8", "supabase": "^2.34.3", "tailwindcss": "^3.4.1", "typescript": "^5"}}